# 个人IB身份新增实施方案

## 需求分析

### 业务需求
- 在现有用户身份体系中新增"个人IB身份"(PERSONAL_IB)
- 保持现有四级等级体系：普通(NORMAL)、青铜(BRONZE)、白银(SILVER)、黄金(GOLD)
- 各等级实盘账户资金门槛：
  - 普通：≥ $20,000
  - 青铜：≥ $50,000  
  - 白银：≥ $100,000
  - 黄金：≥ $200,000

### 技术影响分析
- **Proto定义**：需要在UserIdentity枚举中新增PERSONAL_IB = 5
- **数据库**：需要新增身份等级配置和任务数据
- **业务逻辑**：需要扩展身份切换和任务处理逻辑
- **API接口**：现有接口保持兼容，自动支持新身份类型

## 技术实施方案

### 1. Proto文件修改

#### 1.1 修改UserIdentity枚举
**文件**: `wiki_protos/user_growth_center/v1/growth_center.proto`

```protobuf
// 用户身份
enum UserIdentity {
  UNKNOWN=0; // 未知
  INVESTOR=1; // 投资者
  KOL=2; // KOL
  SERVICE_PROVIDER=3; // 服务商
  TRADER=4; // 交易商
  PERSONAL_IB=5; // 个人IB
}
```

#### 1.2 新增个人IB任务枚举
**文件**: `wiki_protos/user_growth_center/v1/growth_center.proto`

```protobuf
enum TaskEnum {
  CHOOSE_PERSONAL_IB=500; // 选择个人IB身份
  VERIFY_IB_ACCOUNT_ASSETS_1 =501; // 实盘账户资金不小于$20000
  VERIFY_IB_ACCOUNT_ASSETS_2 =502; // 实盘账户资金不小于$50000
  VERIFY_IB_ACCOUNT_ASSETS_3=503; // 实盘账户资金不小于$100000
  VERIFY_IB_ACCOUNT_ASSETS_4 =504; // 实盘账户资金不小于$200000
}
```

### 2. 代码修改清单

#### 2.1 Service层修改
**文件**: `internal/service/growth_center.go`

**修改点1**: 身份切换消息处理
```go
// 在ReceiveUserIdentityMsg函数中新增个人IB身份处理
case "personal_ib":
    identity = pb.UserIdentity_PERSONAL_IB
```

**修改点2**: 新增个人IB任务处理器
```go
// 新增getPersonalIBGrowthRule函数
func (s *Service) getPersonalIBGrowthRule(in *pb.PersonalIBTaskMsg) TaskScheduler {
    return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
        var (
            isFinished bool
            schedule   float64
        )
        
        if t.Task == pb.TaskEnum_VERIFY_IB_ACCOUNT_ASSETS {
            if in.AccountAssets >= float32(t.Target) {
                schedule = float64(t.Target)
                isFinished = true
            } else {
                schedule = float64(in.AccountAssets)
                isFinished = false
            }
        }
        //任务完成判断 不同金额
     
    }
}
```

#### 2.2 消息结构扩展
**文件**: `wiki_protos/user_growth_center/v1/growth_center.proto`

```protobuf
// 新增个人IB任务消息，具体可以学习其他消息
message PersonalIBTaskMsg {
  string user_id=1 [json_name="user_id"];
  float account_assets=2 [json_name="account_assets"]; // 账户资产
}

// 扩展UserGrowthMsg
message UserGrowthMsg {
    GrowthMsgType type =1;
    UserIdentityMsg identity=2;
    InvestorTaskMsg investor=3;
    KolTaskMsg kol =4;
    PersonalIBTaskMsg personal_ib=5; // 新增个人IB消息
}

// 扩展GrowthMsgType枚举
enum GrowthMsgType {
  UNKNOWN_TYPE=0;
  SWITCH_IDENTITY_TYPE=1;
  INVESTOR_TYPE=2;
  KOL_TYPE=3;
  PERSONAL_IB_TYPE=4; // 新增个人IB类型
}
```

### 3. 数据库变更

#### 3.1 身份等级配置数据
**表**: `growthcenter_identity_grade`

需要插入4条记录（个人IB的4个等级）：

```sql
-- 个人IB普通等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator) 
VALUES ('PIB_NORMAL_001', 5, 1, 1, 1, 'personal_ib_normal_banner.png', 'personal_ib_normal_badge.png', 'personal_ib_normal_user_badge.png', 'personal_ib_normal_share_badge.png', NOW(), 'system');

-- 个人IB青铜等级  
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('PIB_BRONZE_001', 5, 2, 1, 1, 'personal_ib_bronze_banner.png', 'personal_ib_bronze_badge.png', 'personal_ib_bronze_user_badge.png', 'personal_ib_bronze_share_badge.png', NOW(), 'system');

-- 个人IB白银等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('PIB_SILVER_001', 5, 3, 1, 1, 'personal_ib_silver_banner.png', 'personal_ib_silver_badge.png', 'personal_ib_silver_user_badge.png', 'personal_ib_silver_share_badge.png', NOW(), 'system');

-- 个人IB黄金等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('PIB_GOLD_001', 5, 4, 1, 1, 'personal_ib_gold_banner.png', 'personal_ib_gold_badge.png', 'personal_ib_gold_user_badge.png', 'personal_ib_gold_share_badge.png', NOW(), 'system');
```

#### 3.2 等级任务配置数据
**表**: `growthcenter_grade_task`

```sql
-- 选择个人IB身份任务
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_001', 1, 500, '选择个人IB身份', '成为个人IB，开启专业投资之路', 0, '', 260, 1, NOW(), 'system', '选择个人IB身份，享受专业投资服务');

-- 账户资产验证任务（普通级）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_002', 2, 501, '账户资产验证', '验证实盘账户资产≥$20,000', 20000, '$', 200, 1, NOW(), 'system', '验证您的实盘账户资产达到$20,000');

-- 账户资产验证任务（青铜级）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_003', 2, 501, '账户资产验证', '验证实盘账户资产≥$50,000', 50000, '$', 200, 1, NOW(), 'system', '验证您的实盘账户资产达到$50,000');

-- 账户资产验证任务（白银级）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_004', 2, 501, '账户资产验证', '验证实盘账户资产≥$100,000', 100000, '$', 200, 1, NOW(), 'system', '验证您的实盘账户资产达到$100,000');

-- IB实名认证任务
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_005', 1, 502, 'IB实名认证', '完成个人IB身份实名认证', 0, '', 260, 1, NOW(), 'system', '完成个人IB身份的实名认证流程');

-- IB合规培训任务
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_006', 1, 503, 'IB合规培训', '完成IB合规培训课程', 0, '', 260, 1, NOW(), 'system', '学习并完成IB相关的合规培训课程');
```

#### 3.3 身份任务关联配置
**表**: `growthcenter_identity_task_association`

```sql
-- 个人IB普通等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES 
('PIB_ASSOC_001', 'PIB_NORMAL_001', 'PIB_TASK_001', 1, NOW()),
('PIB_ASSOC_002', 'PIB_NORMAL_001', 'PIB_TASK_002', 2, NOW()),
('PIB_ASSOC_003', 'PIB_NORMAL_001', 'PIB_TASK_005', 3, NOW());

-- 个人IB青铜等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES 
('PIB_ASSOC_004', 'PIB_BRONZE_001', 'PIB_TASK_003', 1, NOW()),
('PIB_ASSOC_005', 'PIB_BRONZE_001', 'PIB_TASK_006', 2, NOW());

-- 个人IB白银等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES 
('PIB_ASSOC_006', 'PIB_SILVER_001', 'PIB_TASK_004', 1, NOW());

-- 个人IB黄金等级暂无额外任务 2000


```

### 4. 向后兼容性考虑

#### 4.1 API兼容性
- 现有API接口无需修改，自动支持新的身份类型
- 客户端需要更新以识别新的PERSONAL_IB身份类型
- 老版本客户端会将PERSONAL_IB显示为UNKNOWN，不会崩溃

#### 4.2 数据兼容性
- 现有用户数据不受影响
- 新增的枚举值不会影响现有身份的处理逻辑
- 数据库表结构无需修改，仅新增配置数据

#### 4.3 消息兼容性
- Kafka消息处理向后兼容
- 新增的消息类型不会影响现有消息处理
- 老版本服务可以忽略未知的身份类型消息

## 实施步骤

### 第一阶段：Proto和代码修改
1. 修改proto文件，新增PERSONAL_IB身份和相关任务枚举
2. 重新生成proto代码：`make api`
3. 修改Service层代码，新增个人IB身份处理逻辑
4. 更新消息处理逻辑

### 第二阶段：数据库配置
1. 执行SQL脚本，新增身份等级配置
2. 新增等级任务配置
3. 配置身份任务关联关系
4. 验证数据完整性
## 测试建议


### 集成测试(暂不实施 缺乏数据源头)
1. 测试用户切换到个人IB身份
2. 测试个人IB用户的等级升级流程
3. 测试资产验证任务的完成逻辑
4. 测试API接口返回的个人IB身份信息

### API测试用例
```bash
# 获取个人IB身份规则
GET /v1/app/identity/rule?user_id=test_user

# 获取个人IB用户成长详情
GET /v1/app/identity/detail?user_id=test_user

# 个人IB身份切换
POST /v1/app/identity/switch
{
  "user_id": "test_user",
  "identity": "personal_ib"
}
```

## 详细代码修改清单

### 5. 具体文件修改详情

#### 5.1 Proto文件修改
**文件路径**: `wiki_protos/user_growth_center/v1/growth_center.proto`

**修改内容**:
- 第168-173行：在UserIdentity枚举中新增PERSONAL_IB=5
- 第197-216行：在TaskEnum枚举中新增个人IB相关任务(500-503)
- 第160-165行：在GrowthMsgType枚举中新增PERSONAL_IB_TYPE=4
- 新增PersonalIBTaskMsg消息结构
- 在UserGrowthMsg中新增personal_ib字段

#### 5.2 Service层修改
**文件路径**: `internal/service/growth_center.go`

**修改点**:
1. **第1343-1354行**: 在ReceiveUserIdentityMsg函数的switch语句中新增personal_ib case
2. **第1680行后**: 新增getPersonalIBGrowthRule函数
3. **第994行**: 在HandleIdentityGradeUpgrade函数中新增个人IB身份处理
4. **第367行**: 在PostUserIdentitySwitch函数中新增个人IB身份支持

#### 5.3 消息处理扩展
**文件路径**: `internal/service/growth_center.go`

**新增函数**:
```go
// ReceivePersonalIBTaskMsg 处理个人IB任务消息
func (s *Service) ReceivePersonalIBTaskMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.PersonalIBTaskMsg) error {
    if strings.TrimSpace(in.UserId) == "" {
        return errors.New("UserId cannot be empty or null")
    }

    return s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_PERSONAL_IB, s.getPersonalIBGrowthRule(in))
}
```

### 6. 配置文件修改

#### 6.1 Kafka主题配置
**文件路径**: `configs/config.yaml`

**新增配置**:
```yaml
business:
  personal_ib_task_topic: user_growth_tasks_personal_ib  # 个人IB任务主题
```

#### 6.2 服务器配置
**文件路径**: `internal/server/kafka.go`

**修改内容**: 新增个人IB消息订阅
```go
// 新增个人IB任务消息订阅
server.RegisterSubscriber(
    c.PersonalIbTaskTopic,
    c.ConsumeGroup,
    false,
    svc.ReceivePersonalIBTaskMsg,
)
```

### 7. 数据访问层扩展

#### 7.1 DAO层无需修改
现有的DAO层代码已经足够通用，支持任意身份类型的查询和操作，无需针对个人IB身份进行特殊修改。

#### 7.2 模型层兼容性
现有的模型结构完全兼容新的个人IB身份类型，因为：
- UserIdentity枚举支持任意整数值
- 数据库表结构使用整数存储身份类型
- 业务逻辑基于枚举值进行处理

### 8. 国际化支持

#### 8.1 多语言文案
需要为个人IB身份相关的文案添加多语言支持：

**中文文案**:
- "个人IB身份"
- "账户资产验证"
- "IB实名认证"
- "IB合规培训"
- "验证实盘账户资产"

**英文文案**:
- "Personal IB Identity"
- "Account Asset Verification"
- "IB Real Name Verification"
- "IB Compliance Training"
- "Verify Real Account Assets"

