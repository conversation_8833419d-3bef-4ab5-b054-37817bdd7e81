# 用户成长中心数据库存储结构分析

## 概述

用户成长中心采用关系型数据库设计，通过7个核心数据表实现用户身份管理、等级升级、任务系统等功能。本文档详细分析数据库的存储结构、表关联关系和数据组织方式。

## 1. 等级表达方式

### 1.1 身份枚举 (UserIdentity)
```go
enum UserIdentity {
  UNKNOWN=0;          // 未知
  INVESTOR=1;         // 投资者
  KOL=2;              // KOL
  SERVICE_PROVIDER=3; // 服务商
  TRADER=4;           // 交易商
  PERSONAL_IB=5;      // 个人IB
}
```

### 1.2 等级枚举 (UserGrade)
```go
enum UserGrade {
  UNDEFINED=0;  // 未定义
  NORMAL=1;     // 普通
  BRONZE=2;     // 青铜
  SILVER=3;     // 白银
  GOLD=4;       // 黄金
}
```

### 1.3 等级存储方式
- **数据类型**: 整型 (int32)
- **取值范围**: 0-5 (身份), 0-4 (等级)
- **组合方式**: 身份+等级形成唯一的身份等级组合
- **示例**: 个人IB(5) + 青铜(2) = 个人IB青铜等级

## 2. 核心数据表结构

### 2.1 身份等级配置表 (growthcenter_identity_grade)

**表名**: `growthcenter_identity_grade`  
**作用**: 定义所有身份和等级的组合配置

```go
type IdentityGrade struct {
    ID         string               `gorm:"column:Id"`          // 主键 (如: PIB_NORMAL_001)
    Identity   v1.UserIdentity      `gorm:"column:Identity"`    // 身份枚举值 (1-5)
    Grade      v1.UserGrade         `gorm:"column:Grade"`       // 等级枚举值 (1-4)
    IsEnable   bool                 `gorm:"column:Is_Enable"`   // 是否启用
    Mode       v1.IdentityGradeMode `gorm:"column:Mode"`        // 任务完成模式 (1=全部完成, 2=任意完成)
    Banner     string               `gorm:"column:Banner"`      // 等级横幅图
    Badge      string               `gorm:"column:Badge"`       // 徽章图片
    UserBadge  string               `gorm:"column:User_Badge"`  // 用户中心徽章
    ShareBadge string               `gorm:"column:Share_Badge"` // 分享徽章
    CreateTime time.Time            `gorm:"column:Create_Time"` // 创建时间
    Creator    string               `gorm:"column:Creator"`     // 创建人
}
```

**关键特性**:
- 主键采用业务含义的字符串 (如: PIB_NORMAL_001)
- Identity + Grade 组合唯一标识一个身份等级
- Mode 字段控制该等级的任务完成策略

### 2.2 用户身份表 (growthcenter_user_identity)

**表名**: `growthcenter_user_identity`  
**作用**: 存储用户当前的身份等级状态

```go
type UserIdentity struct {
    ID              string    `gorm:"column:Id;primary_key"`         // 主键 (如: UI1703123456789012)
    UserId          string    `gorm:"column:User_Id"`                // 用户ID
    IdentityGradeId string    `gorm:"column:Identity_Grade_Id"`      // 身份等级ID (外键)
    Status          int       `gorm:"column:Status"`                 // 状态 (0=无效, 1=生效)
    AcquireTime     time.Time `gorm:"column:Acquire_Time"`           // 身份获得时间
    CreateTime      time.Time `gorm:"column:Create_Time"`            // 创建时间
    UpdateTime      time.Time `gorm:"column:Update_Time"`            // 更新时间
}
```

**关键特性**:
- 每个用户只有一条有效记录 (Status=1)
- IdentityGradeId 外键关联到 growthcenter_identity_grade.Id
- 主键采用时间戳生成: UI + UnixMicro

### 2.3 等级任务表 (growthcenter_grade_task)

**表名**: `growthcenter_grade_task`  
**作用**: 定义所有任务的配置信息

```go
type GradeTask struct {
    ID          string                   `gorm:"column:Id"`           // 主键 (如: PIB_TASK_001)
    Type        int32                    `gorm:"column:Type"`         // 任务类型 (1=布尔型, 2=数值型)
    Task        v1.TaskEnum              `gorm:"column:Task"`         // 任务枚举 (500-504)
    Name        string                   `gorm:"column:Name"`         // 任务名称
    Content     string                   `gorm:"column:Content"`      // 任务描述
    Target      int64                    `gorm:"column:Target"`       // 目标值 (数值型任务)
    Unit        string                   `gorm:"column:Unit"`         // 单位 (如: $)
    JumpAddress v1.UserGrowthJumpAddress `gorm:"column:Jump_Address"` // 跳转地址
    IsEnable    bool                     `gorm:"column:Is_Enable"`    // 是否启用
    Label       string                   `gorm:"column:label"`        // 详细描述
    CreateTime  time.Time                `gorm:"column:Create_Time"`  // 创建时间
}
```

**任务类型说明**:
- **Type=1 (布尔型)**: 完成/未完成状态，如"选择个人IB身份"
- **Type=2 (数值型)**: 需要达到特定数值，如"账户资产≥$20,000"

### 2.4 身份任务关联表 (growthcenter_identity_task_association)

**表名**: `growthcenter_identity_task_association`  
**作用**: 关联身份等级与任务的多对多关系

```go
type IdentityTaskAssociation struct {
    ID              string    `gorm:"column:Id"`                   // 主键
    IdentityGradeId string    `gorm:"column:Identity_Grade_Id"`    // 身份等级ID (外键)
    GradeTaskId     string    `gorm:"column:Grade_Task_Id"`        // 任务ID (外键)
    Order           int64     `gorm:"column:Order"`                // 排序号
    CreateTime      time.Time `gorm:"column:Create_Time"`          // 创建时间
}
```

**关键特性**:
- 实现身份等级与任务的多对多关联
- Order 字段控制任务在界面上的显示顺序
- 一个身份等级可以关联多个任务
- 一个任务可以被多个身份等级使用

### 2.5 用户任务进度表 (growthcenter_user_identity_task_schedule)

**表名**: `growthcenter_user_identity_task_schedule`  
**作用**: 记录用户在各个任务上的完成进度

```go
type UserIdentityTaskSchedule struct {
    ID              string          `gorm:"column:Id"`                   // 主键
    UserId          string          `gorm:"column:User_Id"`              // 用户ID
    Identity        v1.UserIdentity `gorm:"column:Identity"`             // 身份枚举
    Grade           v1.UserGrade    `gorm:"column:Grade"`                // 等级枚举
    IdentityGradeId string          `gorm:"column:Identity_Grade_Id"`    // 身份等级ID
    GradeTaskId     string          `gorm:"column:Grade_Task_Id"`        // 任务ID (外键)
    Schedule        float64         `gorm:"column:Schedule"`             // 当前进度值
    IsFinish        bool            `gorm:"column:Is_Finish"`            // 是否完成
    CreateTime      time.Time       `gorm:"column:Create_Time"`          // 创建时间
    UpdateTime      time.Time       `gorm:"column:Update_Time"`          // 更新时间
}
```

**进度记录方式**:
- **布尔型任务**: Schedule=0(未完成) 或 1(完成)
- **数值型任务**: Schedule=实际进度值，如资产金额

### 2.6 用户身份历史表 (growthcenter_user_identity_history)

**表名**: `growthcenter_user_identity_history`  
**作用**: 记录用户身份等级的升级历史

```go
type UserIdentityHistory struct {
    ID              string          `gorm:"column:Id"`                   // 主键
    UserId          string          `gorm:"column:User_Id"`              // 用户ID
    Identity        v1.UserIdentity `gorm:"column:Identity"`             // 身份枚举
    Grade           v1.UserGrade    `gorm:"column:Grade"`                // 等级枚举
    IdentityGradeId string          `gorm:"column:Identity_Grade_Id"`    // 身份等级ID
    CreateTime      time.Time       `gorm:"column:Create_Time"`          // 升级时间
}
```

**历史记录特性**:
- 每次等级升级都会新增一条记录
- 按时间顺序记录用户的成长轨迹
- 支持身份切换和等级升级的完整历史

### 2.7 用户关联表 (growthcenter_user_relation)

**表名**: `growthcenter_user_relation`  
**作用**: 管理用户与个人/企业的关联关系

```go
type UserRelation struct {
    ID         string     `gorm:"column:Id"`           // 主键
    UserId     string     `gorm:"column:User_Id"`      // 用户ID
    ObjectCode string     `gorm:"column:Object_Code"`  // 对象代码 (用户ID或企业代码)
    ObjectType int        `gorm:"column:Object_Type"`  // 对象类型 (1=个人, 2=企业)
    Status     int        `gorm:"Status"`              // 状态 (0=未关联, 1=关联)
    CreateTime time.Time  `gorm:"column:Create_Time"`  // 创建时间
    UpdateTime *time.Time `gorm:"column:Update_Time"`  // 更新时间
}
```

**关联关系说明**:
- **个人身份**: ObjectType=1, ObjectCode=UserId
- **企业身份**: ObjectType=2, ObjectCode=企业代码

## 3. 表关联关系图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           用户成长中心数据库关系图                                │
└─────────────────────────────────────────────────────────────────────────────────┘

┌──────────────────────────┐    1:N    ┌─────────────────────────────────┐
│  growthcenter_identity_  │◄──────────│ growthcenter_identity_task_     │
│  grade                   │           │ association                     │
│                          │           │                                 │
│ • Id (PK)               │           │ • Id (PK)                      │
│ • Identity              │           │ • Identity_Grade_Id (FK)       │
│ • Grade                 │           │ • Grade_Task_Id (FK)           │
│ • Mode                  │           │ • Order                        │
│ • Badge                 │           │                                 │
└──────────────────────────┘           └─────────────────────────────────┘
            │                                           │
            │ 1:1                                      │ N:1
            ▼                                           ▼
┌──────────────────────────┐           ┌─────────────────────────────────┐
│  growthcenter_user_      │           │ growthcenter_grade_task         │
│  identity                │           │                                 │
│                          │           │ • Id (PK)                      │
│ • Id (PK)               │           │ • Task (TaskEnum)              │
│ • User_Id               │           │ • Type (1=Bool, 2=Number)      │
│ • Identity_Grade_Id (FK)│           │ • Target                       │
│ • Status                │           │ • Name                         │
│ • Acquire_Time          │           │                                 │
└──────────────────────────┘           └─────────────────────────────────┘
            │                                           │
            │ 1:N                                      │ N:1
            ▼                                           ▼
┌──────────────────────────┐           ┌─────────────────────────────────┐
│  growthcenter_user_      │           │ growthcenter_user_identity_     │
│  identity_history        │           │ task_schedule                   │
│                          │           │                                 │
│ • Id (PK)               │           │ • Id (PK)                      │
│ • User_Id               │           │ • User_Id                      │
│ • Identity              │           │ • Grade_Task_Id (FK)           │
│ • Grade                 │           │ • Schedule (进度值)             │
│ • Identity_Grade_Id     │           │ • Is_Finish                    │
│ • Create_Time           │           │                                 │
└──────────────────────────┘           └─────────────────────────────────┘

┌──────────────────────────┐
│  growthcenter_user_      │
│  relation                │
│                          │
│ • Id (PK)               │
│ • User_Id               │
│ • Object_Code           │
│ • Object_Type           │
│ • Status                │
└──────────────────────────┘
```

## 4. 外键关联关系

### 4.1 主要外键约束

| 子表 | 外键字段 | 父表 | 父表主键 | 关系类型 |
|------|----------|------|----------|----------|
| growthcenter_user_identity | Identity_Grade_Id | growthcenter_identity_grade | Id | N:1 |
| growthcenter_identity_task_association | Identity_Grade_Id | growthcenter_identity_grade | Id | N:1 |
| growthcenter_identity_task_association | Grade_Task_Id | growthcenter_grade_task | Id | N:1 |
| growthcenter_user_identity_task_schedule | Grade_Task_Id | growthcenter_grade_task | Id | N:1 |

### 4.2 逻辑关联关系

| 表1 | 表2 | 关联字段 | 关系说明 |
|-----|-----|----------|----------|
| growthcenter_user_identity | growthcenter_user_identity_history | User_Id | 用户身份与历史记录 |
| growthcenter_user_identity | growthcenter_user_identity_task_schedule | User_Id | 用户身份与任务进度 |
| growthcenter_user_identity | growthcenter_user_relation | User_Id | 用户身份与关联关系 |

## 5. 索引设计和查询优化

### 5.1 建议索引

```sql
-- 用户身份表
CREATE INDEX idx_user_identity_user_id ON growthcenter_user_identity(User_Id);
CREATE INDEX idx_user_identity_status ON growthcenter_user_identity(Status);

-- 用户任务进度表
CREATE INDEX idx_task_schedule_user_identity ON growthcenter_user_identity_task_schedule(User_Id, Identity);
CREATE INDEX idx_task_schedule_grade_task ON growthcenter_user_identity_task_schedule(Grade_Task_Id);

-- 身份历史表
CREATE INDEX idx_identity_history_user_id ON growthcenter_user_identity_history(User_Id);
CREATE INDEX idx_identity_history_identity ON growthcenter_user_identity_history(Identity);

-- 身份等级配置表
CREATE INDEX idx_identity_grade_identity ON growthcenter_identity_grade(Identity);
CREATE INDEX idx_identity_grade_enable ON growthcenter_identity_grade(Is_Enable);

-- 任务关联表
CREATE INDEX idx_task_association_grade_id ON growthcenter_identity_task_association(Identity_Grade_Id);
CREATE INDEX idx_task_association_task_id ON growthcenter_identity_task_association(Grade_Task_Id);
```

### 5.2 常用查询模式

#### 查询用户身份规则
```sql
SELECT i.Identity, i.Grade, i.Badge, tsk.Name, tsk.Target
FROM growthcenter_identity_grade i
LEFT JOIN growthcenter_identity_task_association ass ON i.Id = ass.Identity_Grade_Id
LEFT JOIN growthcenter_grade_task tsk ON ass.Grade_Task_Id = tsk.Id
LEFT JOIN growthcenter_user_identity u ON i.Id = u.Identity_Grade_Id AND u.User_Id = ?
WHERE i.Is_Enable = 1
ORDER BY i.Identity, i.Grade, ass.Order;
```

#### 查询用户任务进度
```sql
SELECT uts.User_Id, gt.Name, uts.Schedule, uts.Is_Finish
FROM growthcenter_user_identity_task_schedule uts
JOIN growthcenter_grade_task gt ON uts.Grade_Task_Id = gt.Id
WHERE uts.User_Id = ? AND uts.Identity = ?
ORDER BY gt.Task;
```

## 6. 数据完整性约束

### 6.1 业务规则约束

1. **用户身份唯一性**: 每个用户同时只能有一个有效身份 (Status=1)
2. **等级顺序性**: 用户等级升级必须按顺序进行，不能跳级
3. **任务关联完整性**: 每个身份等级必须至少关联一个任务
4. **进度数据一致性**: 任务进度表中的Identity和Grade必须与IdentityGradeId一致

### 6.2 数据验证规则

```sql
-- 验证用户身份唯一性
SELECT User_Id, COUNT(*) as cnt 
FROM growthcenter_user_identity 
WHERE Status = 1 
GROUP BY User_Id 
HAVING cnt > 1;

-- 验证任务关联完整性
SELECT ig.Id, ig.Identity, ig.Grade, COUNT(ita.Id) as task_count
FROM growthcenter_identity_grade ig
LEFT JOIN growthcenter_identity_task_association ita ON ig.Id = ita.Identity_Grade_Id
WHERE ig.Is_Enable = 1
GROUP BY ig.Id
HAVING task_count = 0;
```

## 7. 个人IB功能的数据组织

### 7.1 个人IB数据示例

```sql
-- 身份等级配置
INSERT INTO growthcenter_identity_grade VALUES
('PIB_NORMAL_001', 5, 1, 1, 1, 'banner.png', 'badge.png', ...),
('PIB_BRONZE_001', 5, 2, 1, 1, 'banner.png', 'badge.png', ...),
('PIB_SILVER_001', 5, 3, 1, 1, 'banner.png', 'badge.png', ...),
('PIB_GOLD_001', 5, 4, 1, 1, 'banner.png', 'badge.png', ...);

-- 任务配置
INSERT INTO growthcenter_grade_task VALUES
('PIB_TASK_001', 1, 500, '选择个人IB身份', '成为个人IB', 0, '', ...),
('PIB_TASK_002', 2, 501, '账户资产验证', '资产≥$20,000', 20000, '$', ...),
('PIB_TASK_003', 2, 502, '账户资产验证', '资产≥$50,000', 50000, '$', ...),
('PIB_TASK_004', 2, 503, '账户资产验证', '资产≥$100,000', 100000, '$', ...),
('PIB_TASK_005', 2, 504, '账户资产验证', '资产≥$200,000', 200000, '$', ...);
```

### 7.2 用户升级数据流

1. **用户选择个人IB身份**:
   ```sql
   INSERT INTO growthcenter_user_identity (User_Id, Identity_Grade_Id, Status) 
   VALUES ('user123', 'PIB_NORMAL_001', 1);
   ```

2. **创建任务进度记录**:
   ```sql
   INSERT INTO growthcenter_user_identity_task_schedule 
   (User_Id, Identity, Grade, Grade_Task_Id, Is_Finish, Schedule)
   VALUES ('user123', 5, 1, 'PIB_TASK_001', 1, 1);
   ```

3. **资产达标触发升级**:
   ```sql
   UPDATE growthcenter_user_identity 
   SET Identity_Grade_Id = 'PIB_BRONZE_001', Acquire_Time = NOW()
   WHERE User_Id = 'user123';
   
   INSERT INTO growthcenter_user_identity_history
   (User_Id, Identity, Grade, Identity_Grade_Id)
   VALUES ('user123', 5, 2, 'PIB_BRONZE_001');
   ```

## 8. 数据流转示例：个人IB用户完整升级过程

### 8.1 初始化阶段：用户选择个人IB身份

#### 步骤1：插入用户身份记录
```sql
INSERT INTO growthcenter_user_identity (
    Id, User_Id, Identity_Grade_Id, Status, Acquire_Time, Create_Time, Update_Time
) VALUES (
    'UI1703123456789012', 'user_test_001', 'PIB_NORMAL_001', 1, NOW(), NOW(), NOW()
);
```

#### 步骤2：创建任务进度记录
```sql
-- 选择个人IB身份任务（自动完成）
INSERT INTO growthcenter_user_identity_task_schedule (
    Id, User_Id, Identity, Grade, Identity_Grade_Id, Grade_Task_Id,
    Schedule, Is_Finish, Create_Time, Update_Time
) VALUES (
    'UTS1703123456789013', 'user_test_001', 5, 1, 'PIB_NORMAL_001', 'PIB_TASK_001',
    1, true, NOW(), NOW()
);

-- 账户资产验证任务（待完成）
INSERT INTO growthcenter_user_identity_task_schedule (
    Id, User_Id, Identity, Grade, Identity_Grade_Id, Grade_Task_Id,
    Schedule, Is_Finish, Create_Time, Update_Time
) VALUES (
    'UTS1703123456789014', 'user_test_001', 5, 1, 'PIB_NORMAL_001', 'PIB_TASK_002',
    0, false, NOW(), NOW()
);
```

#### 步骤3：创建用户关联关系
```sql
INSERT INTO growthcenter_user_relation (
    Id, User_Id, Object_Code, Object_Type, Status, Create_Time
) VALUES (
    'UR1703123456789015', 'user_test_001', 'user_test_001', 1, 1, NOW()
);
```

### 8.2 升级阶段：用户资产达到$75,000

#### 步骤1：更新任务进度
```sql
-- 更新普通等级资产验证任务（$20,000）
UPDATE growthcenter_user_identity_task_schedule
SET Schedule = 20000, Is_Finish = true, Update_Time = NOW()
WHERE User_Id = 'user_test_001' AND Grade_Task_Id = 'PIB_TASK_002';

-- 创建青铜等级资产验证任务
INSERT INTO growthcenter_user_identity_task_schedule (
    Id, User_Id, Identity, Grade, Identity_Grade_Id, Grade_Task_Id,
    Schedule, Is_Finish, Create_Time, Update_Time
) VALUES (
    'UTS1703123456789016', 'user_test_001', 5, 2, 'PIB_BRONZE_001', 'PIB_TASK_003',
    75000, true, NOW(), NOW()
);
```

#### 步骤2：升级用户身份等级
```sql
UPDATE growthcenter_user_identity
SET Identity_Grade_Id = 'PIB_BRONZE_001', Acquire_Time = NOW(), Update_Time = NOW()
WHERE User_Id = 'user_test_001';
```

#### 步骤3：记录升级历史
```sql
INSERT INTO growthcenter_user_identity_history (
    Id, User_Id, Identity, Grade, Identity_Grade_Id, Create_Time
) VALUES (
    'UIH1703123456789017', 'user_test_001', 5, 2, 'PIB_BRONZE_001', NOW()
);
```

### 8.3 数据查询示例

#### 查询用户当前身份状态
```sql
SELECT
    ui.User_Id,
    ig.Identity,
    ig.Grade,
    ui.Acquire_Time,
    ui.Status
FROM growthcenter_user_identity ui
JOIN growthcenter_identity_grade ig ON ui.Identity_Grade_Id = ig.Id
WHERE ui.User_Id = 'user_test_001' AND ui.Status = 1;

-- 结果：
-- User_Id: user_test_001
-- Identity: 5 (PERSONAL_IB)
-- Grade: 2 (BRONZE)
-- Status: 1 (Active)
```

#### 查询用户任务完成情况
```sql
SELECT
    uts.User_Id,
    gt.Name as TaskName,
    gt.Target,
    gt.Unit,
    uts.Schedule,
    uts.Is_Finish,
    CASE
        WHEN gt.Type = 1 THEN CONCAT(uts.Schedule * 100, '%')
        WHEN gt.Type = 2 THEN CONCAT(uts.Schedule, '/', gt.Target, gt.Unit)
    END as Progress
FROM growthcenter_user_identity_task_schedule uts
JOIN growthcenter_grade_task gt ON uts.Grade_Task_Id = gt.Id
WHERE uts.User_Id = 'user_test_001' AND uts.Identity = 5
ORDER BY gt.Task;

-- 结果：
-- TaskName: 选择个人IB身份, Progress: 100%, Is_Finish: true
-- TaskName: 账户资产验证, Progress: 20000/20000$, Is_Finish: true
-- TaskName: 账户资产验证, Progress: 75000/50000$, Is_Finish: true
```

#### 查询用户升级历史
```sql
SELECT
    uih.User_Id,
    uih.Identity,
    uih.Grade,
    uih.Create_Time as Upgrade_Time,
    CASE uih.Grade
        WHEN 1 THEN '普通'
        WHEN 2 THEN '青铜'
        WHEN 3 THEN '白银'
        WHEN 4 THEN '黄金'
    END as Grade_Name
FROM growthcenter_user_identity_history uih
WHERE uih.User_Id = 'user_test_001' AND uih.Identity = 5
ORDER BY uih.Create_Time;

-- 结果：
-- Grade: 1, Grade_Name: 普通, Upgrade_Time: 2023-12-21 10:00:00
-- Grade: 2, Grade_Name: 青铜, Upgrade_Time: 2023-12-21 11:30:00
```

## 9. 性能优化建议

### 9.1 分区策略
```sql
-- 按用户ID哈希分区用户任务进度表
ALTER TABLE growthcenter_user_identity_task_schedule
PARTITION BY HASH(User_Id) PARTITIONS 16;

-- 按时间分区历史记录表
ALTER TABLE growthcenter_user_identity_history
PARTITION BY RANGE (YEAR(Create_Time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);
```

### 9.2 缓存策略
```go
// 身份等级配置缓存（Redis）
key: "growth_center:identity_grade:{identity}:{grade}"
value: IdentityGrade JSON

// 用户当前身份缓存
key: "growth_center:user_identity:{user_id}"
value: UserIdentity JSON

// 任务配置缓存
key: "growth_center:grade_task:{task_id}"
value: GradeTask JSON
```

### 9.3 读写分离
- **写操作**：身份切换、等级升级、任务进度更新 → 主库
- **读操作**：身份规则查询、用户详情查询 → 从库
- **缓存预热**：定期将热点数据加载到Redis

## 10. 数据一致性保障

### 10.1 事务边界
```go
// 用户升级事务
func (s *Service) upgradeUserIdentity(ctx context.Context, model *SaveUserIdentityModel) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 更新用户身份
        if err := tx.Save(&model.UserIdentity).Error; err != nil {
            return err
        }

        // 2. 批量插入历史记录
        if err := tx.CreateInBatches(model.UserHistory, 100).Error; err != nil {
            return err
        }

        // 3. 批量更新任务进度
        for _, task := range model.UserTaskSchedule {
            if err := tx.Save(task).Error; err != nil {
                return err
            }
        }

        // 4. 更新用户关联关系
        if err := tx.Save(&model.UserRelation).Error; err != nil {
            return err
        }

        return nil
    })
}
```

### 10.2 数据校验规则
```sql
-- 检查数据一致性的存储过程
DELIMITER //
CREATE PROCEDURE CheckDataConsistency()
BEGIN
    -- 检查用户身份唯一性
    SELECT 'Multiple active identities' as Issue, User_Id, COUNT(*) as Count
    FROM growthcenter_user_identity
    WHERE Status = 1
    GROUP BY User_Id
    HAVING COUNT(*) > 1;

    -- 检查任务进度数据一致性
    SELECT 'Task schedule inconsistency' as Issue, uts.User_Id, uts.Identity, uts.Grade
    FROM growthcenter_user_identity_task_schedule uts
    JOIN growthcenter_identity_grade ig ON uts.Identity_Grade_Id = ig.Id
    WHERE uts.Identity != ig.Identity OR uts.Grade != ig.Grade;

    -- 检查孤立的任务进度记录
    SELECT 'Orphaned task schedule' as Issue, uts.User_Id, uts.Grade_Task_Id
    FROM growthcenter_user_identity_task_schedule uts
    LEFT JOIN growthcenter_grade_task gt ON uts.Grade_Task_Id = gt.Id
    WHERE gt.Id IS NULL;
END //
DELIMITER ;
```

这种数据库设计实现了灵活的身份等级管理，支持多身份、多等级、多任务的复杂业务场景，同时保证了数据的一致性和查询的高效性。通过合理的索引设计、缓存策略和事务管理，系统能够支持大规模用户的并发操作。
