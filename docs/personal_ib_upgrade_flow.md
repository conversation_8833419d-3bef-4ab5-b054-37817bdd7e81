# 个人IB等级升级流程详解

## 概述

个人IB身份支持四级等级体系，用户通过账户资产验证任务可以自动升级到对应等级。本文档详细说明了个人IB如何根据收到的消息升级到对应等级的完整流程。

## 等级体系设计

### 四级等级配置

| 等级 | 英文名称 | 资产门槛 | 任务要求 |
|------|----------|----------|----------|
| 普通 | NORMAL | $20,000 | 选择个人IB身份 + 账户资产≥$20,000 |
| 青铜 | BRONZE | $50,000 | 账户资产≥$50,000 |
| 白银 | SILVER | $100,000 | 账户资产≥$100,000 |
| 黄金 | GOLD | $200,000 | 账户资产≥$200,000 |

### 任务枚举定义

```protobuf
enum TaskEnum {
  CHOOSE_PERSONAL_IB=500;            // 选择个人IB身份
  VERIFY_IB_ACCOUNT_ASSETS_1=501;    // 实盘账户资金不小于$20000
  VERIFY_IB_ACCOUNT_ASSETS_2=502;    // 实盘账户资金不小于$50000
  VERIFY_IB_ACCOUNT_ASSETS_3=503;    // 实盘账户资金不小于$100000
  VERIFY_IB_ACCOUNT_ASSETS_4=504;    // 实盘账户资金不小于$200000
}
```

## 消息处理流程

### 1. 消息接收入口

```go
// Kafka消息分发
func (s *Service) HandleGrowthMsg(ctx context.Context, m string, h broker.Headers, in *pb.UserGrowthMsg) error {
    switch in.Type {
    case pb.GrowthMsgType_PERSONAL_IB_TYPE:
        return s.ReceivePersonalIBTaskMsg(ctx, m, h, in.PersonalIb)
    // ... 其他消息类型
    }
}
```

### 2. 个人IB消息处理

```go
func (s *Service) ReceivePersonalIBTaskMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.PersonalIBTaskMsg) error {
    // 1. 验证用户ID
    if strings.TrimSpace(in.UserId) == "" {
        return errors.New("UserId cannot be empty or null")
    }

    // 2. 记录消息内容
    l := s.log.WithContext(ctx)
    l.Infof("个人IB收到的任务为:%#v", *in)

    // 3. 获取成长规则处理器
    method := s.getPersonalIBGrowthRule(in)

    // 4. 调用等级升级处理逻辑
    err := s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_PERSONAL_IB, method)
    if err != nil {
        l.Errorf("ReceivePersonalIBTaskMsg,Err:%v", err)
        return err
    }
    return nil
}
```

### 3. 成长规则处理器

```go
func (s *Service) getPersonalIBGrowthRule(in *pb.PersonalIBTaskMsg) TaskScheduler {
    return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
        var (
            isFinished bool
            schedule   float64
        )

        // 处理账户资产验证任务
        if t.Task == pb.TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_1 ||
           t.Task == pb.TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_2 ||
           t.Task == pb.TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_3 ||
           t.Task == pb.TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_4 {
            
            if in.AccountAssets >= float32(t.Target) {
                schedule = float64(t.Target)    // 进度设为目标值
                isFinished = true               // 任务完成
            } else {
                schedule = float64(in.AccountAssets)  // 进度设为实际资产
                isFinished = false                    // 任务未完成
            }
        }

        // 处理选择个人IB身份任务
        if t.Task == pb.TaskEnum_CHOOSE_PERSONAL_IB {
            isFinished = true
            schedule = 1
        }

        return &models.TaskCompletion{
            IsFinished: isFinished,
            Schedule:   schedule,
        }
    }
}
```

## 等级升级核心逻辑

### HandleIdentityGradeUpgrade函数流程

```go
func (s *Service) HandleIdentityGradeUpgrade(ctx context.Context, userId string, identity pb.UserIdentity, fun TaskScheduler) error {
    // 1. 获取用户当前身份和等级信息
    userGrade, err := s.userIdentity.GetUserIdentityGrade(ctx, userId)
    
    // 2. 验证身份一致性
    if userGrade.Identity != identity {
        return errors.New("身份不一致，消息丢弃")
    }
    
    // 3. 检查是否已达最高等级
    if userGrade.Grade == pb.UserGrade_GOLD {
        return errors.New("用户已经升级至最高等级")
    }
    
    // 4. 获取该身份下所有等级的任务配置
    identityGrade, err := s.gradeTask.FindTaskWithIdentity(ctx, identity)
    
    // 5. 按等级分组任务并计算完成状态
    for _, v := range _gradeSortBase {
        tasks := mapGradeTask[v]
        if len(tasks) > 0 {
            var gradeFinish = true
            var mode = tasks[0].Mode
            
            for _, sv := range tasks {
                // 调用成长规则处理器
                taskCompletion := fun(sv)
                
                // 根据完成模式判断等级是否完成
                if mode == pb.IdentityGradeMode_ALL_FINISH {
                    gradeFinish = gradeFinish && taskCompletion.IsFinished
                } else if mode == pb.IdentityGradeMode_ANY_ONE {
                    gradeFinish = gradeFinish || taskCompletion.IsFinished
                }
            }
            
            // 记录完成的等级
            if gradeFinish {
                gradeFinishList = append(gradeFinishList, v)
            }
        }
    }
    
    // 6. 按顺序升级等级（不能跳级）
    for _, v := range gradeFinishList {
        if v-currentFinishGrade == 1 {
            currentFinishGrade = v
            // 记录升级历史
            // 更新用户身份等级
        }
    }
    
    // 7. 在事务中保存所有数据
    return s.userIdentity.Save(ctx, &models.SaveUserIdentityModel{...})
}
```

## 具体升级场景

### 场景1：新用户首次选择个人IB身份

**消息内容：**
```json
{
  "type": 1,
  "identity": {
    "user_id": "user123",
    "identity": "personal_ib"
  }
}
```

**处理结果：**
- 创建个人IB身份记录
- 自动完成"选择个人IB身份"任务
- 等级设置为普通(NORMAL)
- 等待资产验证任务

### 场景2：用户资产达到$25,000

**消息内容：**
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user123",
    "account_assets": 25000.0
  }
}
```

**处理逻辑：**
1. 检查VERIFY_IB_ACCOUNT_ASSETS_1任务（目标$20,000）
   - 25000 >= 20000 ✓ 任务完成
2. 检查VERIFY_IB_ACCOUNT_ASSETS_2任务（目标$50,000）
   - 25000 < 50000 ✗ 任务未完成

**处理结果：**
- 普通等级的资产验证任务完成
- 用户保持普通等级（已满足条件）
- 青铜等级任务进度更新为25000/50000

### 场景3：用户资产达到$60,000

**消息内容：**
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user123",
    "account_assets": 60000.0
  }
}
```

**处理逻辑：**
1. 检查VERIFY_IB_ACCOUNT_ASSETS_2任务（目标$50,000）
   - 60000 >= 50000 ✓ 任务完成
2. 检查VERIFY_IB_ACCOUNT_ASSETS_3任务（目标$100,000）
   - 60000 < 100000 ✗ 任务未完成

**处理结果：**
- 青铜等级任务完成
- 用户自动升级到青铜等级
- 白银等级任务进度更新为60000/100000

### 场景4：用户资产达到$250,000

**消息内容：**
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user123",
    "account_assets": 250000.0
  }
}
```

**处理逻辑：**
1. 检查所有资产验证任务
   - VERIFY_IB_ACCOUNT_ASSETS_3: 250000 >= 100000 ✓
   - VERIFY_IB_ACCOUNT_ASSETS_4: 250000 >= 200000 ✓

**处理结果：**
- 白银和黄金等级任务都完成
- 用户按顺序升级：青铜→白银→黄金
- 达到最高等级

## 数据库配置

### 身份等级配置
```sql
-- 个人IB各等级配置
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode) VALUES
('PIB_NORMAL_001', 5, 1, 1, 1),  -- 普通等级
('PIB_BRONZE_001', 5, 2, 1, 1),  -- 青铜等级
('PIB_SILVER_001', 5, 3, 1, 1),  -- 白银等级
('PIB_GOLD_001', 5, 4, 1, 1);    -- 黄金等级
```

### 任务配置
```sql
-- 个人IB任务配置
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Target, Unit) VALUES
('PIB_TASK_001', 1, 500, '选择个人IB身份', 0, ''),
('PIB_TASK_002', 2, 501, '账户资产验证', 20000, '$'),
('PIB_TASK_003', 2, 502, '账户资产验证', 50000, '$'),
('PIB_TASK_004', 2, 503, '账户资产验证', 100000, '$'),
('PIB_TASK_005', 2, 504, '账户资产验证', 200000, '$');
```

## 关键特性

### 1. 顺序升级
- 等级升级必须按顺序进行，不能跳级
- 即使资产满足多个等级要求，也会逐级升级

### 2. 实时响应
- 收到资产更新消息后立即处理
- 满足条件时自动触发升级

### 3. 数据一致性
- 所有升级操作在数据库事务中执行
- 保证数据的完整性和一致性

### 4. 错误处理
- 身份不一致的消息会被丢弃
- 已达最高等级时不再处理升级

### 5. 日志记录
- 详细记录消息处理过程
- 便于问题排查和监控

## 监控和调试

### 关键日志
```
个人IB收到的任务为: {UserId:"user123" AccountAssets:25000}
用户已经升级至最高等级
身份不一致，消息丢弃
```

### 数据验证查询
```sql
-- 查看用户当前等级
SELECT ui.User_Id, ig.Identity, ig.Grade, ui.Acquire_Time
FROM growthcenter_user_identity ui
JOIN growthcenter_identity_grade ig ON ui.Identity_Grade_Id = ig.Id
WHERE ui.User_Id = 'user123' AND ig.Identity = 5;

-- 查看任务进度
SELECT uts.User_Id, gt.Name, uts.Schedule, uts.Is_Finish
FROM growthcenter_user_identity_task_schedule uts
JOIN growthcenter_grade_task gt ON uts.Grade_Task_Id = gt.Id
WHERE uts.User_Id = 'user123' AND uts.Identity = 5;
```

通过这个完整的流程，个人IB用户可以根据其账户资产的变化自动升级到对应的等级，实现了灵活、自动化的等级管理机制。
