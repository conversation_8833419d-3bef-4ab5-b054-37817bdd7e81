-- 个人IB身份数据库初始化脚本
-- 执行前请确保数据库连接正常

-- ================================
-- 1. 身份等级配置数据
-- ================================

-- 个人IB普通等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator) 
VALUES ('PIB_NORMAL_001', 5, 1, 1, 1, 'personal_ib_normal_banner.png', 'personal_ib_normal_badge.png', 'personal_ib_normal_user_badge.png', 'personal_ib_normal_share_badge.png', NOW(), 'system');

-- 个人IB青铜等级  
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('PIB_BRONZE_001', 5, 2, 1, 1, 'personal_ib_bronze_banner.png', 'personal_ib_bronze_badge.png', 'personal_ib_bronze_user_badge.png', 'personal_ib_bronze_share_badge.png', NOW(), 'system');

-- 个人IB白银等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('PIB_SILVER_001', 5, 3, 1, 1, 'personal_ib_silver_banner.png', 'personal_ib_silver_badge.png', 'personal_ib_silver_user_badge.png', 'personal_ib_silver_share_badge.png', NOW(), 'system');

-- 个人IB黄金等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('PIB_GOLD_001', 5, 4, 1, 1, 'personal_ib_gold_banner.png', 'personal_ib_gold_badge.png', 'personal_ib_gold_user_badge.png', 'personal_ib_gold_share_badge.png', NOW(), 'system');

-- ================================
-- 2. 等级任务配置数据
-- ================================

-- 选择个人IB身份任务
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_001', 1, 500, '选择个人IB身份', '选择身份为个人ID，并通过认证', 0, '', '260', 1, NOW(), 'system', '选择身份为个人ID，并通过认证');

-- 账户资产验证任务（青铜级 - $20,000）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_002', 2, 501, '账户资产验证', 'MT4/MT5账户资产≥${0}', 20000, '$', '200', 1, NOW(), 'system', 'MT4/MT5账户资产≥{0}');

-- 账户资产验证任务（白银级 - $50,000）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_003', 2, 502, '账户资产验证', 'MT4/MT5账户资产≥${0}', 50000, '$', '200', 1, NOW(), 'system', 'MT4/MT5账户资产≥{0}');

-- 账户资产验证任务（黄金级 - $100,000）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('PIB_TASK_004', 2, 503, '账户资产验证', 'MT4/MT5账户资产≥${0}', 100000, '$', '200', 1, NOW(), 'system', 'MT4/MT5账户资产≥{0}');

-- ================================
-- 3. 身份任务关联配置
-- ================================

-- 个人IB普通等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('PIB_ASSOC_001', 'PIB_NORMAL_001', 'PIB_TASK_001', 1, NOW());

-- 个人IB青铜等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('PIB_ASSOC_002', 'PIB_BRONZE_001', 'PIB_TASK_002', 1, NOW());

-- 个人IB白银等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('PIB_ASSOC_003', 'PIB_SILVER_001', 'PIB_TASK_003', 1, NOW());

-- 个人IB黄金等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('PIB_ASSOC_004', 'PIB_GOLD_001', 'PIB_TASK_004', 1, NOW());

-- ================================
-- 4. 数据验证查询
-- ================================

-- 验证身份等级配置
SELECT * FROM growthcenter_identity_grade WHERE Identity = 5;

-- 验证任务配置
SELECT * FROM growthcenter_grade_task WHERE Task IN (500, 501, 502, 503);

-- 验证任务关联配置
SELECT 
    ita.Id,
    ita.Identity_Grade_Id,
    ig.Identity,
    ig.Grade,
    ita.Grade_Task_Id,
    gt.Name as TaskName,
    gt.Task as TaskEnum,
    ita.`Order`
FROM growthcenter_identity_task_association ita
JOIN growthcenter_identity_grade ig ON ita.Identity_Grade_Id = ig.Id
JOIN growthcenter_grade_task gt ON ita.Grade_Task_Id = gt.Id
WHERE ig.Identity = 5
ORDER BY ig.Grade, ita.`Order`;
