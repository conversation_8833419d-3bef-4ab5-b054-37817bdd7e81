-- 个人IB身份数据库初始化脚本
-- 执行前请确保数据库连接正常

-- ================================
-- 1. 身份等级配置数据
-- ================================

-- 个人IB普通等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator) 
VALUES ('IG00017', 5, 1, 1, 1, '/growth_center/ib_normal.png', '/growth_center/normal_ib.png', '/growth_center/normal_ib.png', '/growth_center/normal_ib.png', NOW(), 'hyde');

-- 个人IB青铜等级  
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('IG00018', 5, 2, 1, 1, '/growth_center/ib_bronze.png', '/growth_center/bronze_ib.png','/growth_center/bronze_ib.png', '/growth_center/bronze_ib.png', NOW(), 'hyde');

-- 个人IB白银等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('IG00019', 5, 3, 1, 1, '/growth_center/ib_silver.png', '/growth_center/silver_ib.png','/growth_center/silver_ib.png', '/growth_center/silver_ib.png', NOW(), 'hyde');

-- 个人IB黄金等级
INSERT INTO growthcenter_identity_grade (Id, Identity, Grade, Is_Enable, Mode, Banner, Badge, User_Badge, Share_Badge, Create_Time, Creator)
VALUES ('IG00020', 5, 4, 1, 1, '/growth_center/ib_gold.png', '/growth_center/gold_ib.png','/growth_center/gold_ib.png', '/growth_center/gold_ib.png', NOW(), 'hyde');

-- ================================
-- 2. 等级任务配置数据
-- ================================

-- 选择个人IB身份任务
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('tsk00029', 1, 500, '选择身份为{0}', '选择身份为个人ID，并通过认证', 0, '', '260', 1, NOW(), 'hyde', '');

-- 账户资产验证任务（青铜级 - $20,000）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('tsk00030', 2, 501, '账户资金验证', 'MT4/MT5账户资产≥${0}', 20000, '$', '200', 1, NOW(), 'hyde', 'MT4/MT5账户资产≥{0}');

-- 账户资产验证任务（白银级 - $50,000）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('tsk00031', 2, 502, '账户资金验证', 'MT4/MT5账户资产≥${0}', 50000, '$', '200', 1, NOW(), 'hyde', 'MT4/MT5账户资产≥{0}');

-- 账户资产验证任务（黄金级 - $100,000）
INSERT INTO growthcenter_grade_task (Id, Type, Task, Name, Content, Target, Unit, Jump_Address, Is_Enable, Create_Time, Creator, Label)
VALUES ('tsk00032', 2, 503, '账户资金验证', 'MT4/MT5账户资产≥${0}', 100000, '$', '200', 1, NOW(), 'hyde', 'MT4/MT5账户资产≥{0}');

-- ================================
-- 3. 身份任务关联配置
-- ================================

-- 个人IB普通等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('ITS000029', 'IG00017', 'tsk00029', 1, NOW());

-- 个人IB青铜等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('ITS000030', 'IG00018', 'tsk00030', 1, NOW());

-- 个人IB白银等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('ITS000031', 'IG00019', 'tsk00031', 1, NOW());

-- 个人IB黄金等级任务关联
INSERT INTO growthcenter_identity_task_association (Id, Identity_Grade_Id, Grade_Task_Id, `Order`, Create_Time)
VALUES ('ITS000032', 'IG00020', 'tsk00032', 1, NOW());

-- ================================
-- 4. 数据验证查询
-- ================================

-- 验证身份等级配置
SELECT * FROM growthcenter_identity_grade WHERE Identity = 5;

-- 验证任务配置
SELECT * FROM growthcenter_grade_task WHERE Task IN (500, 501, 502, 503);

-- 验证任务关联配置
SELECT 
    ita.Id,
    ita.Identity_Grade_Id,
    ig.Identity,
    ig.Grade,
    ita.Grade_Task_Id,
    gt.Name as TaskName,
    gt.Task as TaskEnum,
    ita.`Order`
FROM growthcenter_identity_task_association ita
JOIN growthcenter_identity_grade ig ON ita.Identity_Grade_Id = ig.Id
JOIN growthcenter_grade_task gt ON ita.Grade_Task_Id = gt.Id
WHERE ig.Identity = 5
ORDER BY ig.Grade, ita.`Order`;
