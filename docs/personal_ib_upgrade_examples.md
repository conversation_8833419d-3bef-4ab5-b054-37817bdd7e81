# 个人IB升级实例演示

## 完整升级流程演示

以用户 `user_test_001` 为例，演示从选择个人IB身份到升级至黄金等级的完整过程。

## 步骤1：用户选择个人IB身份

### API调用
```bash
curl -X POST "http://localhost:8000/v1/app/identity/switch" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_test_001",
    "identity": 5
  }'
```

### 或者发送Kafka消息
```json
{
  "type": 1,
  "identity": {
    "user_id": "user_test_001",
    "identity": "personal_ib",
    "enterprise_code": ""
  }
}
```

### 系统处理结果
1. **创建用户身份记录**
   ```sql
   INSERT INTO growthcenter_user_identity (User_Id, Identity_Grade_Id, Status, Acquire_Time)
   VALUES ('user_test_001', 'PIB_NORMAL_001', 1, NOW());
   ```

2. **自动完成选择身份任务**
   ```sql
   INSERT INTO growthcenter_user_identity_task_schedule 
   (User_Id, Identity, Grade, Grade_Task_Id, Is_Finish, Schedule)
   VALUES ('user_test_001', 5, 1, 'PIB_TASK_001', 1, 1);
   ```

3. **创建资产验证任务**
   ```sql
   INSERT INTO growthcenter_user_identity_task_schedule 
   (User_Id, Identity, Grade, Grade_Task_Id, Is_Finish, Schedule)
   VALUES ('user_test_001', 5, 1, 'PIB_TASK_002', 0, 0);
   ```

### 当前状态
- **用户等级**：普通(NORMAL)
- **完成任务**：选择个人IB身份 ✓
- **待完成任务**：账户资产≥$20,000

## 步骤2：用户资产达到$25,000

### 发送资产更新消息
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user_test_001",
    "account_assets": 25000.0
  }
}
```

### 系统处理过程

#### 1. 消息接收和验证
```go
// ReceivePersonalIBTaskMsg 函数处理
l.Infof("个人IB收到的任务为: {UserId:user_test_001 AccountAssets:25000}")
```

#### 2. 任务完成状态计算
```go
// getPersonalIBGrowthRule 处理器
// 检查 VERIFY_IB_ACCOUNT_ASSETS_1 (目标: $20,000)
if 25000 >= 20000 {
    isFinished = true
    schedule = 20000  // 设为目标值
}
```

#### 3. 等级升级检查
```go
// HandleIdentityGradeUpgrade 核心逻辑
// 普通等级任务检查：
// - 选择个人IB身份: 已完成 ✓
// - 账户资产≥$20,000: 已完成 ✓
// 结论：普通等级所有任务完成，保持普通等级
```

#### 4. 数据库更新
```sql
-- 更新任务进度
UPDATE growthcenter_user_identity_task_schedule 
SET Is_Finish = 1, Schedule = 20000, Update_Time = NOW()
WHERE User_Id = 'user_test_001' AND Grade_Task_Id = 'PIB_TASK_002';

-- 创建青铜等级任务
INSERT INTO growthcenter_user_identity_task_schedule 
(User_Id, Identity, Grade, Grade_Task_Id, Is_Finish, Schedule)
VALUES ('user_test_001', 5, 2, 'PIB_TASK_003', 0, 25000);
```

### 当前状态
- **用户等级**：普通(NORMAL) - 保持不变
- **完成任务**：选择个人IB身份 ✓，账户资产≥$20,000 ✓
- **进行中任务**：账户资产≥$50,000 (进度: 25000/50000)

## 步骤3：用户资产达到$75,000

### 发送资产更新消息
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user_test_001",
    "account_assets": 75000.0
  }
}
```

### 系统处理过程

#### 1. 任务完成状态计算
```go
// 检查 VERIFY_IB_ACCOUNT_ASSETS_2 (目标: $50,000)
if 75000 >= 50000 {
    isFinished = true
    schedule = 50000
}

// 检查 VERIFY_IB_ACCOUNT_ASSETS_3 (目标: $100,000)
if 75000 >= 100000 {
    isFinished = false  // 未达到
    schedule = 75000    // 当前进度
}
```

#### 2. 等级升级检查
```go
// 青铜等级任务检查：
// - 账户资产≥$50,000: 已完成 ✓
// 结论：青铜等级任务完成，升级到青铜等级

// 检查升级条件：青铜等级(2) - 当前等级(1) = 1 ✓ (可以升级)
currentFinishGrade = pb.UserGrade_BRONZE
```

#### 3. 数据库更新
```sql
-- 更新用户身份等级
UPDATE growthcenter_user_identity 
SET Identity_Grade_Id = 'PIB_BRONZE_001', Acquire_Time = NOW(), Update_Time = NOW()
WHERE User_Id = 'user_test_001';

-- 记录升级历史
INSERT INTO growthcenter_user_identity_history 
(User_Id, Identity, Grade, Identity_Grade_Id, Create_Time)
VALUES ('user_test_001', 5, 2, 'PIB_BRONZE_001', NOW());

-- 更新青铜等级任务状态
UPDATE growthcenter_user_identity_task_schedule 
SET Is_Finish = 1, Schedule = 50000, Update_Time = NOW()
WHERE User_Id = 'user_test_001' AND Grade_Task_Id = 'PIB_TASK_003';

-- 创建白银等级任务
INSERT INTO growthcenter_user_identity_task_schedule 
(User_Id, Identity, Grade, Grade_Task_Id, Is_Finish, Schedule)
VALUES ('user_test_001', 5, 3, 'PIB_TASK_004', 0, 75000);
```

### 当前状态
- **用户等级**：青铜(BRONZE) ⬆️ **升级成功！**
- **完成任务**：账户资产≥$50,000 ✓
- **进行中任务**：账户资产≥$100,000 (进度: 75000/100000)

## 步骤4：用户资产达到$250,000

### 发送资产更新消息
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user_test_001",
    "account_assets": 250000.0
  }
}
```

### 系统处理过程

#### 1. 任务完成状态计算
```go
// 检查 VERIFY_IB_ACCOUNT_ASSETS_3 (目标: $100,000)
if 250000 >= 100000 {
    isFinished = true
    schedule = 100000
}

// 检查 VERIFY_IB_ACCOUNT_ASSETS_4 (目标: $200,000)
if 250000 >= 200000 {
    isFinished = true
    schedule = 200000
}
```

#### 2. 等级升级检查
```go
// 白银等级任务完成，可以升级到白银
// 黄金等级任务也完成，但需要按顺序升级

// 第一次升级：青铜(2) → 白银(3)
if 3 - 2 == 1 {  // 可以升级
    currentFinishGrade = pb.UserGrade_SILVER
    // 记录升级历史
}

// 第二次升级：白银(3) → 黄金(4)  
if 4 - 3 == 1 {  // 可以升级
    currentFinishGrade = pb.UserGrade_GOLD
    // 记录升级历史
}
```

#### 3. 数据库更新
```sql
-- 更新用户身份等级到黄金
UPDATE growthcenter_user_identity 
SET Identity_Grade_Id = 'PIB_GOLD_001', Acquire_Time = NOW(), Update_Time = NOW()
WHERE User_Id = 'user_test_001';

-- 记录白银等级升级历史
INSERT INTO growthcenter_user_identity_history 
(User_Id, Identity, Grade, Identity_Grade_Id, Create_Time)
VALUES ('user_test_001', 5, 3, 'PIB_SILVER_001', NOW());

-- 记录黄金等级升级历史
INSERT INTO growthcenter_user_identity_history 
(User_Id, Identity, Grade, Identity_Grade_Id, Create_Time)
VALUES ('user_test_001', 5, 4, 'PIB_GOLD_001', NOW());

-- 更新所有任务状态
UPDATE growthcenter_user_identity_task_schedule 
SET Is_Finish = 1, Schedule = 100000, Update_Time = NOW()
WHERE User_Id = 'user_test_001' AND Grade_Task_Id = 'PIB_TASK_004';

UPDATE growthcenter_user_identity_task_schedule 
SET Is_Finish = 1, Schedule = 200000, Update_Time = NOW()
WHERE User_Id = 'user_test_001' AND Grade_Task_Id = 'PIB_TASK_005';
```

### 当前状态
- **用户等级**：黄金(GOLD) ⬆️⬆️ **连续升级成功！**
- **完成任务**：所有任务 ✓
- **状态**：已达到最高等级

## 步骤5：用户资产继续增长到$500,000

### 发送资产更新消息
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "user_test_001",
    "account_assets": 500000.0
  }
}
```

### 系统处理结果
```go
// HandleIdentityGradeUpgrade 检查
if userGrade.Grade == pb.UserGrade_GOLD {
    return errors.New("用户已经升级至最高等级")
}
```

### 日志输出
```
用户已经升级至最高等级
```

### 当前状态
- **用户等级**：黄金(GOLD) - 保持不变
- **处理结果**：消息被忽略，不再处理升级

## 数据验证查询

### 查看用户最终状态
```sql
-- 用户当前身份等级
SELECT 
    ui.User_Id,
    ig.Identity,
    ig.Grade,
    ui.Acquire_Time,
    ui.Status
FROM growthcenter_user_identity ui
JOIN growthcenter_identity_grade ig ON ui.Identity_Grade_Id = ig.Id
WHERE ui.User_Id = 'user_test_001' AND ig.Identity = 5;

-- 结果：
-- User_Id: user_test_001
-- Identity: 5 (PERSONAL_IB)
-- Grade: 4 (GOLD)
-- Status: 1 (Active)
```

### 查看升级历史
```sql
SELECT 
    uih.User_Id,
    uih.Identity,
    uih.Grade,
    uih.Create_Time
FROM growthcenter_user_identity_history uih
WHERE uih.User_Id = 'user_test_001' AND uih.Identity = 5
ORDER BY uih.Create_Time;

-- 结果：
-- Grade: 1 (NORMAL)  - 初始等级
-- Grade: 2 (BRONZE)  - 第一次升级
-- Grade: 3 (SILVER)  - 第二次升级  
-- Grade: 4 (GOLD)    - 第三次升级
```

### 查看任务完成情况
```sql
SELECT 
    uts.User_Id,
    gt.Name as TaskName,
    gt.Task as TaskEnum,
    uts.Schedule,
    uts.Is_Finish,
    uts.Update_Time
FROM growthcenter_user_identity_task_schedule uts
JOIN growthcenter_grade_task gt ON uts.Grade_Task_Id = gt.Id
WHERE uts.User_Id = 'user_test_001' AND uts.Identity = 5
ORDER BY gt.Task;

-- 结果：
-- TaskEnum: 500, Name: 选择个人IB身份, Is_Finish: 1, Schedule: 1
-- TaskEnum: 501, Name: 账户资产验证, Is_Finish: 1, Schedule: 20000
-- TaskEnum: 502, Name: 账户资产验证, Is_Finish: 1, Schedule: 50000
-- TaskEnum: 503, Name: 账户资产验证, Is_Finish: 1, Schedule: 100000
-- TaskEnum: 504, Name: 账户资产验证, Is_Finish: 1, Schedule: 200000
```

## 关键升级规则总结

### 1. 顺序升级原则
- 即使资产满足多个等级要求，也会按顺序逐级升级
- 例如：资产$250,000时，会先升级到白银，再升级到黄金

### 2. 实时响应机制
- 每次收到资产更新消息都会重新计算所有等级的任务完成状态
- 满足条件的等级会立即触发升级

### 3. 最高等级保护
- 达到黄金等级后，后续的资产更新消息会被忽略
- 避免不必要的处理开销

### 4. 数据一致性保证
- 所有升级操作在数据库事务中执行
- 确保身份记录、历史记录、任务进度的一致性

这个完整的演示展示了个人IB用户如何通过账户资产的增长自动升级到对应等级的全过程。
