# Wiki用户成长中心 - 项目说明文档

## 项目概述

**项目名称**: wiki_user_center (Wiki用户成长中心)  
**主要用途**: 基于Kratos框架的微服务，提供用户身份成长、等级管理、任务系统等核心功能  
**技术栈**: Go 1.21 + Kratos v2 + gRPC + HTTP + MySQL + Redis + Kafka

## 核心功能模块

### 1. 用户身份成长系统
- **身份类型管理**: 投资者(INVESTOR)、KOL、服务商(SERVICE_PROVIDER)
- **等级体系**: 普通(NORMAL)、青铜(BRONZE)、白银(SILVER)、黄金(GOLD)
- **身份升级规则**: 支持全部完成(ALL_FINISH)和任意完成(ANY_ONE)两种模式
- **成长历史记录**: 完整的用户身份升级轨迹追踪

### 2. 任务系统
- **投资者任务**: 绑定真实账户、资产验证、入金规模验资、模拟交易、实名认证
- **KOL任务**: 发布优质内容、KOL成长路径
- **服务商任务**: 创建服务商、企业认证、员工管理、企业合作

### 3. 用户关系管理
- **关注系统**: 用户间关注/取消关注功能
- **关系状态**: 未关注、已关注、相互关注、自己等状态管理

### 4. 轮播图管理
- **成长中心轮播图**: 支持多语言的轮播图配置和展示

## API服务说明

### 主要服务接口

#### 用户成长中心服务 (`/api/user_growth_center/v1/`)
```
GET  /v1/app/identity/rule        - 身份升级规则
GET  /v1/app/identity/detail      - 用户成长详情
GET  /v1/app/identity/switch      - 成长中心入口开关
GET  /v1/app/identity/carousel    - 身份轮播图
GET  /v1/app/identity/share       - 身份分享信息
GET  /v1/app/identity/entry       - 升级身份入口
```

#### 用户中心服务 (`/api/user_center/v1/`)
```
POST /v1/app/usercenter/getbusinesscardlist           - 获取用户信息列表
POST /v1/app/usercenter/getenterpriseuseridsbycode    - 根据企业code获取员工ID
GET  /v1/app/usercenter/getofficialnumbertypebyid     - 根据ID获取企业类型
```

#### 其他集成服务
- **AI服务**: 交易商搜索、推荐、机器人、内容审核、翻译
- **新闻服务**: 快讯列表、详情、批量获取
- **推荐服务**: 内容推荐、商业推荐、热门内容
- **社区服务**: 帖子管理、收藏点赞、用户动态
- **论坛服务**: 调解列表、调解详情

## 项目架构特点

### 基于Kratos框架的微服务架构
- **分层架构**: HTTP/gRPC → Service → DAO → Models
- **依赖注入**: 使用Google Wire进行依赖注入管理
- **中间件支持**: 链路追踪、日志记录、指标监控、错误恢复
- **多协议支持**: 同时支持HTTP RESTful和gRPC协议

### 错误处理策略
- **分层错误处理**: HTTP层包装业务错误，Service层处理业务逻辑，DAO层包装数据库错误
- **统一错误格式**: 使用`github.com/airunny/wiki-go-tools/errors`进行错误包装
- **国际化支持**: 集成i18n多语言错误信息

### 数据存储
- **MySQL**: 主要业务数据存储，使用GORM作为ORM
- **Redis**: 缓存和会话管理
- **Kafka**: 异步消息处理，支持用户成长任务消息

## 主要配置说明

### 服务配置
```yaml
server:
  grpc:
    addr: 0.0.0.0:900      # gRPC服务地址
    timeout_seconds: 5      # 超时时间
  http:
    addr: 0.0.0.0:800      # HTTP服务地址
    timeout_seconds: 5      # 超时时间
```

### 业务配置
```yaml
business:
  img_domain: https://img.fx696.com                    # 图片域名
  kol_task_topic: user_growth_tasks_kol               # KOL任务主题
  enterprise_task_topic: user_growth_tasks_enterprise  # 企业任务主题
  user_identity_topic: user_growth_tasks_user_identity # 用户身份主题
  kafka_address: [testkfk.fxeyeinterface.com:9092]   # Kafka地址
```

## 目录结构说明

```
├── api/                    # API定义和生成的代码
│   ├── user_growth_center/ # 用户成长中心API
│   ├── user_center/        # 用户中心API  
│   ├── community/          # 社区API
│   ├── news/              # 新闻API
│   └── ...                # 其他业务模块API
├── cmd/wiki_user_center/   # 应用程序入口
│   ├── main.go            # 主程序
│   ├── wire.go            # Wire依赖注入配置
│   └── wire_gen.go        # Wire生成的代码
├── configs/               # 配置文件
├── internal/              # 内部实现
│   ├── conf/             # 配置结构定义
│   ├── dao/              # 数据访问层
│   ├── models/           # 数据模型
│   ├── server/           # 服务器配置(HTTP/gRPC/Kafka)
│   ├── service/          # 业务逻辑层
│   └── pkg/              # 内部工具包
└── wiki_protos/          # Proto文件定义(子模块)
```

## 核心数据模型

### 用户身份等级 (IdentityGrade)
- 身份类型、等级、任务模式配置
- 支持徽章、Banner等视觉元素

### 等级任务 (GradeTask)  
- 任务类型(布尔型/数值型)
- 任务目标、单位、跳转地址

### 用户身份 (UserIdentity)
- 用户当前身份和等级状态
- 任务完成进度追踪

### 用户关系 (UserRelation)
- 用户间关注关系管理
- 关注状态和时间记录

## 开发和部署

### 本地开发
```bash
# 安装依赖
make init

# 生成API代码
make api

# 生成Wire依赖注入代码  
make generate

# 构建应用
make build

# 运行服务
./bin/wiki_user_center -conf ./configs
```



*本文档为AI友好格式，提供了项目的完整技术概览和功能边界，便于AI理解项目上下文并协助开发工作。*
