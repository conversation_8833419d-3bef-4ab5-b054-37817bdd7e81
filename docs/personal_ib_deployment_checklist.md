# 个人IB功能部署检查清单

## 部署前检查

### 1. 代码检查 ✅
- [x] Proto文件修改完成 (`api-wikiprotos/user_growth_center/v1/growth_center.proto`)
- [x] Service层代码修改完成 (`internal/service/growth_center.go`)
- [x] 编译通过，无语法错误
- [x] 所有必要的枚举和消息结构已添加

### 2. 文档检查 ✅
- [x] 数据库初始化脚本准备完成 (`docs/personal_ib_database_init.sql`)
- [x] 测试指南文档完成 (`docs/personal_ib_test_guide.md`)
- [x] 实施总结文档完成 (`docs/personal_ib_implementation_summary.md`)

## 部署步骤

### 第一步：Proto文件部署
```bash
# 1. 提交api-wikiprotos的修改（如果有权限）
cd api-wikiprotos
git add user_growth_center/v1/growth_center.proto
git commit -m "feat: add personal IB identity support"
git push

# 2. 回到主项目，更新子模块
cd ..
make dep

# 3. 重新生成proto代码
make api

# 4. 验证生成的代码
go build ./...
```

### 第二步：数据库初始化
```bash
# 执行数据库初始化脚本
mysql -h <数据库主机> -u <用户名> -p <数据库名> < docs/personal_ib_database_init.sql

# 验证数据插入
mysql -h <数据库主机> -u <用户名> -p <数据库名> -e "
SELECT COUNT(*) as identity_count FROM growthcenter_identity_grade WHERE Identity = 5;
SELECT COUNT(*) as task_count FROM growthcenter_grade_task WHERE Task IN (500, 501, 502, 503, 504);
SELECT COUNT(*) as association_count FROM growthcenter_identity_task_association WHERE Identity_Grade_Id LIKE 'PIB_%';
"
```

### 第三步：服务部署
```bash
# 1. 编译服务
go build -o bin/wiki_user_center cmd/wiki_user_center/main.go

# 2. 停止现有服务（根据实际部署方式）
# systemctl stop wiki_user_center
# 或
# docker stop wiki_user_center

# 3. 部署新版本
# 复制二进制文件到部署目录
# 或更新Docker镜像

# 4. 启动服务
# systemctl start wiki_user_center
# 或
# docker start wiki_user_center
```

## 部署后验证

### 1. 服务健康检查
```bash
# 检查服务状态
curl -X GET "http://localhost:8000/healthz"

# 检查日志
tail -f /var/log/wiki_user_center.log
# 或
docker logs wiki_user_center
```

### 2. 功能验证

#### API测试
```bash
# 1. 获取身份规则（应包含个人IB）
curl -X GET "http://localhost:8000/v1/app/identity/rule?user_id=test_user_001"

# 2. 切换到个人IB身份
curl -X POST "http://localhost:8000/v1/app/identity/switch" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test_user_001", "identity": 5}'

# 3. 获取用户成长详情
curl -X GET "http://localhost:8000/v1/app/identity/detail?user_id=test_user_001"
```

#### 数据库验证
```sql
-- 验证身份等级配置
SELECT * FROM growthcenter_identity_grade WHERE Identity = 5;

-- 验证任务配置
SELECT * FROM growthcenter_grade_task WHERE Task IN (500, 501, 502, 503, 504);

-- 验证用户身份（如果有测试用户）
SELECT ui.User_Id, ig.Identity, ig.Grade, ui.Status 
FROM growthcenter_user_identity ui
JOIN growthcenter_identity_grade ig ON ui.Identity_Grade_Id = ig.Id
WHERE ui.User_Id = 'test_user_001' AND ig.Identity = 5;
```

### 3. Kafka消息测试
```bash
# 发送身份切换消息
kafka-console-producer --bootstrap-server <kafka-server> --topic user_growth_msg
# 输入消息：
{
  "type": 1,
  "identity": {
    "user_id": "test_user_001",
    "identity": "personal_ib",
    "enterprise_code": ""
  }
}

# 发送个人IB任务消息
{
  "type": 6,
  "personal_ib": {
    "user_id": "test_user_001",
    "account_assets": 25000.0
  }
}
```

## 回滚计划

### 如果需要回滚
```bash
# 1. 停止服务
systemctl stop wiki_user_center

# 2. 恢复到之前版本
# 恢复二进制文件或Docker镜像

# 3. 清理数据库（如果需要）
mysql -h <数据库主机> -u <用户名> -p <数据库名> -e "
DELETE FROM growthcenter_identity_task_association WHERE Identity_Grade_Id LIKE 'PIB_%';
DELETE FROM growthcenter_grade_task WHERE Task IN (500, 501, 502, 503, 504);
DELETE FROM growthcenter_identity_grade WHERE Identity = 5;
"

# 4. 重启服务
systemctl start wiki_user_center
```

## 监控要点

### 关键指标
- [ ] 个人IB用户注册数量
- [ ] 身份切换成功率
- [ ] 任务完成率
- [ ] 等级升级成功率

### 日志监控
- [ ] 身份切换相关日志
- [ ] 个人IB任务处理日志
- [ ] 错误和异常日志
- [ ] 性能相关日志

### 告警设置
- [ ] 个人IB消息处理失败告警
- [ ] 数据库操作异常告警
- [ ] 服务响应时间告警

## 注意事项

### 数据安全
- ⚠️ 在生产环境执行数据库脚本前，请先备份数据库
- ⚠️ 建议在测试环境充分验证后再部署到生产环境
- ⚠️ 监控部署后的系统性能和错误率

### 兼容性
- ✅ 新功能向后兼容，不影响现有用户
- ✅ 老版本客户端可以正常工作
- ✅ 现有API接口保持不变

### 性能影响
- 📊 新增的枚举值和消息处理对性能影响很小
- 📊 数据库查询性能基本无影响
- 📊 建议监控Kafka消息处理性能

## 完成确认

部署完成后，请确认以下项目：

- [ ] 服务正常启动，健康检查通过
- [ ] 数据库数据正确插入
- [ ] API接口返回正确的个人IB身份信息
- [ ] Kafka消息处理正常
- [ ] 日志记录正常，无错误信息
- [ ] 现有功能未受影响
- [ ] 监控和告警配置完成

## 联系信息

如果部署过程中遇到问题，请联系：
- 开发团队：[开发团队联系方式]
- 运维团队：[运维团队联系方式]
- 紧急联系：[紧急联系方式]
