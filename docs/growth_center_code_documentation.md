# 用户成长中心代码文档

## 概述

`internal/service/growth_center.go` 文件是用户成长中心的核心服务层实现，包含了完整的用户身份管理和等级升级系统。本文档详细说明了所有关键函数的功能和实现逻辑。

## 系统架构

### 身份体系
- **投资者(INVESTOR)**：资产验证、实名认证、账户绑定
- **KOL**：粉丝数量、内容发布等社交影响力
- **服务商(SERVICE_PROVIDER)**：企业员工、合作数量等企业发展
- **交易商(TRADER)**：企业员工、合作数量等交易业务
- **个人IB(PERSONAL_IB)**：账户资产验证等投资业务

### 等级体系
- **普通(NORMAL)**：基础等级，切换身份时自动获得
- **青铜(BRONZE)**：完成初级任务后获得
- **白银(SILVER)**：完成中级任务后获得
- **黄金(GOLD)**：完成高级任务后获得，最高等级

## 核心函数详解

### 1. 消息处理入口

#### HandleGrowthMsg
- **功能**：Kafka消息分发处理器
- **支持消息类型**：
  - SWITCH_IDENTITY_TYPE：身份切换
  - INVESTOR_TYPE：投资者任务
  - KOL_TYPE：KOL任务
  - PERSONAL_IB_TYPE：个人IB任务
  - SERVICE_PROVIDER_TYPE：服务商任务
  - TRADER_TYPE：交易商任务

### 2. 身份切换处理

#### ReceiveUserIdentityMsg
- **功能**：处理用户身份切换消息
- **核心逻辑**：
  - 验证消息参数有效性
  - 处理身份清空操作
  - 创建或更新身份记录
  - 初始化基础等级任务
  - 更新用户关联关系

#### PostUserIdentitySwitch
- **功能**：HTTP接口，用户手动切换身份
- **支持身份**：投资者、KOL、服务商、交易商、个人IB
- **实现方式**：内部调用ReceiveUserIdentityMsg

### 3. 等级升级核心逻辑

#### HandleIdentityGradeUpgrade
- **功能**：处理用户身份等级升级的核心逻辑
- **执行步骤**：
  1. 验证用户当前身份状态
  2. 获取身份下所有等级任务配置
  3. 计算用户可达到的最高等级
  4. 更新任务进度和身份等级
  5. 记录升级历史和触发奖励
- **关键特性**：
  - 等级升级必须按顺序进行
  - 支持ALL_FINISH和ANY_ONE两种完成模式
  - 防止身份不一致的消息错乱
  - 事务中执行保证数据一致性

### 4. 任务进度处理

#### ReceiveInvestorMsg
- **功能**：处理投资者任务进度更新
- **任务类型**：
  - 绑定真实账户
  - 账户资产验证
  - 实名认证
  - 入金规模验证

#### ReceiveKolMsg
- **功能**：处理KOL任务进度更新
- **任务类型**：
  - KOL之路（粉丝数量）
  - 发布内容（发帖数量）

#### ReceivePersonalIBTaskMsg
- **功能**：处理个人IB任务进度更新
- **任务类型**：
  - 选择个人IB身份
  - 四个等级的账户资产验证（$20K、$50K、$100K、$200K）

#### ReceiveServiceProviderMsg / ReceiveTraderMsg
- **功能**：处理企业身份任务进度更新
- **任务类型**：
  - 企业员工数量
  - 企业合作数量

### 5. 任务规则处理器

#### getInvestorGrowthRule
- **功能**：投资者成长规则处理器
- **支持任务**：真实账户绑定、资产验证、实名认证、入金验证

#### getKolGrowthRule
- **功能**：KOL成长规则处理器
- **支持任务**：粉丝数量验证、发帖数量验证

#### getPersonalIBGrowthRule
- **功能**：个人IB成长规则处理器
- **支持任务**：身份选择、四级资产验证

#### getServiceProviderGrowthRule
- **功能**：服务商成长规则处理器
- **支持任务**：企业员工数量、企业合作数量

### 6. HTTP API接口

#### GetIdentityRule
- **功能**：获取身份升级规则列表
- **返回内容**：
  - 所有身份的等级信息
  - 每个等级的任务要求
  - 多语言支持的规则描述
  - 身份徽章图片

#### GetUpgradeIdentity
- **功能**：获取用户身份升级信息
- **返回内容**：
  - 下一等级的升级任务
  - 任务完成状态
  - 升级提示文案

#### GetIdentityShare
- **功能**：获取身份分享图片
- **返回内容**：
  - 分享徽章图片URL
  - 身份名称和获得日期

### 7. 辅助功能

#### AddFxVIP
- **功能**：为用户添加天眼VIP会员权益
- **触发条件**：投资者升级到青铜等级
- **VIP时长**：100年（终身会员）

#### getIdentityRule (私有方法)
- **功能**：构建单个身份的规则信息
- **处理逻辑**：
  - 检查用户获得状态
  - 按等级分组规则数据
  - 生成任务信息和徽章

## 数据流程

### 身份切换流程
1. 用户通过HTTP接口或Kafka消息触发身份切换
2. 系统验证参数并解析身份类型
3. 创建或更新用户身份记录
4. 初始化普通等级的基础任务
5. 更新用户关联关系
6. 在事务中保存所有数据

### 等级升级流程
1. 接收任务进度更新消息
2. 调用对应身份的成长规则处理器
3. 计算任务完成状态和进度
4. 检查是否满足等级升级条件
5. 按顺序升级到可达到的最高等级
6. 记录升级历史和触发奖励
7. 更新任务进度和身份信息

### 任务完成判断
- **数值类任务**：比较实际值与目标值
- **布尔类任务**：检查状态标识
- **完成模式**：
  - ALL_FINISH：所有任务都必须完成
  - ANY_ONE：任意一个任务完成即可

## 错误处理

### 分层错误处理
- **HTTP层**：返回用户友好的错误信息
- **Service层**：记录详细错误日志
- **数据层**：处理数据库操作异常

### 关键错误场景
- 用户ID为空或无效
- 身份类型不支持
- 企业身份缺少企业代码
- 用户已达到最高等级
- 身份不一致的消息

## 数据一致性

### 事务保证
- 所有身份切换操作在事务中执行
- 等级升级和任务更新原子性操作
- 关联关系更新的一致性保证

### 并发控制
- 防止重复消息处理
- 身份切换的幂等性设计
- 等级升级的顺序性保证

## 性能优化

### 缓存策略
- 身份规则配置缓存
- 用户状态信息缓存
- 多语言文案缓存

### 数据库优化
- 合理的索引设计
- 批量操作优化
- 查询语句优化

## 监控和日志

### 关键指标
- 身份切换成功率
- 等级升级成功率
- 消息处理延迟
- 任务完成率

### 日志记录
- 消息接收和处理日志
- 错误和异常详细日志
- 性能和调试信息日志

## 扩展性设计

### 新身份类型
- 在枚举中添加新身份
- 实现对应的成长规则处理器
- 添加消息处理函数
- 更新API接口支持

### 新任务类型
- 在任务枚举中添加新任务
- 在成长规则处理器中添加处理逻辑
- 更新数据库配置

### 新等级支持
- 在等级枚举中添加新等级
- 更新等级排序配置
- 添加对应的任务配置

这个文档提供了用户成长中心代码的完整技术说明，帮助开发者理解系统架构和实现细节。
