# 个人IB功能测试指南

## 测试前准备

### 1. 数据库初始化
执行SQL脚本初始化个人IB相关数据：
```bash
mysql -h testdb-mysql.fxeyeinterface.com -u wikifx -p usercenter < docs/personal_ib_database_init.sql
```

### 2. 服务启动
确保服务正常启动并能接收Kafka消息。

## API测试用例

### 1. 获取身份规则列表
测试个人IB身份是否出现在规则列表中：

```bash
curl -X GET "http://localhost:8000/v1/app/identity/rule?user_id=test_user_001" \
  -H "Content-Type: application/json"
```

**预期结果**：返回的规则列表中应包含个人IB身份（Identity=5）

### 2. 切换到个人IB身份
测试用户切换到个人IB身份：

```bash
curl -X POST "http://localhost:8000/v1/app/identity/switch" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test_user_001",
    "identity": 5
  }'
```

**预期结果**：返回切换成功，用户身份变更为个人IB

### 3. 获取用户成长详情
验证个人IB用户的等级和任务信息：

```bash
curl -X GET "http://localhost:8000/v1/app/identity/detail?user_id=test_user_001" \
  -H "Content-Type: application/json"
```

**预期结果**：返回个人IB身份的等级信息和相关任务

### 4. 获取升级身份信息
测试个人IB用户的升级提示：

```bash
curl -X GET "http://localhost:8000/v1/app/identity/upgrade?user_id=test_user_001" \
  -H "Content-Type: application/json"
```

**预期结果**：返回下一级别的升级任务信息

## Kafka消息测试

### 1. 身份切换消息
发送身份切换消息到Kafka：

```json
{
  "type": 1,
  "identity": {
    "user_id": "test_user_001",
    "identity": "personal_ib",
    "enterprise_code": ""
  }
}
```

**主题**：`user_growth_msg`

### 2. 个人IB任务消息
发送个人IB任务进度消息：

```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "test_user_001",
    "account_assets": 25000.0
  }
}
```

**主题**：`user_growth_msg`

**预期结果**：用户任务进度更新，满足条件时自动升级

## 数据验证

### 1. 验证身份等级配置
```sql
SELECT * FROM growthcenter_identity_grade WHERE Identity = 5;
```

### 2. 验证任务配置
```sql
SELECT * FROM growthcenter_grade_task WHERE Task IN (500, 501, 502, 503, 504);
```

### 3. 验证用户身份
```sql
SELECT 
    ui.User_Id,
    ig.Identity,
    ig.Grade,
    ui.Status,
    ui.Acquire_Time
FROM growthcenter_user_identity ui
JOIN growthcenter_identity_grade ig ON ui.Identity_Grade_Id = ig.Id
WHERE ui.User_Id = 'test_user_001' AND ig.Identity = 5;
```

### 4. 验证任务进度
```sql
SELECT 
    uts.User_Id,
    uts.Identity,
    uts.Grade,
    gt.Name as TaskName,
    gt.Task as TaskEnum,
    uts.Schedule,
    uts.Is_Finish
FROM growthcenter_user_identity_task_schedule uts
JOIN growthcenter_grade_task gt ON uts.Grade_Task_Id = gt.Id
WHERE uts.User_Id = 'test_user_001' AND uts.Identity = 5
ORDER BY uts.Grade, gt.Task;
```

## 测试场景

### 场景1：新用户选择个人IB身份
1. 用户首次选择个人IB身份
2. 系统自动创建普通等级身份
3. 完成"选择个人IB身份"任务

### 场景2：资产验证升级
1. 用户账户资产达到$20,000
2. 发送个人IB任务消息
3. 系统自动完成资产验证任务
4. 用户保持普通等级（已满足条件）

### 场景3：等级升级
1. 用户账户资产达到$50,000
2. 发送个人IB任务消息
3. 系统自动升级到青铜等级

### 场景4：最高等级
1. 用户账户资产达到$200,000
2. 发送个人IB任务消息
3. 系统自动升级到黄金等级（最高等级）

## 错误处理测试

### 1. 无效身份切换
```bash
curl -X POST "http://localhost:8000/v1/app/identity/switch" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "",
    "identity": 5
  }'
```

**预期结果**：返回用户ID为空的错误

### 2. 无效消息
发送空的用户ID消息：
```json
{
  "type": 6,
  "personal_ib": {
    "user_id": "",
    "account_assets": 25000.0
  }
}
```

**预期结果**：消息处理失败，记录错误日志

## 性能测试

### 1. 并发身份切换
使用多个用户同时切换到个人IB身份，验证系统稳定性。

### 2. 大量消息处理
发送大量个人IB任务消息，验证消息处理性能。

## 注意事项

1. **测试环境**：确保在测试环境中进行，避免影响生产数据
2. **数据清理**：测试完成后清理测试数据
3. **日志监控**：关注应用日志，确保没有错误信息
4. **数据一致性**：验证数据库中的数据一致性
5. **向后兼容**：确保现有功能不受影响
