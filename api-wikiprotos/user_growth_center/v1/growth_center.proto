syntax = "proto3";

package api.user_growth_center.v1;

import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "api/user_growth_center/v1;v1";

message IdentityRuleBaseInfo {
  string name =1 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份名称"}];
  string badge =2 [json_name="badge",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份徽章"}];
  string identity_tips=3 [json_name="identity_tips",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份升级提示词"}];
  string identity_desc=4 [json_name="identity_desc",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份升级描述"}];
  bool  is_acquire=5 [json_name="is_acquire",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否获得"}];
  repeated IdentityGradeInfo identity_grade=6[json_name="identity_grade",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份等级"}];
}

message IdentityGradeInfo {
  string grade_name =1 [json_name="grade_name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级名称"}];
  string grade_desc=2 [json_name="grade_desc",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级升级描述"}];
  bool  is_acquire=3 [json_name="is_acquire",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否获得"}];
  string badge=4 [json_name="badge",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"徽章"}];
  repeated string bg_color=5 [json_name="bg_color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"背景色"}];
  string grade_upgrade_title=6 [json_name="grade_upgrade_title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级升级标题"}];
  repeated  IdentityGradeTask grade_task =7 [json_name="grade_task",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级任务"}];
  repeated string upgrade_success_tips =8 [json_name="upgrade_success_tips",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级成功提示词"}];
  string banner =9 [json_name="banner",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级banner"}];
  string upgrade_success_img =10 [json_name="upgrade_success_img",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级到最高级背景图"}];
  string grade_name_color =11 [json_name="grade_name_color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级名称字体颜色（进度用）"}];
  string grade_process_img =12 [json_name="grade_process_img",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级进度背景图"}];
  string process_name=13 [json_name="process_name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"进度名称"}];
  string banner_bg_color=14 [json_name="banner_bg_color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级banner投影背景"}];
  string grade_name_font_color=15 [json_name="grade_name_font_color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级名称字体颜色（banner用）"}];
  double badge_width=16[json_name="badge_width",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"徽章宽度"}];
  double badge_height=17[json_name="badge_height",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"徽章高度"}];
}

// 身份等级任务
message IdentityGradeTask {
  string task_id=1 [json_name="task_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务Id"}];
  string task_name =2 [json_name="task_name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务名称"}];
  string task_content=3 [json_name="task_content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务内容"}];
  int32  task_type=4 [json_name="task_type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务类型:1 bool类型,2 数值类型"}];
  string  task_target=5 [json_name="task_target",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数值类型任务目标值"}];
  string  task_current=6 [json_name="task_current",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数值类型任务当前进度"}];
  bool is_finish=7 [json_name="is_finish",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否完成"}];
  string task_status_color =8 [json_name="task_finish_color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务状态背景色"}];
  string task_ico =9 [json_name="task_ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务ico"}];
  UserGrowthJumpAddress jump_address=10 [json_name="jump_address",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务地址"}];
  int32 jump_address_type=11 [json_name="jump_address_type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转类型 1 本地跳转 2 外部跳转"}];
  repeated string upgrade_welfare=12 [json_name="upgrade_welfare",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级福利"}];
  repeated  string task_tips=13 [json_name="task_tips",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务提示"}];
}

message GetIdentityRuleRequest {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
}
message GetIdentityRuleReply {
  repeated IdentityRuleBaseInfo rules=1 [json_name="rules",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"规则列表"}];
}

message GetUserGrowthDetailRequest {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户Id"}];
}
message GetUserGrowthDetailReply{
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
  repeated IdentityGradeInfo grade_info =4 [json_name="grade_info",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份等级详情"}];
}

message  GetGrowthCenterEntryRequest {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
}
message GetGrowthCenterEntryReply {
  bool is_show_growth_center =1 [json_name="is_show_growth_center",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示身份成长中心"}];
  bool is_show_grade_rule =2 [json_name="is_show_grade_rule",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示身份等级规则"}];
}

message GetIdentityCarouselRequest {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetIdentityCarouselReply {
  repeated CarouselInfo banners=1 [json_name="banner",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"轮播图列表"}];
  int64 time_span =2 [json_name="time_span",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"轮播图切换时间间隔"}];
}

message  CarouselInfo {
  string name =1 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"轮播图名称"}];
  string img=2 [json_name="img",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"轮播banner"}];
  UserGrowthJumpAddress jump_url =3 [json_name="jump_url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转地址"}];
}

message  GetIdentityShareRequest {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetIdentityShareReply {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string badge=2 [json_name="badge",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份徽章"}];
  string identity=3 [json_name="identity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份"}];
  string  acquire_time =4[json_name="acquire_time",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获取时间"}];
  repeated  string content=5 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文案"}];
  repeated  string footer_content=6 [json_name="footer_content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"底部分享文案"}];
  string footer_qr_image=7 [json_name="footer_qr_image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"底部分享二维码图片"}];
  double badge_width=8[json_name="badge_width",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"徽章宽度"}];
  double badge_height=9[json_name="badge_height",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"徽章高度"}];
}
message  GetUpgradeIdentityRequest {
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}

message  GetUpgradeIdentityReply{
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string name =2 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"名称"}];
  string logo =3 [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"logo"}];
  UserGrowthJumpAddress jump_address=4 [json_name="jump_address",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务地址"}];
}

message PostUserIdentitySwitchRequest{
  string user_id =1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  UserIdentity identity =2 [json_name="identity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份类型",default:"1"}];
}

message PostUserIdentitySwitchReply{
   bool  result =1 [json_name="result",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"切换结果"}];
}


/*============================消息队列==============================*/
message EnterpriseTaskMsg {
  int64 staff_cnt=1 [json_name="staff_cnt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"员工数"}];
  int64 cooperation_cnt=2 [json_name="cooperation_cnt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"合作数"}];
  string user_id=3 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string identity=4 [json_name="identity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份"}];
  int64 is_authenticated=5 [json_name="is_authenticated",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否认证"}];
  string enterpriseCode=6 [json_name="enterprise_code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关联企业Code"}];
}

message  InvestorTaskMsg {
  string user_id=1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  float fund_total=2 [json_name="fund_total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"金额"}];
  bool  is_real_name=3[json_name="is_real_name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否实名认证"}];
  bool  real_flag=4[json_name="real_flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"绑定实盘"}];
  bool  has_order=5[json_name="has_order",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实盘有交易记录"}];
}

message KolTaskMsg {
  string user_id=1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  int64  fans_cnt=2 [json_name="fans_cnt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"粉丝数"}];
  int64 posts_cnt=3 [json_name="posts_cnt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发帖数"}];
}

message PersonalIBTaskMsg {
  string user_id=1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  float account_assets=2 [json_name="account_assets",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"账户资产"}];
}

message UserIdentityMsg {
  string user_id=1 [json_name="user_id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string identity=2 [json_name="identity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份"}];
  string enterpriseCode=3 [json_name="enterprise_code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关联企业Code"}];
}

message  UserGrowthMsg {
    GrowthMsgType type =1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型"}];
    UserIdentityMsg identity=2 [json_name="identity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份切换"}];
    InvestorTaskMsg investor=3 [json_name="investor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"投资者"}];
    KolTaskMsg kol =4 [json_name="kol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"kol"}];
    EnterpriseTaskMsg service=5 [json_name="service",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务商"}];
    EnterpriseTaskMsg trader=6 [json_name="trader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商"}];
    PersonalIBTaskMsg personal_ib=7 [json_name="personal_ib",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"个人IB"}];
}

/*===========================枚举==================================*/

// kafka 消息类型
enum GrowthMsgType {
  UNKNOWN_TYPE=0; // 未知
  SWITCH_IDENTITY_TYPE=1;// 身份切换类型
  INVESTOR_TYPE=2;// 投资者类型
  KOL_TYPE=3; // KOL类型
  SERVICE_PROVIDER_TYPE=4; // 服务商类型
  TRADER_TYPE=5; // 交易商类型
  PERSONAL_IB_TYPE=6; // 个人IB类型
}

// 用户身份
enum UserIdentity {
  UNKNOWN=0; // 未知
  INVESTOR=1; // 投资者
  KOL=2; // KOL
  SERVICE_PROVIDER=3; // 服务商
  TRADER=4;//交易商
  PERSONAL_IB=5; // 个人IB
}

// 用户等级
enum UserGrade {
  UNDEFINED=0; // 未定义
  NORMAL=1; // 普通
  BRONZE=2; // 青铜
  SILVER=3; // 白银
  GOLD=4; //黄金
}

// 用户升级跳转地址
enum UserGrowthJumpAddress {
  UNKNOWN_USER_GROWTH_JUMP_ADDRESS=0; // 未知
  BINDING_MT4_MT5_ACCOUNT=200; //  绑定MT4/MT5账号页面
  QUOTATION_TRADING=210; // 行情交易
  COMMUNITY_PUBLISHING=220; // 社区发布页
  SERVICE_PROVIDER_AUTHENTICATION=230; // 服务商认证页
  STAFF_APPROVAL=240; // 员工审核列表页
  ENTERPRISE_COOPERATION_APPROVAL=250; // 企业合作审核
  REAL_NAME_VERIFICATION=260; // 用户实名认证
}

//
enum TaskEnum {
  UNKNOWN_TASK=0; // 未知

  CHOOSE_INVESTOR=100; //选择身份为投资者
  BINDING_REAL_ACCOUNT=101; // 绑定真实账户
  VERIFY_ACCOUNT_ASSETS=102; // 账户资产验证
  VERIFY_DEPOSIT_SCALE=103; // 入金规模验资
  BIND_MOCK_ACCOUNT=104; // 绑定模拟交易
  USER_AUTHORIZATION=105; // 实名认证--活体验证

  CHOOSE_KOL=200; // 选择身份为KOL
  PUBLISH_CONTENT=201; // 发布优质内容
  KOL_ROUTE=202; // KOL之路

  CREATE_SERVICE_PROVIDER=300; //创建服务商
  CERTIFIED_ENTERPRISE=301; // 认证企业
  ENTERPRISE_STAFF=302; // 企业员工
  ENTERPRISE_COOPERATION=303; // 企业合作

  JOIN_TRADER=400; //加入任意一家交易商企业
  TRADER_ENTERPRISE_STAFF=401; // 企业员工
  TRADER_ENTERPRISE_COOPERATION=402; // 企业合作

  CHOOSE_PERSONAL_IB=500; // 选择个人IB身份
  VERIFY_IB_ACCOUNT_ASSETS=501; // 实盘账户资金
}

// 身份等级模式
enum IdentityGradeMode {
  UNKNOWN_IDENTITY_GRADE_MODE=0; // 未知

  ALL_FINISH=1; // 全部完成
  ANY_ONE=2; // 任意完成一个
}

