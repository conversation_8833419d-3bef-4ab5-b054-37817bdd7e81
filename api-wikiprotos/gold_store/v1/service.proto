syntax = "proto3";

package api.gold_store.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "gold_store/v1/models.proto";
import "gold_store/v1/sign.proto";
import "gold_store/v1/task.proto";
option go_package = "api/gold_store/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  rpc GoldStoreQuickAccess(common.EmptyRequest) returns (GoldStoreQuickAccessReply){
    option (google.api.http) = {get: "/v1/gold_store/quick_access"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "快捷入口",tags: ["金币商城"]};
  }
  rpc MyGoldJump(MyGoldJumpRequest) returns (MyGoldJumpReply){
    option (google.api.http) = {get: "/v1/my_gold_store/jump"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "我的金币跳转",tags: ["金币商城"]};
  }

  // ===========================================================
  // =========================== 商品 ===========================
  // ===========================================================
  // 商品tab
  rpc GoodsTab(GoodsTabRequest) returns (GoodsTabReply) {
    option (google.api.http) = {get: "/v1/goods/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取商品tab",tags: ["商品"]};
  }
  // 商品分页
  rpc GoodsList(GoodsListRequest) returns (GoodsListReply) {
    option (google.api.http) = {get: "/v1/goods/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取商品列表",tags: ["商品"]};
  }
  // 商品详情页
  rpc GetGoodsDetail(GoodsDetailRequest) returns (GoodsDetail) {
    option (google.api.http) = {get: "/v1/goods/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "商品详情",tags: ["商品"]};
  }
  // 精选好物
  rpc BestGoods(BestGoodsRequest) returns (BestGoodsReply) {
    option (google.api.http) = {get: "/v1/goods/best"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "精选好物",tags: ["商品"]};
  }

  // ===========================================================
  // =========================== 订单 ===========================
  // ===========================================================
  // 订单预算
  rpc OrderTotalAmount(OrderTotalAmountRequest) returns (OrderTotalAmountReply) {
    option (google.api.http) = {post: "/v1/order/amount",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "下单价格计算",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 下单预检查
  rpc PreCheck(CreateOrderRequest) returns (PreCheckReply) {
    option (google.api.http) = {post: "/v1/order/precheck",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "下单预检查",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 用户下单
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderReply) {
    option (google.api.http) = {post: "/v1/order",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户下单",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 订单筛选类目列表
  rpc OrderFilter(OrderFilterRequest) returns (OrderFilterReply) {
    option (google.api.http) = {get: "/v1/order/filters"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "订单筛选分类",tags: ["订单"]};
  }
  // 用户订单数量
  rpc GetOrderCount(common.EmptyRequest) returns (OrderCountReply) {
    option (google.api.http) = {get: "/v1/order/count"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户订单数",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 订单tab
  rpc GetOrderTab(GetOrderTabRequest) returns (OrderTabReply) {
    option (google.api.http) = {get: "/v1/order/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取订单tab",tags: ["订单"]};
  }
  // 订单列表
  rpc GetOrderList(OrderListRequest) returns (OrderListReply) {
    option (google.api.http) = {post: "/v1/order/list",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取订单列表",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 订单详情
  rpc GetOrderDetail(OrderDetailRequest) returns (OrderDetail) {
    option (google.api.http) = {get: "/v1/order/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取订单详情",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 订单物流
  rpc GetOrderLogistics(OrderLogisticsRequest) returns (GetOrderLogisticsReply) {
    option (google.api.http) = {get: "/v1/order/logistics"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "订单物流",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 查询用户订单数
  rpc GetUserOrderCount(GetUserOrderCountRequest) returns (GetUserOrderCountReply) {
    option (google.api.http) = {get: "/v1/user/order/count"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "订单物流",tags: ["订单"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 报告订单推送
  rpc ReportOrderPush(ReportOrderPushRequest) returns (ReportOrderPushReply) {
    option (google.api.http) = {post: "/v1/order/report/push",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "报告订单推送",tags: ["订单"]};
  }

  // 快递查询
  rpc QueryExpressInfo(QueryExpressInfoRequest) returns (QueryExpressInfoReply) {
    option (google.api.http) = {post: "/v1/express/query", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "快递查询",tags: ["物流"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 快递100推送回调接口
  rpc ExpressPushCallback(ExpressPushCallbackRequest) returns (ExpressPushCallbackReply) {
    option (google.api.http) = {post: "/v1/express/callback", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "快递100推送回调",tags: ["物流"]};
  }
  // ===========================================================
  // =========================== 签到 ===========================
  // ===========================================================
  // 获取签到信息聚合
  rpc GetSignAggregateInfo(GetSignAggregateInfoRequest) returns (GetSignAggregateInfoReply) {
    option (google.api.http) = {post: "/v1/sign/aggregate-info",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取签到信息聚合",tags: ["签到"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 执行签到
  rpc SignIn(SignInRequest) returns (SignInReply) {
    option (google.api.http) = {post: "/v1/sign/in",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "执行签到",tags: ["签到"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }

  // ===========================================================
  // =========================== 任务 ===========================
  // ===========================================================
  // 获取任务列表
  rpc GetTaskList(GetTaskListRequest) returns (GetTaskListReply) {
    option (google.api.http) = {post: "/v1/task/list",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取任务列表",tags: ["任务"]};
  }
  // 领取奖励
  rpc ReceiveTaskReward(ReceiveTaskRewardRequest) returns (ReceiveTaskRewardReply) {
    option (google.api.http) = {post: "/v1/task/reward",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "领取任务奖励",tags: ["任务"]};
  }
  // 领取任务（如活动任务、限时任务等）
  //  rpc ClaimTask(ClaimTaskRequest) returns (ClaimTaskReply) {
  //    option (google.api.http) = {post: "/v1/task/claim",body:"*"};
  //    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "领取任务",tags: ["任务"]};
  //  }
  // 查询奖励记录
  rpc ListRewardRecords(ListRewardRecordsRequest) returns (ListRewardRecordsReply) {
    option (google.api.http) = {post: "/v1/reward/records",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "查询奖励记录",tags: ["任务"]};
  }
  // 发送任务事件到队列
  rpc SendTaskEventToQueue(TaskEventRequest) returns (TaskEventReply) {
    option (google.api.http) = {post: "/v1/task/event",body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "发送任务事件",tags: ["任务"]};
  }

  // ===========================================================
  // =========================== 用户地址 =======================
  // ===========================================================
  // 获取国家列表
  rpc GetCountryList(common.EmptyRequest) returns (GetCountryListReply) {
    option (google.api.http) = {get: "/v1/country/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取国家列表", tags: ["用户地址"]};
  }
  // 获取用户地址列表
  rpc GetAddressList(common.EmptyRequest) returns (GetAddressListReply) {
    option (google.api.http) = {get: "/v1/user/address/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户地址列表", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 添加用户地址
  rpc AddAddress(Address) returns (AddAddressReply) {
    option (google.api.http) = {post: "/v1/user/address", body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加用户地址", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 修改用户地址
  rpc UpdateAddress(Address) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/user/address/update", body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "修改用户地址", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 获取用户地址详情
  rpc GetAddressDetail(GetAddressDetailRequest) returns (Address) {
    option (google.api.http) = {get: "/v1/user/address/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户地址详情", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 设置用户地址为默认
  rpc SetAddressDefault(SetAddressDefaultRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/user/address/default", body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "设置用户地址为默认", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 删除用户地址
  rpc DeleteAddress(DeleteAddressRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/user/address/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除用户地址", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 用户地址总数
  rpc GetAddressCount(common.EmptyRequest) returns (GetAddressCountReply) {
    option (google.api.http) = {get: "/v1/user/address/count"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户地址总数", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 获取用户购物地址
  rpc GetOrderAddress(common.EmptyRequest) returns (Address) {
    option (google.api.http) = {get: "/v1/user/order/address"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户购物地址", tags: ["用户地址"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // ===========================================================
  // =========================== 礼品卡 ===========================
  // ===========================================================
  rpc GetUserGiftCard(GetUserGiftCardRequest) returns (GetUserGiftCardReply) {
    option (google.api.http) = {get: "/v1/user/gift_card/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户礼品卡", tags: ["礼品卡"]};
  }
  rpc GiftCardRemind(GiftCardRemindRequest) returns (GiftCardRemindReply) {
    option (google.api.http) = {get: "/v1/user/gift_card/remind"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户礼品卡提示", tags: ["礼品卡"]};
  }
}

// ==================================== 金币商城 =======================================
enum QuicAccessJumpType {
  QuicAccessJumpTypeGiftCard = 0; // 礼品卡
  QuicAccessJumpTypeEarnPoint = 1; // 赚积分
  QuicAccessJumpTypePointsStore = 2; // 积分商城
  QuicAccessJumpTypeAddressManagement = 3; // 地址管理
}
message QuickAccessItem {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快捷入口名称"}];
  string icon = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快捷入口图标"}];
  QuicAccessJumpType jump_type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳转类型:0:礼品卡；1：赚金币；2：金币充值；3：地址管理"}];
}
message GoldStoreQuickAccessReply {
  repeated QuickAccessItem items = 1;
}

message MyGoldJumpRequest {
  string timezone = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时区"}];
}
message MyGoldJumpReply {
  bool jump_new = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否跳转新地址"}];
  bool sign = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否签到过"}];
}

// ==================================== 商品 =======================================
enum GoodsLabelType {
  GoodsLabelImage = 0; // 图片
  GoodsLabelText = 1; // 文本
}
message GoodsLabel {
  GoodsLabelType type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tag类型:0：图片；1：文本"}];
  string image = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
  string text = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文本"}];
  string color = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文本颜色"}];
  string id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}
message GoodsListItem {
  string goods_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  Image image = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品图片"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string title = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品标题"}];
  float price = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品价格"}];
  string price_show = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品价格"}];
  repeated GoodsLabel labels = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品标签"}];
  GoodsStatus status = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品状态:0:下架；1：上架"}];
  GoodsCategory category =9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品分类"}];

}

message GoodsDetail {
  string goods_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string title = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品标题"}];
  string description = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品描述"}];
  float price = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品价格"}];
  int32 sales = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品销��"}];
  bool free_shipping = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否免运费"}];
  string selected_sku_id = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "已选商品sku id"}];
  string selected_spec_desc = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "已选商品规格描述"}];
  GoodsCategory category = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品分类：0：实物商品；1：VIP；2：VPS"}];
  Image image = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品图片"}];
  bool disable = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品是否不可用，如果为true表示当前是商品不可购买"}];
  GoodsStatus status = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品状态:0：下架；1：上架"}];
  repeated GoodsLabel best_sellers = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品畅销标签"}];
  repeated GoodsLabel labels = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品标签"}];
  repeated Image carousels = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品轮播图"}];
  repeated Image details = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品详情图"}];
  repeated GoodsSpec specs = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格"}];
  repeated GoodsSku skus = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品sku"}];
  int32 buy_limit = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品限购数量:0：不限购"}];
}
message GoodsTabRequest {}
message GoodsTab {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab名称"}];
  bool selected = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否选中"}];
}
message GoodsTabReply {
  repeated GoodsTab tabs = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品tab"}];
  repeated GoodsListItem goods = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品列表"}];
}

message GoodsListRequest {
  string tab_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabID"}];
  int32 size = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数据大小"}];
  int32 page = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页数"}];
}
message GoodsListReply {
  repeated GoodsListItem goods = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品列表"}];
}

message GoodsDetailRequest {
  string goods_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
}


message BestGoodsRequest {}
message BestGoodsReply {
  repeated GoodsListItem goods = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品列表"}];
}
// ============================================== 地址=================================================
message CountryInfo {
  string code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家名称"}];
}
message GetCountryListReply {
  repeated CountryInfo items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家列表"}];
}

message GetAddressListReply {
  repeated Address items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址列表"}];
}

message AddAddressReply {
  int32 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址ID"}];
  string address_show = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示名称，返回的时候展示使用"}];
}

message SetAddressDefaultRequest{
  int32 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址ID"}];
}

message DeleteAddressRequest {
  int32 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址ID"}];
}

message GetAddressDetailRequest {
  int32 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址ID"}];
}

message GetAddressCountReply {
  int32 count = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址数量"}];
}


// ==================================== 订单 ====================================
message OrderTotalAmountRequest {
  PaymentMethod method = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购"}];
  string operation_ticket = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "如果是礼品卡和用户任务，传递票据信息"}];
  string goods_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  int32 address_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "如果是实物商品需要传递用户选中的地址ID"}];
  int32 quantity = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品数量"}];
  repeated SpecSelected specs = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "选中的商品规格"}];
}
message OrderTotalAmountReply {
  float total_amount = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单总价"}];
  float shipping_fee = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "运费"}];
  Address address = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收货地址"}];
  string code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "错误码"}];
}

message SpecSelected {
  string spec_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格ID"}];
  string value_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值ID"}];
}
message CreateOrderRequest {
  PaymentMethod method = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购"}];
  string operation_ticket = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "如果是礼品卡和用户任务，传递票据信息"}];
  string goods_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  int32 address_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "如果是实物商品需要传递用户选中的地址ID"}];
  int32 quantity = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品数量"}];
  float total_amount = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "预支付价格"}];
  repeated SpecSelected specs = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "选中的商品规格"}];
}
message PreCheckReply {
  float total_amount = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单总价"}];
  float shipping_fee = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "运费"}];
  Address address = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收货地址"}];
  GoodsDetail goods_detail = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品详情"}];
}
message CreateOrderReply {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单ID"}];
}
message OrderFilterRequest {}
message OrderFilterValue {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示名称"}];
}
message OrderFilterGroup {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示名称"}];
  repeated OrderFilterValue values = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "过滤值"}];
}
message OrderFilterReply {
  repeated OrderFilterGroup groups = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单来源分类"}];
}

message OrderCountReply {
  int32 delivery = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "待收货订单数量"}];
  int32 complete = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "已完成订单数量"}];
}

enum OrderPaymentMethodIcon {
  OrderPaymentMethodIcon_UNKNOWN = 0; // 未知
  OrderPaymentMethodIcon_GOLD = 1; // 金币
  OrderPaymentMethodIcon_POINTS = 2; // 积分
}
message OrderBase {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单ID"}];
  string goods_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  string sku_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "skuID"}];
  string goods_name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  Image image = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品图片"}];
  OrderSource source = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单来源：0：金币商城；1：VIP；2：VPS；3：报告；4：展会；5：EA"}];
  string source_show = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单来源展示"}];
  PaymentMethod payment_method = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购"}];
  string total_amount_show = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单金额展示"}];
  string spec_desc = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格描述(前端展示就行)"}];
  float price = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品单价"}];
  string price_show = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品单价展示"}];
  int32 quantity = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品数量"}];
  float total_amount = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品总金额"}];
  OrderStatus status = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态:0：未知状态；1：待支付；2：已支付；3：已取消；4：待收货；5：已完成"}];
  string status_show = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态显示"}];
  OrderPaymentMethodIcon payment_method_icon = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式图标"}];
}

message OrderTab {
  OrderStatus status = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab分类：0：全部；1：待支付；2：待收货；3：已取消；4：已完成"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab名称"}];
  //  bool selected = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否选中"}];
}
message GetOrderTabRequest {
  int32 size = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量量"}];
}
message OrderTabReply {
  repeated OrderTab tabs = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单tab"}];
  //  repeated OrderBase orders = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单列表"}];
}

message OrderFilter {
  string group_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分类ID"}];
  repeated string values_ids = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值ID"}];
}
message OrderListRequest {
  OrderStatus status = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab分类：0：全部；1：待支付；2：待收货；3：已取消；4：已完成"}];
  int32 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页大小,默认10"}];
  string offset = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏移;默认空字符串"}];
  repeated OrderFilterGroup filters = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "过滤条件"}];
}
message OrderListReply {
  repeated OrderBase orders = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单列表"}];
  string offset = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页偏移"}];
}



message OrderDetailRequest {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单ID"}];
}
message VPSExtra {
  string effective_time = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "生效时间"}];
  string expiration_time = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "到期时间"}];
  string open_state = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开通状态"}];
  string server_language = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "服务器语言"}];
  string server_city = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "服务器城市"}];
  string server_account = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "服务器账号"}];
}
message ReportExtra {
  string send_email = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发送邮箱"}];
  string language = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
  string send_state = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发送状态"}];
  string generate_state = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "生成状态"}];
  bool go_details = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否可查看详情"}];
}
message ExhibitionUser {
  string user_name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "姓名"}];
  string email = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string phone_number = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
}
message ExhibitionExtra {
  string title = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会标题"}];
  string description = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会描述"}];
  string ticket_type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "门票类型"}];
  bool use_exchange = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否使用兑换码"}];
  repeated ExhibitionUser users = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户信息"}];
}
message OrderDetail {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单ID"}];
  string goods_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  string sku_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "skuID"}];
  string goods_name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  Image image = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品图片"}];
  GoodsCategory category = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品分类：0：实物商品；1：VIP；2：VPS"}];
  string email = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string symbol = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种符号"}];
  OrderSource source = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单来源:0：金币商城；1：报告；2：VIP；3：VPS"}];
  string source_show = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单来源展示"}];
  string spec_desc = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格描述(前端展示就行)"}];
  float price = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品单价"}];
  string price_show = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品单价展示"}];
  int32 quantity = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品数量"}];
  float shipping_fee = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "运费"}];
  float total_amount = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单总金额"}];
  string total_amount_show = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单金额展示"}];
  float total_pay = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付总金额"}];
  string total_pay_show = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付金额展示"}];
  OrderStatus status = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态:0：未知状态；1：待支付；2：已支付；3：已取消；4：待收货；5：已完成"}];
  string status_show = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态显示"}];
  PaymentMethod payment_method = 28[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：现金"}];
  int64 order_time = 29[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下单时间(单位秒)"}];
  int64 pay_time = 30[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付时间(单位秒)"}];
  string delivery_method = 31[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "配送方式"}];
  Address address = 32[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收货地址"}];
  LogisticStep current_delivery = 33[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前配送步骤"}];
  VPSExtra vps_extra = 34[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "VPS额外信息"}];
  ReportExtra report_extra = 35[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "上报额外信息"}];
  ExhibitionExtra exhibition_extra = 36[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会额外信息"}];
  repeated SpecSelected selected_specs = 37[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "选中的商品规格"}];
  OrderPaymentMethodIcon payment_method_icon = 38[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式图标"}];
}

message OrderLogisticsRequest {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单号"}];
}
message GetOrderLogisticsReply {
  string carrier_name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流公司名称"}];
  string carrier_icon = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流公司图标"}];
  string tracking_no = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流单号"}];
  Address address = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收货地址"}];
  repeated LogisticStep steps = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流节点"}];
}

message GetUserOrderCountRequest {
  string user_id = 1;
}
message GetUserOrderCountReply {
  int32 total = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总订单数"}];
  int32 store_total = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "金币商城订单数"}];
}

message ReportOrderPushRequest {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户id"}];
  string order_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单id"}];
  string trader_code = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  int32 report_type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告类型:1:信用报告；2：研究报告"}];
  string order_time = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下单时间（单位秒）"}];
  float price = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "价格"}];
  int32 symbol_type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种类型:1:金币;2:美元"}];
  int32 report_language = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告语言:1:中文简体；2:英语；3：中文繁体"}];
  int32 pay_type = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付类型:0:其他；1:金币"}];
}
message ReportOrderPushReply {}

// ==================================== 礼品卡 ====================================
enum GiftCardStatus {
  GiftCardStatusUnknown = 0; // 未知状态
  GiftCardStatusEnable = 1; // 可用
  GiftCardStatusNotReady = 2; // 未到开始时间
  GiftCardStatusExpired = 3; // 已过期
  GiftCardStatusUsed = 4; //  已使用
}
message GiftCard {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡名称"}];
  Image image = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡图片"}];
  int64 start_time = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  int64 end_time = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  GiftCardStatus status = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡状态：0：未知状态；1：未到可用时间；2：可用；3：已过期；4：已使用"}];
  string status_stamp =7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不可用状态印章"}];
  string goods_id =8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "产品Id"}];
}

enum UserGiftCardTabType {
  UserGiftCardTabTypeALL = 0; // 全部
  UserGiftCardTabTypeENABLE = 1; // 可用
  UserGiftCardTabTypeDISABLE = 2; // 不可用
}

message GetUserGiftCardRequest {
  UserGiftCardTabType status = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡tab类型：0：全部；1：可用；2：不可用"}];
  int32 size = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数据大小"}];
  int32 page = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页数"}];
}
message GetUserGiftCardReply {
  repeated GiftCardTab tabs = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡tab列表"}];
  repeated GiftCard gift_cards = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡列表"}];
}
message GiftCardTab{
  string name = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡tab类型"}];
  UserGiftCardTabType value = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡tab类型的值"}];
  int32 count = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量"}];
}

message GiftCardRemindRequest{}
message GiftCardRemindReply{
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡名称"}];
  Image image = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "礼品卡图片"}];
  int64 start_time = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  int64 end_time = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  string goods_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "产品Id"}];
  bool is_have_gift = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否有礼品"}];
}
// ===========================================================
// =========================== 快递查询 ===========================
// ===========================================================

message QueryExpressInfoRequest {
  string tracking_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快递单号"}];
  string phone = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收件人或寄件人手机号）"}];
}

message QueryExpressInfoReply {
  string tracking_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快递单号"}];
  string carrier_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快递公司编码"}];
  string carrier_name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快递公司名称"}];
  LogisticStepStatus status = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流状态"}];
  bool is_completed = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否已完成"}];
  string last_update = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后更新时间"}];
  repeated ExpressTrace traces = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流轨迹"}];
}

message ExpressTrace {
  string time = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间"}];
  string context = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "描述"}];
  string location = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地点"}];
  LogisticStepStatus status = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态"}];
}

// ===========================================================
// =========================== 快递推送回调 ===========================
// ===========================================================

message ExpressPushCallbackRequest {
  string sign = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签名"}];
  string param = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推送数据"}];
}

message ExpressPushCallbackReply {
  bool result = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "处理结果"}];
  string return_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "返回代码"}];
  string message = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "返回消息"}];
}
