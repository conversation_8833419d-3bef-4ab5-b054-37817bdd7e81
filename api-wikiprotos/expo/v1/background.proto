syntax = "proto3";

package api.expo.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "expo/v1/models.proto";

option go_package = "api/expo/v1;v1";

service Background {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // ===========================================================
  // =========================== 图片直播 =======================
  // ===========================================================
  rpc ListLiveImage(ListLiveImageRequest) returns (ListLiveImageReply) {
    option (google.api.http) = {get: "/v1/admin/image/live/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询图片直播列表",tags: ["图片直播"]};
  }
  rpc AddLiveImage(AddLiveImageRequest) returns (AddLiveImageReply) {
    option (google.api.http) = {post: "/v1/admin/image/live/add", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加展会图片",tags: ["图片直播"],description: "为指定展会添加图片"};
  }
  rpc DeleteLiveImage(DeleteLiveImageRequest) returns (DeleteLiveImageReply) {
    option (google.api.http) = {post: "/v1/admin/image/live/delete", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除展会图片",tags: ["图片直播"],description: "删除指定的展会图片"};
  }
  rpc SyncLiveImages(SyncLiveImagesRequest) returns (SyncLiveImagesReply) {
    option (google.api.http) = {post: "/v1/admin/live/sync/images", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "手动同步展会图片到人员库",tags: ["图片直播"],description: "手动触发展会图片同步到腾讯云人员库，异步执行，立即返回任务ID"};
  }
  // 查询展会图片同步状态
  rpc GetSyncStatus(GetSyncStatusRequest) returns (GetSyncStatusReply) {
    option (google.api.http) = {get: "/v1/expo/sync-status"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "查询展会图片同步状态",tags: ["图片直播"],description: "查询展会图片同步任务的详细状态和进度信息"};
  }
  // ===========================================================
  // =========================== 嘉宾 ===========================
  // ===========================================================
  rpc AddGuest(Guest) returns (AddGuestReply) {
    option (google.api.http) = {post: "/v1/admin/guest/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增嘉宾",tags: ["嘉宾"]};
  }
  rpc GetGuest(GetGuestRequest) returns (Guest) {
    option (google.api.http) = {get: "/v1/admin/guest/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询嘉宾",tags: ["嘉宾"]};
  }
  rpc UpdateGuest(Guest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/guest/update" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新嘉宾",tags: ["嘉宾"]};
  }
  rpc SetGuestEnable(SetGuestEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/guest/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置嘉宾状态",tags: ["嘉宾"]};
  }
  rpc ListGuest(ListGuestRequest) returns (ListGuestReply) {
    option (google.api.http) = {get: "/v1/admin/guest/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询嘉宾列表",tags: ["嘉宾"]};
  }
  rpc DeleteGuest(DeleteGuestRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/guest/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除嘉宾",tags: ["嘉宾"]};
  }
  // ===========================================================
  // =========================== 展会社区 =======================
  // ===========================================================
  rpc AddExpoCommunity(ExpoCommunity) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/expo/community/add" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增展会",tags: ["展会社区"]};
  }
  rpc GetExpoCommunity(GetExpoCommunityRequest) returns (ExpoCommunity) {
    option (google.api.http) = {get: "/v1/admin/expo/community/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询展会",tags: ["展会社区"]};
  }
  rpc UpdateExpoCommunity(ExpoCommunity) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/community/update" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展会",tags: ["展会社区"]};
  }
  rpc SetExpoCommunityEnable(SetExpoCommunityEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/community/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会状态",tags: ["展会社区"]};
  }
  rpc DeleteExpoCommunity(DeleteExpoCommunityRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/community/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会",tags: ["展会社区"]};
  }
  // ===========================================================
  // =========================== 展会会场 =======================
  // ===========================================================
  rpc AddHall(ExpoHall) returns (AddGuestReply) {
    option (google.api.http) = {post: "/v1/admin/expo/hall/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增会场",tags: ["会场"]};
  }
  rpc GetHall(GetHallRequest) returns (ExpoHall) {
    option (google.api.http) = {get: "/v1/admin/expo/hall/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询会场",tags: ["会场"]};
  }
  rpc UpdateHall(ExpoHall) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/hall/update" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新会场",tags: ["会场"]};
  }
  rpc SetHallEnable(SetHallEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/hall/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置会场状态",tags: ["会场"]};
  }
  rpc ListHall(ListHallRequest) returns (ListHallReply) {
    option (google.api.http) = {get: "/v1/admin/expo/hall/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询会场列表",tags: ["会场"]};
  }
  rpc DeleteHall(DeleteHallRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/hall/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除会场",tags: ["会场"]};
  }
  // ===========================================================
  // =========================== 展会嘉宾 =======================
  // ===========================================================
  rpc AddExpoGuest(ExpoGuest) returns (AddExpoGuestReply) {
    option (google.api.http) = {post: "/v1/admin/expo/guest/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增展会嘉宾",tags: ["展会嘉宾"]};
  }
  rpc GetExpoGuest(GetExpoGuestRequest) returns (ExpoGuest) {
    option (google.api.http) = {get: "/v1/admin/expo/guest/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会嘉宾",tags: ["展会嘉宾"]};
  }
  rpc ListExpoGuest(ListExpoGuestRequest) returns (ListExpoGuestReply) {
    option (google.api.http) = {get: "/v1/admin/expo/guest/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询展会嘉宾",tags: ["展会嘉宾"]};
  }
  rpc DeleteExpoGuest(DeleteExpoGuestRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/guest/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会嘉宾",tags: ["展会嘉宾"]};
  }
  // ===========================================================
  // =========================== 展会议程 =======================
  // ===========================================================
  rpc AddExpoSchedule(ExpoScheduleInfo) returns (AddExpoScheduleReply) {
    option (google.api.http) = {post: "/v1/admin/expo/schedule/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会议程",tags: ["展会议程"]};
  }
  rpc GetExpoSchedule(GetExpoScheduleRequest) returns (ExpoScheduleInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/schedule/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会议程",tags: ["展会议程"]};
  }
  rpc UpdateExpoSchedule(ExpoScheduleInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/schedule/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展会议程",tags: ["展会议程"]};
  }
  rpc ListExpoSchedule(ListExpoScheduleRequest) returns (ListExpoScheduleReply) {
    option (google.api.http) = {get: "/v1/admin/expo/schedule/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会议程列表",tags: ["展会议程"]};
  }
  rpc DeleteExpoSchedule(DeleteExpoScheduleRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/schedule/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会议程",tags: ["展会议程"]};
  }
  // ===========================================================
  // =========================== 展会参展商 =========================
  // ===========================================================
  rpc AddExpoExhibitor(ExpoExhibitorInfo) returns (AddExpoExhibitorReply) {
    option (google.api.http) = {post: "/v1/admin/expo/exhibitor/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展商",tags: ["参展商"]};
  }
  rpc GetExpoExhibitor(GetExpoExhibitorRequest) returns (ExpoExhibitorInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展商详情",tags: ["参展商"]};
  }
  rpc UpdateExpoExhibitor(ExpoExhibitorInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/exhibitor/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展商",tags: ["参展商"]};
  }
  rpc SetExpoExhibitorEnable(SetExpoExhibitorEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/exhibitor/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展商状态",tags: ["参展商"]};
  }
  rpc ListExpoExhibitor(ListExpoExhibitorRequest) returns (ListExpoExhibitorReply) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展商列表",tags: ["参展商"]};
  }
  rpc DeleteExpoExhibitor(DeleteExpoExhibitorRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/exhibitor/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展商",tags: ["参展商"]};
  }
  // ===========================================================
  // =========================== 展会指南 =======================
  // ===========================================================
  rpc AddExpoGuide(ExpoGuideInfo) returns (AddExpoGuideReply) {
    option (google.api.http) = {post: "/v1/admin/expo/guide/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会指南",tags: ["展会指南"]};
  }
  rpc GetExpoGuide(GetExpoGuideRequest) returns (ExpoGuideInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/guide/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会指南详情",tags: ["展会指南"]};
  }
  rpc UpdateExpoGuide(ExpoGuideInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/guide/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改展会指南",tags: ["展会指南"]};
  }
  rpc SetExpoGuideEnable(SetExpoGuideEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/guide/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会指南状态",tags: ["展会指南"]};
  }
  rpc ListExpoGuide(ListExpoGuideRequest) returns (ListExpoGuideReply) {
    option (google.api.http) = {get: "/v1/admin/expo/guide/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会指南列表",tags: ["展会指南"]};
  }
  rpc DeleteExpoGuide(DeleteExpoGuideRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/guide/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会指南",tags: ["展会指南"]};
  }
  // ===========================================================
  // =========================== 合作伙伴 =======================
  // ===========================================================
  rpc GetExpoPartnerType(GetExpoPartnerTypeRequest) returns (GetExpoPartnerTypeReply) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/type"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取合作伙伴类型",tags: ["合作伙伴"]};
  }
  rpc AddExpoPartner(ExpoPartnerInfo) returns (AddExpoPartnerReply) {
    option (google.api.http) = {post: "/v1/admin/expo/partner/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加合作伙伴",tags: ["合作伙伴"]};
  }
  rpc GetExpoPartner(GetExpoPartnerRequest) returns (ExpoPartnerInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会合作伙伴详情",tags: ["合作伙伴"]};
  }
  rpc UpdateExpoPartner(ExpoPartnerInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/partner/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改合作伙伴",tags: ["合作伙伴"]};
  }
  rpc SetExpoPartnerEnable(SetExpoPartnerEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/partner/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置合作伙伴状态",tags: ["合作伙伴"]};
  }
  rpc ListExpoPartner(ListExpoPartnerRequest) returns (ListExpoPartnerReply) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取合作伙伴列表",tags: ["合作伙伴"]};
  }
  rpc DeleteExpoPartner(DeleteExpoPartnerRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/partner/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除合作伙伴",tags: ["合作伙伴"]};
  }
  // ===========================================================
  // =========================== 展会回顾 =======================
  // ===========================================================
  rpc AddExpoReview(ExpoReviewInfo) returns (AddExpoReviewReply) {
    option (google.api.http) = {post: "/v1/admin/expo/review/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会回顾",tags: ["展会回顾"]};
  }
  rpc GetExpoReview(GetExpoReviewRequest) returns (ExpoReviewInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/review/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会展会回顾详情",tags: ["展会回顾"]};
  }
  rpc UpdateExpoReview(ExpoReviewInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/review/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改展会回顾",tags: ["展会回顾"]};
  }
  rpc SetExpoReviewEnable(SetExpoReviewEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/review/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会回顾状态",tags: ["展会回顾"]};
  }
  rpc ListExpoReview(ListExpoReviewRequest) returns (ListExpoReviewReply) {
    option (google.api.http) = {get: "/v1/admin/expo/review/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会回顾列表",tags: ["展会回顾"]};
  }
  rpc DeleteExpoReview(DeleteExpoReviewRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/review/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会回顾",tags: ["展会回顾"]};
  }
  // ===========================================================
  // =========================== 展会直播 =======================
  // ===========================================================
  rpc AddExpoLive(ExpoLive) returns (AddExpoLiveReply) {
    option (google.api.http) = {post: "/v1/admin/expo/live/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会直播",tags: ["展会直播"]};
  }
  rpc GetExpoLive(GetExpoLiveRequest) returns (ExpoLive) {
    option (google.api.http) = {get: "/v1/admin/expo/live/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会直播详情",tags: ["展会直播"]};
  }
  rpc UpdateExpoLive(ExpoLive) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/live/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改展会直播",tags: ["展会直播"]};
  }
  rpc SetExpoLiveEnable(SetExpoLiveEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/live/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会直播状态",tags: ["展会直播"]};
  }
  rpc ListExpoLive(ListExpoLiveRequest) returns (ListExpoLiveReply) {
    option (google.api.http) = {get: "/v1/admin/expo/live/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会直播列表",tags: ["展会直播"]};
  }
  rpc DeleteExpoLive(DeleteExpoLiveRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/live/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会直播",tags: ["展会直播"]};
  }

  // 展会照片上传与人脸识别
  rpc FacePhotoUpload(FacePhotoUploadRequest) returns (FacePhotoUploadReply) {
    option (google.api.http) = {post: "/v1/face/photosUpload", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "展会照片上传与人脸识别",tags: ["人脸识别"],description: "后台管理接口，上传展会照片并进行人脸检测入库"};
  }
  // 创建人员库
  rpc FaceGroupCreate(FaceGroupCreateRequest) returns (FaceGroupCreateReply) {
    option (google.api.http) = {post: "/v1/face/createGroups", body: "*"};
  }
  // 获取人员库信息
  rpc GetFaceGroupInfo(FaceGroupInfoRequest) returns (FaceGroupInfo) {
    option (google.api.http) = {post: "/v1/face/groupsInfo", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取人员库信息",tags: ["人脸识别"],description: "根据人员库ID获取详细信息"};
  }
  // 获取人员库列表
  rpc FaceGroupList(FaceGroupListRequest) returns (FaceGroupListReply) {
    option (google.api.http) = {post: "/v1/face/groupsList", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取人员库列表",tags: ["人脸识别"],description: "分页获取人员库列表，支持场景类型筛选"};
  }
  // 根据展会ID获取人员库
  rpc FaceGroupsByExpo(FaceGroupsByExpoRequest) returns (FaceGroupInfo) {
    option (google.api.http) = {post: "/v1/face/groupsByExpo", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据场景获取人员库",tags: ["人脸识别"],description: "根据展会ID等场景信息获取对应的人员库"};
  }
  // 照片列表
  rpc FacesPhotoList(PhotoFacesRequest) returns (PhotoFacesReply) {
    option (google.api.http) = {post: "/v1/face/PhotoList", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据场景获取人员库",tags: ["人脸识别"],description: "根据展会ID等场景信息获取对应的人员库"};
  }
}



// ===========================================================
// =========================== 嘉宾 ===========================
// ===========================================================
message GuestLanguage {
  string description = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "介绍"}];
  string label = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
}
message Guest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾id"}];
  string avatar = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称"}];
  string wiki_number = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号"}];
  repeated int64 expo_ids = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关联展会ID"}];
  string phone_area_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号区域码"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string whats_app = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "WhatsApp"}];
  string wechat = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "微信"}];
  string facebook = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Facebook"}];
  string twitter = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Twitter"}];
  string linkedin = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "LinkedIn"}];
  string instagram = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Instagram"}];
  string telegram = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Telegram"}];
  string youtube = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Youtube"}];
  string reddit = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Reddit"}];
  string tiktop = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Tiktop"}];
  bool enable = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Enable"}];
  int64 created_at = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  int64 updated_at = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  string creator = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  map<string,GuestLanguage> languages = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
}
message AddGuestReply {
  int64 id = 1;
}

message SetGuestEnableRequest {
  int64 id = 1;
  bool enable = 2;
}

message GetGuestRequest {
  int64 id = 1;
}

message ListGuestRequest {
  int32 page = 1;
  int32 size = 2;
}
message ListGuestReply {
  repeated Guest guests = 1;
}

message DeleteGuestRequest {
  int64 id = 1;
}

// ===========================================================
// =========================== 展会社区 =======================
// ===========================================================

message ExpoCommunityLanguage {
  string description = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "介绍"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
}

message ExpoCommunity {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  map<string,ExpoCommunityLanguage> languages = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会多语言"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  int64 updated_at = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  string creator = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建者"}];
}

message GetExpoCommunityRequest {
  int64 expo_id = 1;
}

message SetExpoCommunityEnableRequest {
  int64 expo_id = 1;
  bool enable = 2;
}

message DeleteExpoCommunityRequest {
  int64 expo_id = 1;
}

// ===========================================================
// =========================== 展会会场 =======================
// ===========================================================
enum ExpoHallType {
  ExpoHallType_MAIN = 0; // 主会场
  ExpoHallType_SUB = 1; // 分会场
}

message ExpoHallLanguage {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场名称"}];
}
message ExpoHall {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  ExpoHallType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场类型；0：主会场；1：分会场"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场名称"}];
  bool enable = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  map<string,ExpoHallLanguage> languages = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
}

message GetHallRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message SetHallEnableRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListHallRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小"}];
}
message ListHallReply {
  repeated ExpoHall items = 1;
}

message DeleteHallRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message ExpoGuest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID,新增时不用传递"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 guest_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾ID"}];
  string avatar = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像,新增时不用传递"}];
  string name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称,新增时不用传递"}];
  string wiki_number = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号,新增时不用传递"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号,新增时不用传递"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱,新增时不用传递"}];
  bool enable = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Enable,新增时不用传递"}];
  int64 created_at = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间,新增时不用传递"}];
  string creator = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人,新增时不用传递"}];
}
message AddExpoGuestReply {
  int64 id = 1;
}

message GetExpoGuestRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会访客关联ID"}];
}
message GetExpoGuestReply {
  Guest guest = 1;
}

message ListExpoGuestRequest {
  int64 expo_id = 1;
  int32 page = 2;
  int32 size = 3;
}
message ListExpoGuestReply {
  repeated ExpoGuest guests = 1;
}

message DeleteExpoGuestRequest {
  int64 expo_id = 1;
  int64 id = 2;
}

enum ScheduleType {
  ScheduleType_PERSONAL = 0;// 个人演讲
}

message ExpoScheduleLanguage {
  string theme = 1;
}
message ExpoScheduleSpeaker {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾ID，保存时必传"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称,新增时不用传递"}];
  string wiki_number = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号,新增时不用传递"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号,新增时不用传递"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱,新增时不用传递"}];
}
message ExpoScheduleInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID，创建时不用传递"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 hall_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场ID"}];
  ScheduleType type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型"}];
  string theme = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "主题"}];
  ExpoScheduleSpeaker host = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "主持人，保存时只传ID"}];
  repeated ExpoScheduleSpeaker speakers = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲嘉宾"}];
  int64 start = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  int64 end = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  bool enable = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  map<string,ExpoScheduleLanguage> languages = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
}

message AddExpoScheduleReply {
  int64 id = 1;
}

message GetExpoScheduleRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message ListExpoScheduleRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小"}];
}

message ListExpoScheduleReply {
 repeated ExpoScheduleInfo schedules = 1;
}

message DeleteExpoScheduleRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message ExhibitorEmployeeInfo {
  string avatar = 1;
  string name = 2;
  string wiki_number = 3;
}

message ExpoExhibitorLanguage {}
message ExpoExhibitorInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 hall_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场ID"}];
  string trader_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商编码"}];
  string trader_name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string trader_logo = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string trader_min_logo = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商小logo"}];
  SponsorLevel sponsor_level = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级"}];
  string booth_length = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位长度"}];
  string booth_width = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位宽度"}];
  string booth_height = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位高度"}];
  string booth = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位"}];
  string contact = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "联系人"}];
  string phone_area_code = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "电话区域"}];
  string phone = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "电话"}];
  string email = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  repeated ExhibitorEmployeeInfo employees = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工"}];
  int64 rank = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序"}];
}
message AddExpoExhibitorReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoExhibitorRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoExhibitorEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListExpoExhibitorRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message ListExpoExhibitorReply {
  repeated ExpoExhibitorInfo exhibitors = 1;
}

message DeleteExpoExhibitorRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message ExpoGuideInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "指南ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string map_url = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地图url"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message AddExpoGuideReply {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}
message GetExpoGuideRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoGuideEnableRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListExpoGuideRequest {
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ListExpoGuideReply {
  repeated ExpoGuideInfo items = 1;
}

message DeleteExpoGuideRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message GetExpoPartnerTypeRequest {}
message ExpoPartnerTypeItem {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
}
message GetExpoPartnerTypeReply {
  repeated ExpoPartnerTypeItem items = 1;
}

message ExpoPartnerInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string website = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "网址"}];
  int32 rank = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序"}];
  string type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型id"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message AddExpoPartnerReply {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoPartnerRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoPartnerEnableRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListExpoPartnerRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int64 type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型id"}];
  string keyword = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关键字"}];
}
message ListExpoPartnerReply {
  repeated ExpoPartnerInfo partners = 1;
}

message DeleteExpoPartnerRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

enum ExpoReviewType {
  REVIEW_TYPE_VIDEO = 0; // 视频
  REVIEW_TYPE_PICTURE = 1; // 图片
}
message ExpoReviewLanguage {
  string description = 1;
}
message ExpoReviewInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  ExpoReviewType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型"}];
  string video_url = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频地址"}];
  repeated string image_url = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
  map<string, ExpoReviewLanguage> languages = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
  int64 file_count = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文件大小"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message AddExpoReviewReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoReviewRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message SetExpoReviewEnableRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}
message ListExpoReviewRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message ListExpoReviewReply {
  repeated ExpoReviewInfo items = 1;
}

message DeleteExpoReviewRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message ExpoLive {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 level = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "级别"}];
  string cover = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "封面"}];
  string url = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播地址"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}
message AddExpoLiveReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoLiveRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoLiveEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}
message ListExpoLiveRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message ListExpoLiveReply {
  repeated ExpoLive lives = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
}

message DeleteExpoLiveRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片id"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
}
message adminLiveImage {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  repeated string urls = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
}

message ListLiveImageRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
}

message ListLiveImageReply {
  repeated adminLiveImage list = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片列表"}];
  int32 page = 2;      // 页码，从1开始
  int32 size = 3;      // 每页数量
}

message AddLiveImageRequest {
  int64 expo_id = 1;    // 展会ID
  repeated string  image_url_list = 2;  // 图片地址
}

message AddLiveImageReply {
  int64 expo_id = 1;    // 展会ID
  int64 submit_num = 2; //添加数量
  int64 fail_num =3; //失败数量
}

message DeleteLiveImageRequest {
  uint64 image_id = 1;   // 图片ID
}

message DeleteLiveImageReply {
  string message = 1;    // 提示信息
}

message SyncLiveImagesRequest {
  int64 expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "要同步图片的展会ID"}]; // 展会ID
}

message SyncLiveImagesReply {
  bool started = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否成功启动", description: "同步任务是否成功启动"}];        // 是否成功启动
  string message = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "响应消息", description: "启动结果描述信息"}];       // 消息
  string task_id = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID", description: "异步任务的唯一标识"}];       // 任务ID
  int64 expo_id = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "展会ID"}];       // 展会ID
  string group_id = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人员库ID", description: "关联的人员库ID"}];       // 人员库ID
}

message GetSyncStatusRequest {
  int64 expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "要查询同步状态的展会ID"}]; // 展会ID
}

// 同步状态枚举
enum SyncStatus {
  SYNC_STATUS_UNKNOWN = 0;    // 未知状态
  SYNC_STATUS_NOT_STARTED = 1; // 未开始
  SYNC_STATUS_RUNNING = 2;     // 运行中
  SYNC_STATUS_COMPLETED = 3;   // 已完成
  SYNC_STATUS_FAILED = 4;      // 失败
  SYNC_STATUS_CANCELLED = 5;   // 已取消
}

// 图片同步项状态
enum ImageSyncStatus {
  IMAGE_SYNC_STATUS_UNKNOWN = 0;   // 未知状态
  IMAGE_SYNC_STATUS_PENDING = 1;   // 待处理
  IMAGE_SYNC_STATUS_PROCESSING = 2; // 处理中
  IMAGE_SYNC_STATUS_SUCCESS = 3;   // 成功
  IMAGE_SYNC_STATUS_FAILED = 4;    // 失败
  IMAGE_SYNC_STATUS_SKIPPED = 5;   // 跳过
  IMAGE_SYNC_STATUS_DELETED = 6;   // 已删除
}

// 同步操作类型
enum SyncAction {
  SYNC_ACTION_UNKNOWN = 0;  // 未知操作
  SYNC_ACTION_ADD = 1;      // 新增
  SYNC_ACTION_DELETE = 2;   // 删除
  SYNC_ACTION_SKIP = 3;     // 跳过
}

// 图片同步详情
message ImageSyncItem {
  string image_url = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片URL", description: "图片地址"}];
  SyncAction action = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "同步操作", description: "对该图片执行的操作类型"}];
  ImageSyncStatus status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "处理状态", description: "图片的处理状态"}];
  string error_message = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "错误信息", description: "处理失败时的错误信息"}];
  int32 face_count = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人脸数量", description: "检测到的人脸数量"}];
  int64 processed_at = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "处理时间", description: "处理完成时间戳"}];
  int32 retry_count = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "重试次数", description: "重试次数"}];
}

// 查询同步状态响应
message GetSyncStatusReply {
  int64 expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "展会ID"}];
  string group_id = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人员库ID", description: "关联的人员库ID"}];
  SyncStatus status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "同步状态", description: "整体同步状态"}];
  string task_id = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID", description: "当前任务ID"}];

  // 统计信息
  int32 total_images = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总图片数", description: "需要处理的总图片数"}];
  int32 processed_images = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "已处理数", description: "已处理的图片数"}];
  int32 success_count = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "成功数量", description: "成功处理的图片数"}];
  int32 failed_count = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "失败数量", description: "处理失败的图片数"}];
  int32 skipped_count = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳过数量", description: "跳过的图片数"}];
  int32 deleted_count = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "删除数量", description: "删除的图片数"}];

  // 时间信息
  int64 started_at = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间", description: "任务开始时间戳"}];
  int64 completed_at = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "完成时间", description: "任务完成时间戳"}];
  int64 duration_ms = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "执行时长", description: "任务执行时长（毫秒）"}];

  // 详细信息
  string message = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态消息", description: "当前状态的描述信息"}];
  repeated ImageSyncItem items = 15 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片详情", description: "每个图片的同步详情"}];

  // 进度信息
  float progress_percent = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "进度百分比", description: "同步进度百分比 (0-100)"}];
  string current_image = 17 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前处理图片", description: "当前正在处理的图片URL"}];
}


message FacePhotoUploadRequest {
  string photo_url = 1;       // 展会照片URL
  int64 expo_id = 2;        // 展会场景ID
  string operator = 3;        // 操作员ID
}

// 人脸照片上传响应
message FacePhotoUploadReply {
  string upload_id = 1;           // 上传批次ID
  string original_photo_url = 2;  // 原始照片URL
  int32 total_faces = 3;          // 检测到的总人脸数
  int32 success_faces = 4;        // 成功入库的人脸数
  int32 failed_faces = 5;         // 失败的人脸数
  repeated FaceUploadItem faces = 6; // 人脸处理结果列表
}

// 人脸上传结果项
message FaceUploadItem {
  string face_id = 1;       // 业务人脸ID
  string face_url = 2;      // 人脸截图URL
  float quality_score = 3;  // 质量分数
  FaceRect rect = 4;        // 人脸在原图中的坐标
  string status = 5;        // 入库状态
  string error_message = 6; // 错误信息
}


// 展会信息
message ExhibitionInfo {
  string exhibition_id = 1;     // 展会ID
  string exhibition_name = 2;   // 展会名称
  string exhibition_date = 3;   // 展会日期
}

// 创建人员库请求
message FaceGroupCreateRequest {
  string name = 1;          // 人员库名称
  string description = 2;   // 描述
  int64 expo_id = 4;      // 场景ID
  int32 max_face_num = 5;   // 最大人脸数
}

// 创建人员库响应
message FaceGroupCreateReply {
  string group_id = 1;              // 生成的人员库ID
  string name = 2;                  // 人员库名称
  string description = 3;           // 描述
  string face_model_version = 4;    // 算法模型版本
  int32 max_faces = 5;              // 最大人脸数量
  int32 estimated_face_count = 6;   // 预估当前人脸数量
  int32 status = 7;                 // 状态
  string created_at = 8;            // 创建时间
}

// 获取人员库信息请求
message FaceGroupInfoRequest {
  string group_id = 1;  // 人员库ID
}

// 根据场景获取人员库请求
message FaceGroupsByExpoRequest {
  int64 expo_id = 2;    // 场景ID
}

// 获取人员库列表请求
message FaceGroupListRequest {
  int32 page = 2;          // 页码
  int32 size = 3;          // 每页数量
}

// 人员库列表响应
message FaceGroupListReply {
  repeated FaceGroupInfo groups = 1; // 人员库列表
  int32 total = 2;                   // 总数
  int32 page = 3;                    // 当前页
  int32 size = 4;                    // 每页数量
}

// 人员库信息
message FaceGroupInfo {
  string group_id = 1;              // 人员库ID
  string name = 2;                  // 人员库名称
  string description = 3;           // 描述
  string face_model_version = 4;    // 算法模型版本
  int32 max_faces = 5;              // 最大人脸数量
  int32 estimated_face_count = 6;   // 预估当前人脸数量
  int32 status = 7;                 // 状态
  string created_at = 8;            // 创建时间
}

// 获取照片人脸列表请求
message PhotoFacesRequest {
  string photo_url = 1;  // 原始照片URL
}

// 获取照片人脸列表响应
message PhotoFacesReply {
  string photo_url = 1;             // 原始照片URL
  int32 total_faces = 2;            // 总人脸数
  int32 success_faces = 3;          // 成功人脸数
  int32 failed_faces = 4;           // 失败人脸数
  repeated FaceUploadItem faces = 5; // 人脸列表
}