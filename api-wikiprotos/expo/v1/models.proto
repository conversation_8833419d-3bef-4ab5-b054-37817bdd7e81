syntax = "proto3";

package api.expo.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";

option go_package = "api/expo/v1;v1";

// 赞助等级
enum SponsorLevel {
  SponsorLevel_NONE = 0;// 无赞助
  SponsorLevel_SILVER = 1; // 白银
  SponsorLevel_GOLD = 2; // 黄金
  SponsorLevel_PLATINUM = 3; // 铂金
  SponsorLevel_DIAMOND = 4; // 钻石
  SponsorLevel_GOLOBAL = 5; // 全球
}

enum Industry {
  INDUSTRY_UNKNOWN = 0; // 未知
  INDUSTRY_STOCK = 1; // Stock
  INDUSTRY_FOREX = 2; // Forex
  INDUSTRY_CRYPTO = 3; // Crypto
  INDUSTRY_FINTECH= 4; // Fintech
}

enum Identity {
  IDENTITY_UNKNOWN = 0; // 未知
  IDENTITY_TRADER = 1; // 交易商
  IDENTITY_INVESTOR = 2; // 投资者
  IDENTITY_SERVICE_PROVIDER = 3; // 服务商
  IDENTITY_KOL = 4; // KOL
}

enum SubIdentity {
  SUB_IDENTITY_UNKNOWN = 0; // 未知
  SUB_IDENTITY_FOREX = 10001; // Forex Broker
  SUB_IDENTITY_SERVICE_PROVIDER = 30001; // Service Provider（Press/Media/Cloud/Bank/Wealth management/CRM）
  SUB_IDENTITY_FINTECH = 30002; // Fintech（Payment/Al/Liquidity/Trading platform）
  SUB_IDENTITY_CRYPTO = 30003; // Crypto / Digital Assets
  SUB_IDENTITY_SERVICE_IB = 30004; // IB / Affiliate
  SUB_IDENTITY_INVESTOR = 30005; // Investor / VC
  SUB_IDENTITY_TRADER = 30006; // Trader
  SUB_IDENTITY_OTHER = 30007; // Other
  SUB_IDENTITY_KOL = 40001; // KOL
}

// 人脸矩形位置
message FaceRect {
  int32 x = 1;      // 左上角X坐标
  int32 y = 2;      // 左上角Y坐标
  int32 width = 3;  // 宽度
  int32 height = 4; // 高度
}