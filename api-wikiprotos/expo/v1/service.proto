syntax = "proto3";

package api.expo.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "expo/v1/models.proto";

option go_package = "api/expo/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // ===========================================================
  // =========================== 展会 ===========================
  // ===========================================================
  // 获取开屏展会
  rpc GetOpenExpo(common.EmptyRequest) returns (OpenExpoReply) {
    option (google.api.http) = {get: "/v1/expo/open"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取开屏展会",tags: ["展会"]};
  }
  // 用户展会权益
  rpc UserExpoRight(UserExpoRightRequest) returns (UserExpoRightReply) {
    option (google.api.http) = {get: "/v1/expo/user_right"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"用户展会权益",tags: ["展会"]};
  }
  // 展会tab
  rpc ExpoTab(common.EmptyRequest) returns (ExpoTabReply) {
    option (google.api.http) = {get: "/v1/expo/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会tab",tags: ["展会"]};
  }
  // 获取展会列表
  rpc ExpoList(ExpoListRequest) returns (ExpoListReply) {
    option (google.api.http) = {get: "/v1/expo/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会列表",tags: ["展会"]};
  }
  // 展会详情
  rpc GetExpoDetail(ExpoDetailRequest) returns (ExpoDetail) {
    option (google.api.http) = {get: "/v1/expo/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会详情",tags: ["展会"]};
  }
  // 展会用户报名
  rpc ExpoUserRegistration(ExpoUserSignUpRequest) returns (ExpoUserSignUpReply) {
    option (google.api.http) = {post: "/v1/expo/audience/registration" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会用户报名",tags: ["展会"]};
  }
  // 展会参展商报名
  rpc ExhibitorRegistration(ExhibitorSignUpRequest) returns (ExhibitorSignUpReply) {
    option (google.api.http) = {post: "/v1/expo/exhibitor/registration" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"参展商报名",tags: ["展会"]};
  }
  // 展会互动
  rpc ExpoInteraction(ExpoInteractionRequest) returns (ExpoInteractionReply) {
    option (google.api.http) = {get: "/v1/expo/interaction"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动",tags: ["展会"]};
  }
  // 展会互动预告短语
  rpc ExpoInteractionPreview(common.EmptyRequest) returns (ExpoInteractionPreviewReply) {
    option (google.api.http) = {get: "/v1/expo/interaction/preview"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动预告短语",tags: ["展会"]};
  }
  // 发表展会互动
  rpc PostExpoInteraction(PostExpoInteractionRequest) returns (PostExpoInteractionReply) {
    option (google.api.http) = {post: "/v1/expo/interaction" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"发表展会互动",tags: ["展会"]};
  }
  // 展会议程
  rpc ExpoSchedule(ExpoScheduleRequest) returns (ExpoScheduleReply) {
    option (google.api.http) = {get: "/v1/expo/schedule"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会议程",tags: ["展会"]};
  }
  // 展会指南
  rpc ExpoGuide(ExpoGuideRequest) returns (ExpoGuideReply) {
    option (google.api.http) = {get: "/v1/expo/guide"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会指南",tags: ["展会"]};
  }
  // 获取伙伴
  rpc ExpoPartner(ExpoPartnerRequest) returns (ExpoPartnerReply) {
    option (google.api.http) = {get: "/v1/expo/partner"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会合作伙伴",tags: ["展会"]};
  }
  // 展会话题
  rpc ExpoTopic(ExpoTopicRequest) returns (ExpoTopicReply) {
    option (google.api.http) = {get: "/v1/expo/topic"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会话题",tags: ["展会"]};
  }
  // 演讲嘉宾列表
  rpc GetSpeakerList(GetSpeakerListRequest) returns (GetSpeakerListReply) {
    option (google.api.http) = {get: "/v1/expo/speaker"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"演讲嘉宾",tags: ["展会"]};
  }
  // 演讲嘉宾主页
  rpc GetSpeakerDetail(GetSpeakerDetailRequest) returns (GetSpeakerDetailReply) {
    option (google.api.http) = {get: "/v1/expo/speaker/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"演讲嘉宾主页",tags: ["展会"]};
  }
  // 观展用户tab
  rpc GetAudienceTab(GetAudienceTabRequest) returns (GetAudienceTabReply) {
    option (google.api.http) = {get: "/v1/expo/audience/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"观展用户tab",tags: ["展会"]};
  }
  // 观展用户列表
  rpc GetAudienceList(GetAudienceRequest) returns (GetAudienceReply) {
    option (google.api.http) = {get: "/v1/expo/audience/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"观展用户列表",tags: ["展会"]};
  }
  // 参展商列表
  rpc ExhibitorList(GetExhibitorListRequest) returns (GetExhibitorListReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"参展商列表",tags: ["展会"]};
  }
  // 参展商员工列表
  rpc ExhibitorEmployeeList(GetExhibitorEmployeeListRequest) returns (GetExhibitorEmployeeListReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/employee/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"参展商员工列表",tags: ["展会"]};
  }
  // 展位列表
  rpc GetExhibitorBoothList(GetExhibitorBoothListRequest) returns (GetExhibitorBoothListReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/booth/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展位列表",tags: ["展会"]};
  }
  // ===========================================================
  // =========================== 票夹 ===========================
  // ===========================================================
  // 票夹列表
  rpc TicketList(GetTicketListRequest) returns (GetTicketListReply) {
    option (google.api.http) = {get: "/v1/ticket/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "票夹列表",tags: ["票夹"]};
  }
  // 票夹详情
  rpc GetTicketDetail(GetTicketDetailRequest) returns (TicketDetail) {
    option (google.api.http) = {get: "/v1/ticket/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "票夹详情",tags: ["票夹"]};
  }
  // =========================== 人脸识别接口 ===========================

  // 用户人脸搜索接口
  rpc FaceSearch(FaceSearchRequest) returns (FaceSearchReply) {
    option (google.api.http) = {post: "/v1/face/faceSearch", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "用户人脸搜索",
      tags: ["人脸识别"],
      description: "前台用户自助接口，上传自拍照搜索展会照片",
    };
  }


  // ===========================================================
  // =========================== 图片直播 =======================
  // ===========================================================

  // 分页图片直播列表
  rpc GetLiveImages(GetLiveImagesRequest) returns (GetLiveImagesReply) {
    option (google.api.http) = {get: "/v1/expo/images/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "分页图片直播列表",
      tags: ["展会图片"],
      description: "根据展会ID分页获取展会图片列表"
    };
  }
}

message OpenExpoReply {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会ID"}];
  ExpoStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会状态"}];
  string logo = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会logo"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会名称"}];
  string country_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会所在国家"}];
  int64 start_time = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会开始时间"}];
  string ticket_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会门票代码"}];
  TicketStatus ticket_status = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会门票状态"}];
  string payment_total = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会门票总额"}];
  int64 created_at = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会创建时间"}];
}

message UserExpoRightRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会ID"}];
}
enum ExpoRightType {
  ExpoRightType_Interaction = 0; // 展会互动
  ExpoRightType_Chat = 1; // 自由聊天
}
message ExpoRight {
  ExpoRightType type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"权益类型：0：展会互动；1：自由聊天"}];
  string title = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"权益标题"}];
  string lock = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"锁状态"}];
  repeated string description = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"权益描述"}];
  bool activate = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"是否激活"}];
  string icon = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"图标"}];
  string jump_title = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"跳转标题"}];
}
message UserExpoRightReply {
  repeated ExpoRight rights = 2;
}

enum ExpoStatus {
  ExpoStatus_UNKNOWN = 0; // 未知状态
  ExpoStatus_NOT_START = 1; // 未开始
  ExpoStatus_PROCESSING = 2; // 进行中
  ExpoStatus_END = 3; // 已结束
}
message ExpoTab {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示名称"}];
  repeated ExpoTab sub_tabs = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "子tab"}];
}
message ExpoTabReply {
  repeated ExpoTab tabs = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabs"}];
}

message ExpoListRequest {
  string tab_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab_id"}];
  string sub_tab_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "sub_tab_id"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小，默认10"}];
  int32 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页页数，默认1"}];
}
message ExpoBase {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  ExpoStatus status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会状态：0-未知；1-未开始；2-进行中；3-已结束"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string address = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string audience = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会观众"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家代码"}];
  int64 start_time = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间(单位秒)"}];
  string zone = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会时区"}];
}
message ExpoListReply {
  repeated ExpoBase expos = 1;
}

enum ExpoAudienceGroupCategory {
  Speakers = 0; // 演讲嘉宾
  Professionals = 1; // 从业者
  Investors = 2; // 投资者
  NotGrouped = 3; // 未分组
  Exhibitors = 4; // 赞助商
}

message ExpoExhibitor {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商名称"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商logo"}];
  string sponsor_level_icon = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
}
message ExpoAudienceGroup {
  ExpoAudienceGroupCategory category = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "观众分类"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分组名称"}];
  string count = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人员数量"}];
  repeated string avatars = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像列表"}];
  repeated ExpoExhibitor exhibitors = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo列表"}];
}
message LiveInfo {
  string live_url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播名称"}];
}
message ExpoDetail {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  ExpoStatus status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会状态：0-未知；1-未开始；2-进行中；3-已结束"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string address = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string description =  6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会描述"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家代码"}];
  int64 start_time = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间(单位秒)"}];
  string longitude = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会经度"}];
  string latitude = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会纬度"}];
  string baidu_image = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "百度地图图片"}];
  string google_image = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "谷歌地图图片"}];
  bool registration = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否展会报名"}];
  string main_live = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播地址"}];
  repeated LiveInfo lives = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多视角直播信息"}];
  repeated string images = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片直播"}];
  int64 image_count = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片直播数量"}];
  repeated ExpoAudienceGroup audience_groups = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "观众分组"}];
}
message ExpoDetailRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}


message ExpoUserSignUpRequest {
  string username = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名"}];
  string phone_area_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机区号"}];
  string phone = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string company = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司"}];
  string job = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "职位"}];
  Industry industry = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业"}];
  Identity identity = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
}
message ExpoUserSignUpReply {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string address = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string payment_total = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商收款金额"}];
  string ticket_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商票据"}];
  TicketStatus status = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据状态"}];
}

message ExhibitorSignUpRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string exhibit_size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位大小"}];
  string company = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  string website = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司网址"}];
  string contact = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "联系人"}];
  string phone_area_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机区号"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
}
message ExhibitorSignUpReply {}

message ExpoInteractionRequest {
  int64 expo_id = 1;
  int32 page = 2;
  int32 size = 3;
}
message ExpoInteraction {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论id"}];
  string avatar = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string nickname = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称"}];
  string origin_content = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始内容"}];
  string content = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容"}];
  string time = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "互动时间"}];
  string country = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家"}];
  string like = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否点赞"}];
  repeated ExpoInteraction comments = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论"}];
}
message ExpoInteractionReply {
  string total = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总评论数据"}];
  repeated ExpoInteraction comments = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论"}];
}

message ExpoInteractionPreviewReply {
  repeated string previews = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "预告评论"}];
}

message PostExpoInteractionRequest {
  string content = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容"}];
}
message PostExpoInteractionReply {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message ExpoScheduleRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoScheduleHallLine {
  int64 timestamp = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间（单位秒）"}];
  string timestamp_show = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示时间"}];
  string theme = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲主题"}];
  string description = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲描述"}];
  string speaker = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者"}];
  string speaker_avatar = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者头像"}];
  string speaker_description = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者描述"}];
}
// 展会场次
message ExpoScheduleHall {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次名称"}];
  repeated ExpoScheduleHallLine lines = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次时间线"}];
}
message ExpoSchedule {
  string date = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间"}];
  repeated ExpoScheduleHall halls = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次"}];
}
message ExpoScheduleReply {
  repeated ExpoSchedule schedules = 1;
}

message ExpoGuideRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoBooth {
  string broker_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string sponsor_level_icon = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string booth = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位"}];
}
message ExpoGuideReply {
  string guide_map_url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导览图"}];
  repeated ExpoBooth booth = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商展位"}];
  string address = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "详细地址"}];
  string location = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "位置名称"}];
  string longitude = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会经度"}];
  string latitude = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会纬度"}];
  string baidu_map_url = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "百度地图链接"}];
  string google_map_url = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "谷歌地图链接"}];
}

message ExpoPartnerRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoPartnerGroup {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "合作分组名称"}];
  repeated string logo = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "合作伙伴logo"}];
}
message ExpoPartnerReply {
  repeated ExpoPartnerGroup groups = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "合作伙伴分组"}];
}

message ExpoTopicRequest {
  int64 expo_id = 1;
  int32 page = 2;
  int32 size = 3;
}
message ExpoTopicItem {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题ID"}];
  string title = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题名称"}];
  string image_url = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题图片"}];
  string avatar = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题头像"}];
  string nickname = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称"}];
  bool like = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否点赞"}];
}
message ExpoTopicReply {
  repeated ExpoTopicItem topics = 1;
}

message GetSpeakerListRequest {
  int64 expo_id = 1;
}

message ExpoSpeakerSchedule {
  string date = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间"}];
  repeated ExpoSpeaker speakers = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者"}];
}
message ExpoSpeaker {
  int64 speaker_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者ID"}];
  string speaker_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者名称"}];
  string speaker_avatar = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者头像"}];
  int64 timestamp = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间（单位秒）"}];
  string timestamp_show = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示时间"}];
  string theme = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲主题"}];
  string description = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲描述"}];
  string speaker = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者"}];
  bool subscribe = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否订阅"}];
  string speaker_description = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者描述"}];
}
message GetSpeakerListReply {
  repeated ExpoSpeakerSchedule schedule = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程列表"}];
}

message GetSpeakerDetailRequest {
  int64 speaker_id = 1;
  int64 expo_id = 2;
}
message SocialMedia {
  string icon = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "媒体icon"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "媒体名称"}];
  string value = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "媒体值"}];
}
message GetSpeakerDetailReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者名称"}];
  string avatar = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者头像"}];
  int64 fans = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "粉丝数"}];
  int64 follow = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关注数"}];
  int64 like = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "点赞数"}];
  string description = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者描述"}];
  repeated string label = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  repeated SocialMedia social_media = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "社交信息"}];
  repeated ExpoSpeakerSchedule schedule = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程列表"}];
  string user_id = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
}

message GetAudienceTabRequest {
  int64 expo_id = 1;
}
message AudienceTab {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签名称"}];
  string id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}
message GetAudienceTabReply {
  repeated AudienceTab tabs = 1;
}

message GetAudienceRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string tab_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabID"}];
  int32 page = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
  string keyword = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索关键字"}];
}
message AudienceItem {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string nickname = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称"}];
  string avatar = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string country_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  Identity identity = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  bool check = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否验证"}];
  repeated string label = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
}
message GetAudienceReply {
  repeated AudienceItem items = 1;
}

message GetExhibitorListRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
}
message Exhibitor {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string logo = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string sponsor_level_icon = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string booth = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位号"}];
  repeated string employees = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商员工"}];
}
message GetExhibitorListReply {
  repeated Exhibitor items = 1;
}

message GetExhibitorEmployeeListRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
}
message ExhibitorEmployee {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工ID"}];
  string avatar = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工头像"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工名称"}];
  string title = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工职位"}];
  string exhibitor_logo = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string exhibitor_name = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string sponsor_level_icon = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string booth = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位号"}];
}
message GetExhibitorEmployeeListReply {
  repeated ExhibitorEmployee employees = 1;
}
message GetExhibitorBoothListRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
}
message ExhibitorBooth {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
  string booth= 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位号"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string sponsor_level_icon = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
}
message GetExhibitorBoothListReply {
  repeated ExhibitorBooth booths = 1;
}
// ===========================================================
// =========================== 票夹 ===========================
// ===========================================================


enum TicketStatus {
  TICKET_STATUS_REVIEW = 0; // 审核中
  TICKET_STATUS_PASS = 1; // 待使用
  TICKET_STATUS_USED = 2; // 已核销
  TICKET_STATUS_EXPIRED = 3; // 已过期
  TICKET_STATUS_REJECT = 4; // 审核未通过
}

message TicketBase {
  string ticket_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据编码"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  string location = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string address = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  int64 start_time = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间；单位秒"}];
  string payment_total = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单支付总金额"}];
  TicketStatus status = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据状态"}];
}

message TicketDetail {
  string ticket_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据编码"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  string location = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string address = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  int64 start_time = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间；单位秒"}];
  string payment_total = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单支付总金额"}];
  TicketStatus status = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据状态"}];
  string username = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名"}];
  string phone = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  Industry industry = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业"}];
  Identity identity = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  string company = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司"}];
  string job = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "职位"}];
  int64 created_at = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间;单位秒"}];
}

message GetTicketListRequest {
  int32 size = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小，默认10"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页页数，默认1"}];
}
message GetTicketListReply {
  repeated TicketBase items = 1;
}

message GetTicketDetailRequest {
  string ticket_code = 1;
}

message GetLiveImagesReply {
  repeated LiveImage list = 1;  // 展会图片列表
  int64 total = 2;               // 总数
  int32 page = 3;                // 当前页
  int32 size = 4;
}

message GetLiveImagesRequest {
  int64 expo_id = 1;
  int32 page = 2;      // 页码，从1开始
  int32 size = 3;      // 每页数量
}

message LiveImage {
  uint64 id = 1;         // 图片ID
  int64 expo_id = 2;    // 展会ID
  string image_url = 3;  // 图片地址
  string image_thumbnail_url = 4;  // 图片地址
  int64 created_at = 5;  // 创建时间（Unix时间戳）
}

// 人脸搜索请求
message FaceSearchRequest {
  string search_photo_url = 1;     // 用户自拍照URL
  int64 expo_id = 2;    // 展会id
}

// 人脸搜索响应
message FaceSearchReply {
  string search_id = 1;             // 搜索批次ID
  int32 total_searched_faces = 2;   // 搜索到的总人脸数
  int64 search_duration_ms = 3;     // 搜索耗时(毫秒)
  repeated FaceSearchResultItem results = 4; // 搜索结果列表
  repeated FaceRect search_face_rect = 5;
  int32 total_results = 6;          // 总结果数
  int32 page = 7;                   // 当前页码
  int32 page_size = 8;              // 每页大小
  bool from_cache = 9;              // 是否来自缓存
}
// 人脸搜索结果项
message FaceSearchResultItem {
  string photo_url = 1;   // 匹配到的展会照片URL列表
  string photo_thumbnail_url = 2;   //缩略图
  float max_similarity = 3;         // 最高相似度
}