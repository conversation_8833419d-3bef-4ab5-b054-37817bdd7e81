// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"wiki_user_center/api/common"
	"wiki_user_center/internal/conf"
	"wiki_user_center/internal/dao"
	"wiki_user_center/internal/server"
	"wiki_user_center/internal/service"
)

import (
	_ "git55.fxeyeinterface.com/public-projects/go-tools/i18n"
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(serverConfig *common.ServerConfig, dataConfig *common.DataConfig, business *conf.Business, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := dao.NewMySQL(dataConfig, logger)
	if err != nil {
		return nil, nil, err
	}
	client, cleanup2, err := dao.NewRedis(dataConfig, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	user := dao.NewUser(db, client)
	userIdentity := dao.NewUserIdentity(db, client)
	gradeTask := dao.NewGradeTask(db, client)
	userIdentityTaskSchedule := dao.NewUserIdentityTaskSchedule(db, client)
	banner := dao.NewBanner(db, client)
	userIdentityHistory := dao.NewUserIdentityHistory(db, client)
	identityGrade := dao.NewIdentityGrade(db, client)
	userRelation := dao.NewUserRelation(db)
	serviceService := service.NewGreeterService(user, userIdentity, gradeTask, userIdentityTaskSchedule, logger, business, banner, userIdentityHistory, identityGrade, userRelation)
	grpcServer := server.NewGRPCServer(serverConfig, serviceService, logger)
	httpServer := server.NewHTTPServer(serverConfig, serviceService, logger)
	kafkaServer := server.NewKafkaServer(business, logger, serviceService)
	app := newApp(logger, grpcServer, httpServer, kafkaServer)
	return app, func() {
		cleanup2()
		cleanup()
	}, nil
}
