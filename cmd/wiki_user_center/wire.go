//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"wiki_user_center/api/common"
	"wiki_user_center/internal/conf"
	"wiki_user_center/internal/dao"
	"wiki_user_center/internal/server"
	"wiki_user_center/internal/service"
)

// wireApp init kratos application.
func wireApp(*common.ServerConfig, *common.DataConfig, *conf.Business, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, dao.ProviderSet, service.ProviderSet, newApp))
}
