package main

import (
	"flag"
	"github.com/tx7do/kratos-transport/transport/kafka"
	"google.golang.org/protobuf/encoding/protojson"
	"os"
	"time"
	"wiki_user_center/internal/conf"

	"git55.fxeyeinterface.com/public-projects/go-tools/alarm"
	"git55.fxeyeinterface.com/public-projects/go-tools/config"
	"git55.fxeyeinterface.com/public-projects/go-tools/env"
	k8s "git55.fxeyeinterface.com/public-projects/go-tools/kubernetes"
	"git55.fxeyeinterface.com/public-projects/go-tools/registry"
	"git55.fxeyeinterface.com/public-projects/go-tools/trace"
	"git55.fxeyeinterface.com/public-projects/go-tools/urlformat"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"

	_ "git55.fxeyeinterface.com/public-projects/go-tools/i18n"
	"github.com/go-kratos/kratos/v2/encoding/json"
	_ "go.uber.org/automaxprocs"
	fxLog "wiki_user_center/internal/pkg/log"
)

var (
	Name     = env.GetServiceName()
	Version  string
	flagconf string
	id, _    = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
	json.MarshalOptions = protojson.MarshalOptions{
		UseEnumNumbers: true, // 将枚举值作为数字发出，默认为枚举值的字符串
	}
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, ks *kafka.Server) *kratos.App {
	opts := []kratos.Option{
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
			ks,
		),
	}

	_, ok := os.LookupEnv("KUBERNETES_SERVICE_HOST")
	if ok {
		clientSet, err := k8s.NewClient()
		if err != nil {
			panic(err)
		}

		opts = append(opts, kratos.Registrar(registry.NewRegistry(clientSet, logger)))
	}

	return kratos.New(opts...)
}

func main() {
	flag.Parse()

	var bc conf.Bootstrap
	err := config.LoadConfig(&bc, config.WithFilePath(flagconf))
	if err != nil {
		panic(err)
	}
	urlformat.SetFormat(urlformat.NewFormat(bc.Server.OssDomain))
	//logger := fxLog.NewWikiFxLogger(fxLog.WithFilePath(bc.Business.LogPath))
	logger := fxLog.NewWikiFxLogger(fxLog.WithConsole())
	defer func() { _ = logger.Close() }()

	if bc.Server.Trace != nil {
		_, err = trace.NewTrace(
			bc.Server.Trace.Kind,
			bc.Server.Trace.Endpoint,
			trace.WithFraction(bc.Server.Trace.Fraction),
		)
		if err != nil {
			panic(err)
		}
	}

	if bc.Server.Alarm != nil {
		a := bc.Server.Alarm
		_, err = alarm.NewAlarm(&alarm.Config{
			Delay:        time.Duration(a.DelayMinutes) * time.Minute,
			NoDelayCount: int(a.NoDelayCount),
			Webhook:      a.Webhook,
			ServiceName:  a.ServiceName,
			Users:        a.Users,
		})
		if err != nil {
			panic(err)
		}
	}

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc.Business, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
