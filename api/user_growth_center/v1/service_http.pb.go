// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: user_growth_center/v1/service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	common "wiki_user_center/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceAssist = "/api.user_growth_center.v1.Service/Assist"
const OperationServiceCreateUserDivisionInvitation = "/api.user_growth_center.v1.Service/CreateUserDivisionInvitation"
const OperationServiceCreateUserDivisionRewardLevel = "/api.user_growth_center.v1.Service/CreateUserDivisionRewardLevel"
const OperationServiceDeleteUserDivisionRewardLevel = "/api.user_growth_center.v1.Service/DeleteUserDivisionRewardLevel"
const OperationServiceFriendAssistance = "/api.user_growth_center.v1.Service/FriendAssistance"
const OperationServiceGetActivityMain = "/api.user_growth_center.v1.Service/GetActivityMain"
const OperationServiceGetActivityShare = "/api.user_growth_center.v1.Service/GetActivityShare"
const OperationServiceGetBanner = "/api.user_growth_center.v1.Service/GetBanner"
const OperationServiceGetContent = "/api.user_growth_center.v1.Service/GetContent"
const OperationServiceGetDepositDetail = "/api.user_growth_center.v1.Service/GetDepositDetail"
const OperationServiceGetGlobalTrader = "/api.user_growth_center.v1.Service/GetGlobalTrader"
const OperationServiceGetGrowthCenterEntry = "/api.user_growth_center.v1.Service/GetGrowthCenterEntry"
const OperationServiceGetIdentityCarousel = "/api.user_growth_center.v1.Service/GetIdentityCarousel"
const OperationServiceGetIdentityRule = "/api.user_growth_center.v1.Service/GetIdentityRule"
const OperationServiceGetIdentityShare = "/api.user_growth_center.v1.Service/GetIdentityShare"
const OperationServiceGetInvitationPopupData = "/api.user_growth_center.v1.Service/GetInvitationPopupData"
const OperationServiceGetInviteRewardBannerData = "/api.user_growth_center.v1.Service/GetInviteRewardBannerData"
const OperationServiceGetInvitedRecordData = "/api.user_growth_center.v1.Service/GetInvitedRecordData"
const OperationServiceGetInviterActivityTime = "/api.user_growth_center.v1.Service/GetInviterActivityTime"
const OperationServiceGetQuizInfo = "/api.user_growth_center.v1.Service/GetQuizInfo"
const OperationServiceGetQuizRecord = "/api.user_growth_center.v1.Service/GetQuizRecord"
const OperationServiceGetRecommendTrader = "/api.user_growth_center.v1.Service/GetRecommendTrader"
const OperationServiceGetRecommenderList = "/api.user_growth_center.v1.Service/GetRecommenderList"
const OperationServiceGetRewordDetail = "/api.user_growth_center.v1.Service/GetRewordDetail"
const OperationServiceGetRewordPoolDetail = "/api.user_growth_center.v1.Service/GetRewordPoolDetail"
const OperationServiceGetShareLinkData = "/api.user_growth_center.v1.Service/GetShareLinkData"
const OperationServiceGetTraderActivityPage = "/api.user_growth_center.v1.Service/GetTraderActivityPage"
const OperationServiceGetUpgradeIdentity = "/api.user_growth_center.v1.Service/GetUpgradeIdentity"
const OperationServiceGetUserDivisionActivityInfo = "/api.user_growth_center.v1.Service/GetUserDivisionActivityInfo"
const OperationServiceGetUserDivisionActivityList = "/api.user_growth_center.v1.Service/GetUserDivisionActivityList"
const OperationServiceGetUserDivisionEntry = "/api.user_growth_center.v1.Service/GetUserDivisionEntry"
const OperationServiceGetUserDivisionInviteeInfo = "/api.user_growth_center.v1.Service/GetUserDivisionInviteeInfo"
const OperationServiceGetUserDivisionInviterStatisticsInfo = "/api.user_growth_center.v1.Service/GetUserDivisionInviterStatisticsInfo"
const OperationServiceGetUserDivisionRewardLevelInfo = "/api.user_growth_center.v1.Service/GetUserDivisionRewardLevelInfo"
const OperationServiceGetUserGrowthDetail = "/api.user_growth_center.v1.Service/GetUserGrowthDetail"
const OperationServiceGetUserInfo = "/api.user_growth_center.v1.Service/GetUserInfo"
const OperationServiceGetVpsLevel = "/api.user_growth_center.v1.Service/GetVpsLevel"
const OperationServiceGrandLuckyDraw = "/api.user_growth_center.v1.Service/GrandLuckyDraw"
const OperationServiceHealthy = "/api.user_growth_center.v1.Service/Healthy"
const OperationServicePostUpgradeVPSStatus = "/api.user_growth_center.v1.Service/PostUpgradeVPSStatus"
const OperationServicePostUserIdentitySwitch = "/api.user_growth_center.v1.Service/PostUserIdentitySwitch"
const OperationServiceSearchTrader = "/api.user_growth_center.v1.Service/SearchTrader"
const OperationServiceStartDraw = "/api.user_growth_center.v1.Service/StartDraw"
const OperationServiceStringReply = "/api.user_growth_center.v1.Service/StringReply"
const OperationServiceSubmitQuiz = "/api.user_growth_center.v1.Service/SubmitQuiz"
const OperationServiceUpdateInviterActivityTime = "/api.user_growth_center.v1.Service/UpdateInviterActivityTime"
const OperationServiceUpdateUserDivisionRewardLevel = "/api.user_growth_center.v1.Service/UpdateUserDivisionRewardLevel"
const OperationServiceUpgradeVPS = "/api.user_growth_center.v1.Service/UpgradeVPS"
const OperationServiceUserCheckIn = "/api.user_growth_center.v1.Service/UserCheckIn"
const OperationServiceWatchLiveCompleted = "/api.user_growth_center.v1.Service/WatchLiveCompleted"

type ServiceHTTPServer interface {
	// Assist 给用户助力
	Assist(context.Context, *AssistRequest) (*AssistReply, error)
	// CreateUserDivisionInvitation----------------------------------奖励配置----------------------------------//
	//----------------------------------邀请----------------------------------//
	// 新增用户分裂邀请记录
	CreateUserDivisionInvitation(context.Context, *CreateUserDivisionInvitationRequest) (*CreateUserDivisionInvitationReply, error)
	// CreateUserDivisionRewardLevel 新增用户分裂奖励配置
	CreateUserDivisionRewardLevel(context.Context, *CreateUserDivisionRewardLevelRequest) (*CreateUserDivisionRewardLevelReply, error)
	// DeleteUserDivisionRewardLevel 删除用户分裂奖励配置
	DeleteUserDivisionRewardLevel(context.Context, *DeleteUserDivisionRewardLevelRequest) (*common.EmptyReply, error)
	// FriendAssistance 好友助力
	FriendAssistance(context.Context, *FriendAssistanceRequest) (*FriendAssistanceReply, error)
	// GetActivityMain 活动主页
	GetActivityMain(context.Context, *GetActivityMainRequest) (*GetActivityMainReply, error)
	// GetActivityShare 活动主页分享
	GetActivityShare(context.Context, *GetActivityShareRequest) (*GetActivityShareReply, error)
	// GetBanner 轮播图和广告
	GetBanner(context.Context, *GetBannerRequest) (*GetBannerReply, error)
	// GetContent 规则和说明
	GetContent(context.Context, *GetContentRequest) (*GetContentReply, error)
	// GetDepositDetail 入金一手
	GetDepositDetail(context.Context, *GetDepositDetailRequest) (*GetDepositDetailReply, error)
	// GetGlobalTrader 全球交易商列表
	GetGlobalTrader(context.Context, *GetGlobalTraderRequest) (*GetGlobalTraderReply, error)
	// GetGrowthCenterEntry 成长中心入展示开关
	GetGrowthCenterEntry(context.Context, *GetGrowthCenterEntryRequest) (*GetGrowthCenterEntryReply, error)
	// GetIdentityCarousel 获取我的页面banner位置轮播图（有屏蔽点位）
	GetIdentityCarousel(context.Context, *GetIdentityCarouselRequest) (*GetIdentityCarouselReply, error)
	// GetIdentityRule 身份升级规则
	GetIdentityRule(context.Context, *GetIdentityRuleRequest) (*GetIdentityRuleReply, error)
	// GetIdentityShare 获取身份分享图
	GetIdentityShare(context.Context, *GetIdentityShareRequest) (*GetIdentityShareReply, error)
	// GetInvitationPopupData获取邀请奖励弹窗数据
	GetInvitationPopupData(context.Context, *GetInvitationPopupDataRequest) (*GetInvitationPopupDataReply, error)
	// GetInviteRewardBannerData获取邀请奖励领取banner数据
	GetInviteRewardBannerData(context.Context, *GetInviteRewardBannerDataRequest) (*GetInviteRewardBannerDataReply, error)
	// GetInvitedRecordData获取邀请用户记录数据
	GetInvitedRecordData(context.Context, *GetInvitedRecordDataRequest) (*GetInvitedRecordDataReply, error)
	// GetInviterActivityTime----------------------------------答题模块----------------------------------//
	//----------------------------------奖励配置----------------------------------//
	GetInviterActivityTime(context.Context, *common.EmptyRequest) (*GetInviterActivityTimeReply, error)
	// GetQuizInfo----------------------------------答题模块----------------------------------//
	//获取试题数据
	GetQuizInfo(context.Context, *common.EmptyRequest) (*GetQuizInfoReply, error)
	// GetQuizRecord获取试卷记录
	GetQuizRecord(context.Context, *common.EmptyRequest) (*GetQuizRecordReply, error)
	// GetRecommendTrader 搜索推荐交易商
	GetRecommendTrader(context.Context, *GetRecommendTraderRequest) (*GetRecommendTraderReply, error)
	// GetRecommenderList 获取推荐官列表
	GetRecommenderList(context.Context, *GetRecommenderListRequest) (*GetRecommenderListReply, error)
	// GetRewordDetail 我的奖励
	GetRewordDetail(context.Context, *GetRewordDetailRequest) (*GetRewordDetailReply, error)
	// GetRewordPoolDetail 瓜分现金奖池
	GetRewordPoolDetail(context.Context, *GetRewordPoolDetailRequest) (*GetRewordPoolDetailReply, error)
	// GetShareLinkData获取分享推广链接数据
	GetShareLinkData(context.Context, *GetShareLinkDataRequest) (*GetShareLinkDataReply, error)
	// GetTraderActivityPage 交易商活动详情页
	GetTraderActivityPage(context.Context, *GetTraderActivityPageRequest) (*GetTraderActivityPageReply, error)
	// GetUpgradeIdentity 升级身份入口
	GetUpgradeIdentity(context.Context, *GetUpgradeIdentityRequest) (*GetUpgradeIdentityReply, error)
	// GetUserDivisionActivityInfo----------------------------------邀请----------------------------------//
	//----------------------------------裂变流程----------------------------------//
	// 裂变活动详情
	GetUserDivisionActivityInfo(context.Context, *common.EmptyRequest) (*GetUserDivisionActivityInfoReply, error)
	// GetUserDivisionActivityList 获取邀请活动VPS升级记录列表
	GetUserDivisionActivityList(context.Context, *GetUserDivisionActivityListRequest) (*GetUserDivisionActivityListReply, error)
	// GetUserDivisionEntry 裂变活动入口
	GetUserDivisionEntry(context.Context, *common.EmptyRequest) (*GetUserDivisionEntryReply, error)
	// GetUserDivisionInviteeInfo 获取用户分裂被邀请人数据列表
	GetUserDivisionInviteeInfo(context.Context, *GetUserDivisionInviteeInfoRequest) (*GetUserDivisionInviteeInfoReply, error)
	// GetUserDivisionInviterStatisticsInfo 获取用户分裂邀请人统计数据列表
	GetUserDivisionInviterStatisticsInfo(context.Context, *GetUserDivisionInviterStatisticsInfoRequest) (*GetUserDivisionInviterStatisticsInfoReply, error)
	GetUserDivisionRewardLevelInfo(context.Context, *common.EmptyRequest) (*GetUserDivisionRewardLevelInfoReply, error)
	// GetUserGrowthDetail 身份成长中心详情页
	GetUserGrowthDetail(context.Context, *GetUserGrowthDetailRequest) (*GetUserGrowthDetailReply, error)
	GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoReply, error)
	// GetVpsLevel获取用户vps等级
	GetVpsLevel(context.Context, *common.EmptyRequest) (*GetVpsLevelReply, error)
	// GrandLuckyDraw 幸运大抽奖详情页
	GrandLuckyDraw(context.Context, *GrandLuckyDrawRequest) (*GrandLuckyDrawReply, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// PostUpgradeVPSStatus VPS升级回调接口
	PostUpgradeVPSStatus(context.Context, *PostUpgradeVPSStatusRequest) (*PostUpgradeVPSStatusReply, error)
	// PostUserIdentitySwitch APP 切换身份
	PostUserIdentitySwitch(context.Context, *PostUserIdentitySwitchRequest) (*PostUserIdentitySwitchReply, error)
	// SearchTrader 搜索交易商
	SearchTrader(context.Context, *SearchTraderRequest) (*SearchTraderReply, error)
	// StartDraw 开始抽奖
	StartDraw(context.Context, *GrandLuckyDrawRequest) (*StartDrawReply, error)
	StringReply(context.Context, *StringReplyRequest) (*common.StringReply, error)
	// SubmitQuiz提交试卷
	SubmitQuiz(context.Context, *SubmitQuizRequest) (*SubmitQuizReply, error)
	UpdateInviterActivityTime(context.Context, *UpdateInviterActivityTimeRequest) (*UpdateInviterActivityTimeReply, error)
	// UpdateUserDivisionRewardLevel 修改用户分裂奖励配置
	UpdateUserDivisionRewardLevel(context.Context, *UpdateUserDivisionRewardLevelRequest) (*common.EmptyReply, error)
	// UpgradeVPS 升级VPS
	UpgradeVPS(context.Context, *UpgradeVPSRequest) (*UpgradeVPSReply, error)
	// UserCheckIn 用户签到
	UserCheckIn(context.Context, *UserCheckInRequest) (*UserCheckInReply, error)
	// WatchLiveCompleted 观看直播任务完成
	WatchLiveCompleted(context.Context, *WatchLiveRequest) (*WatchLiveReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy1_HTTP_Handler(srv))
	r.GET("/v1/user_info", _Service_GetUserInfo0_HTTP_Handler(srv))
	r.GET("/v1/string_reply", _Service_StringReply0_HTTP_Handler(srv))
	r.GET("/v1/app/identity/rule", _Service_GetIdentityRule0_HTTP_Handler(srv))
	r.GET("/v1/app/identity/detail", _Service_GetUserGrowthDetail0_HTTP_Handler(srv))
	r.GET("/v1/app/identity/switch", _Service_GetGrowthCenterEntry0_HTTP_Handler(srv))
	r.GET("/v1/app/identity/carousel", _Service_GetIdentityCarousel0_HTTP_Handler(srv))
	r.GET("/v1/app/identity/share", _Service_GetIdentityShare0_HTTP_Handler(srv))
	r.GET("/v1/app/identity/entry", _Service_GetUpgradeIdentity0_HTTP_Handler(srv))
	r.POST("/v1/app/identity", _Service_PostUserIdentitySwitch0_HTTP_Handler(srv))
	r.GET("/v1/app/investment/main", _Service_GetActivityMain0_HTTP_Handler(srv))
	r.GET("/v1/app/investment/share", _Service_GetActivityShare0_HTTP_Handler(srv))
	r.GET("/v1/app/content", _Service_GetContent0_HTTP_Handler(srv))
	r.GET("/v1/app/search/recommend", _Service_GetRecommendTrader0_HTTP_Handler(srv))
	r.GET("/v1/app/global/trader", _Service_GetGlobalTrader0_HTTP_Handler(srv))
	r.GET("/v1/app/trader/detail", _Service_GetTraderActivityPage0_HTTP_Handler(srv))
	r.GET("/v1/app/recommender/list", _Service_GetRecommenderList0_HTTP_Handler(srv))
	r.GET("/v1/app/reword/pool", _Service_GetRewordPoolDetail0_HTTP_Handler(srv))
	r.POST("/v1/app/user/checkin", _Service_UserCheckIn0_HTTP_Handler(srv))
	r.GET("/v1/app/rewords/detail", _Service_GetRewordDetail0_HTTP_Handler(srv))
	r.GET("/v1/app/luckydraw/detail", _Service_GrandLuckyDraw0_HTTP_Handler(srv))
	r.POST("/v1/app/startdraw", _Service_StartDraw0_HTTP_Handler(srv))
	r.GET("/v1/app/deposit/detail", _Service_GetDepositDetail0_HTTP_Handler(srv))
	r.GET("/v1/app/search/trader", _Service_SearchTrader0_HTTP_Handler(srv))
	r.GET("/v1/app/investment/banner", _Service_GetBanner0_HTTP_Handler(srv))
	r.GET("/v1/app/investment/assist", _Service_Assist0_HTTP_Handler(srv))
	r.GET("/v1/app/live/completed", _Service_WatchLiveCompleted0_HTTP_Handler(srv))
	r.GET("/v1/app/investment/friendassistance", _Service_FriendAssistance0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getinvitationpopupdata", _Service_GetInvitationPopupData0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getinviterewardbannerdata", _Service_GetInviteRewardBannerData0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getsharelinkdata", _Service_GetShareLinkData0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/invitedrecord", _Service_GetInvitedRecordData0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getuservpslevel", _Service_GetVpsLevel0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getquizinfo", _Service_GetQuizInfo0_HTTP_Handler(srv))
	r.POST("/v1/userdivision/submitquiz", _Service_SubmitQuiz0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getquizrecord", _Service_GetQuizRecord0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/activity/time", _Service_GetInviterActivityTime0_HTTP_Handler(srv))
	r.PUT("/v1/userdivision/activity/time", _Service_UpdateInviterActivityTime0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/reward", _Service_GetUserDivisionRewardLevelInfo0_HTTP_Handler(srv))
	r.POST("/v1/userdivision/reward", _Service_CreateUserDivisionRewardLevel0_HTTP_Handler(srv))
	r.PUT("/v1/userdivision/reward/{id}", _Service_UpdateUserDivisionRewardLevel0_HTTP_Handler(srv))
	r.DELETE("/v1/userdivision/reward/{id}", _Service_DeleteUserDivisionRewardLevel0_HTTP_Handler(srv))
	r.POST("/v1/userdivision/invitation", _Service_CreateUserDivisionInvitation0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/inviterInfo", _Service_GetUserDivisionInviterStatisticsInfo0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/inviteeInfo", _Service_GetUserDivisionInviteeInfo0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/activities", _Service_GetUserDivisionActivityList0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/activity", _Service_GetUserDivisionActivityInfo0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/entry", _Service_GetUserDivisionEntry0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/upgrade/vps", _Service_UpgradeVPS0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/upgrade/callback", _Service_PostUpgradeVPSStatus0_HTTP_Handler(srv))
}

func _Service_Healthy1_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserInfo(ctx, req.(*GetUserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_StringReply0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StringReplyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceStringReply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StringReply(ctx, req.(*StringReplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.StringReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetIdentityRule0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetIdentityRuleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetIdentityRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetIdentityRule(ctx, req.(*GetIdentityRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetIdentityRuleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserGrowthDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserGrowthDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserGrowthDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserGrowthDetail(ctx, req.(*GetUserGrowthDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserGrowthDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetGrowthCenterEntry0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGrowthCenterEntryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetGrowthCenterEntry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGrowthCenterEntry(ctx, req.(*GetGrowthCenterEntryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGrowthCenterEntryReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetIdentityCarousel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetIdentityCarouselRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetIdentityCarousel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetIdentityCarousel(ctx, req.(*GetIdentityCarouselRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetIdentityCarouselReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetIdentityShare0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetIdentityShareRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetIdentityShare)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetIdentityShare(ctx, req.(*GetIdentityShareRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetIdentityShareReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUpgradeIdentity0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUpgradeIdentityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUpgradeIdentity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUpgradeIdentity(ctx, req.(*GetUpgradeIdentityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUpgradeIdentityReply)
		return ctx.Result(200, reply)
	}
}

func _Service_PostUserIdentitySwitch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PostUserIdentitySwitchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServicePostUserIdentitySwitch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PostUserIdentitySwitch(ctx, req.(*PostUserIdentitySwitchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PostUserIdentitySwitchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetActivityMain0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetActivityMainRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetActivityMain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetActivityMain(ctx, req.(*GetActivityMainRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetActivityMainReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetActivityShare0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetActivityShareRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetActivityShare)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetActivityShare(ctx, req.(*GetActivityShareRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetActivityShareReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetContent0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetContentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetContent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetContent(ctx, req.(*GetContentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetContentReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRecommendTrader0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRecommendTraderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRecommendTrader)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRecommendTrader(ctx, req.(*GetRecommendTraderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRecommendTraderReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetGlobalTrader0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGlobalTraderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetGlobalTrader)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGlobalTrader(ctx, req.(*GetGlobalTraderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGlobalTraderReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetTraderActivityPage0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTraderActivityPageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetTraderActivityPage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTraderActivityPage(ctx, req.(*GetTraderActivityPageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTraderActivityPageReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRecommenderList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRecommenderListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRecommenderList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRecommenderList(ctx, req.(*GetRecommenderListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRecommenderListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRewordPoolDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRewordPoolDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRewordPoolDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRewordPoolDetail(ctx, req.(*GetRewordPoolDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRewordPoolDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserCheckIn0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserCheckInRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserCheckIn)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserCheckIn(ctx, req.(*UserCheckInRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserCheckInReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRewordDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRewordDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRewordDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRewordDetail(ctx, req.(*GetRewordDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRewordDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GrandLuckyDraw0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GrandLuckyDrawRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGrandLuckyDraw)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GrandLuckyDraw(ctx, req.(*GrandLuckyDrawRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GrandLuckyDrawReply)
		return ctx.Result(200, reply)
	}
}

func _Service_StartDraw0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GrandLuckyDrawRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceStartDraw)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartDraw(ctx, req.(*GrandLuckyDrawRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartDrawReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetDepositDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDepositDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetDepositDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDepositDetail(ctx, req.(*GetDepositDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDepositDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SearchTrader0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchTraderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSearchTrader)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchTrader(ctx, req.(*SearchTraderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchTraderReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetBanner0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBannerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetBanner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBanner(ctx, req.(*GetBannerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBannerReply)
		return ctx.Result(200, reply)
	}
}

func _Service_Assist0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AssistRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceAssist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Assist(ctx, req.(*AssistRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AssistReply)
		return ctx.Result(200, reply)
	}
}

func _Service_WatchLiveCompleted0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WatchLiveRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceWatchLiveCompleted)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WatchLiveCompleted(ctx, req.(*WatchLiveRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WatchLiveReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FriendAssistance0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FriendAssistanceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFriendAssistance)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FriendAssistance(ctx, req.(*FriendAssistanceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FriendAssistanceReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInvitationPopupData0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInvitationPopupDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInvitationPopupData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInvitationPopupData(ctx, req.(*GetInvitationPopupDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInvitationPopupDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInviteRewardBannerData0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInviteRewardBannerDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInviteRewardBannerData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInviteRewardBannerData(ctx, req.(*GetInviteRewardBannerDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInviteRewardBannerDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetShareLinkData0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetShareLinkDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetShareLinkData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetShareLinkData(ctx, req.(*GetShareLinkDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetShareLinkDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInvitedRecordData0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInvitedRecordDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInvitedRecordData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInvitedRecordData(ctx, req.(*GetInvitedRecordDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInvitedRecordDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetVpsLevel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetVpsLevel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVpsLevel(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVpsLevelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetQuizInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetQuizInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQuizInfo(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetQuizInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SubmitQuiz0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SubmitQuizRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSubmitQuiz)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SubmitQuiz(ctx, req.(*SubmitQuizRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SubmitQuizReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetQuizRecord0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetQuizRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQuizRecord(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetQuizRecordReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInviterActivityTime0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInviterActivityTime)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInviterActivityTime(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInviterActivityTimeReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UpdateInviterActivityTime0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateInviterActivityTimeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUpdateInviterActivityTime)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateInviterActivityTime(ctx, req.(*UpdateInviterActivityTimeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateInviterActivityTimeReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserDivisionRewardLevelInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserDivisionRewardLevelInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserDivisionRewardLevelInfo(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserDivisionRewardLevelInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CreateUserDivisionRewardLevel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserDivisionRewardLevelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCreateUserDivisionRewardLevel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserDivisionRewardLevel(ctx, req.(*CreateUserDivisionRewardLevelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserDivisionRewardLevelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UpdateUserDivisionRewardLevel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserDivisionRewardLevelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUpdateUserDivisionRewardLevel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserDivisionRewardLevel(ctx, req.(*UpdateUserDivisionRewardLevelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_DeleteUserDivisionRewardLevel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserDivisionRewardLevelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceDeleteUserDivisionRewardLevel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserDivisionRewardLevel(ctx, req.(*DeleteUserDivisionRewardLevelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CreateUserDivisionInvitation0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserDivisionInvitationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCreateUserDivisionInvitation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserDivisionInvitation(ctx, req.(*CreateUserDivisionInvitationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserDivisionInvitationReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserDivisionInviterStatisticsInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserDivisionInviterStatisticsInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserDivisionInviterStatisticsInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserDivisionInviterStatisticsInfo(ctx, req.(*GetUserDivisionInviterStatisticsInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserDivisionInviterStatisticsInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserDivisionInviteeInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserDivisionInviteeInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserDivisionInviteeInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserDivisionInviteeInfo(ctx, req.(*GetUserDivisionInviteeInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserDivisionInviteeInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserDivisionActivityList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserDivisionActivityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserDivisionActivityList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserDivisionActivityList(ctx, req.(*GetUserDivisionActivityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserDivisionActivityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserDivisionActivityInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserDivisionActivityInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserDivisionActivityInfo(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserDivisionActivityInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserDivisionEntry0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserDivisionEntry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserDivisionEntry(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserDivisionEntryReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UpgradeVPS0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpgradeVPSRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUpgradeVPS)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpgradeVPS(ctx, req.(*UpgradeVPSRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpgradeVPSReply)
		return ctx.Result(200, reply)
	}
}

func _Service_PostUpgradeVPSStatus0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PostUpgradeVPSStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServicePostUpgradeVPSStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PostUpgradeVPSStatus(ctx, req.(*PostUpgradeVPSStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PostUpgradeVPSStatusReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	Assist(ctx context.Context, req *AssistRequest, opts ...http.CallOption) (rsp *AssistReply, err error)
	CreateUserDivisionInvitation(ctx context.Context, req *CreateUserDivisionInvitationRequest, opts ...http.CallOption) (rsp *CreateUserDivisionInvitationReply, err error)
	CreateUserDivisionRewardLevel(ctx context.Context, req *CreateUserDivisionRewardLevelRequest, opts ...http.CallOption) (rsp *CreateUserDivisionRewardLevelReply, err error)
	DeleteUserDivisionRewardLevel(ctx context.Context, req *DeleteUserDivisionRewardLevelRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	FriendAssistance(ctx context.Context, req *FriendAssistanceRequest, opts ...http.CallOption) (rsp *FriendAssistanceReply, err error)
	GetActivityMain(ctx context.Context, req *GetActivityMainRequest, opts ...http.CallOption) (rsp *GetActivityMainReply, err error)
	GetActivityShare(ctx context.Context, req *GetActivityShareRequest, opts ...http.CallOption) (rsp *GetActivityShareReply, err error)
	GetBanner(ctx context.Context, req *GetBannerRequest, opts ...http.CallOption) (rsp *GetBannerReply, err error)
	GetContent(ctx context.Context, req *GetContentRequest, opts ...http.CallOption) (rsp *GetContentReply, err error)
	GetDepositDetail(ctx context.Context, req *GetDepositDetailRequest, opts ...http.CallOption) (rsp *GetDepositDetailReply, err error)
	GetGlobalTrader(ctx context.Context, req *GetGlobalTraderRequest, opts ...http.CallOption) (rsp *GetGlobalTraderReply, err error)
	GetGrowthCenterEntry(ctx context.Context, req *GetGrowthCenterEntryRequest, opts ...http.CallOption) (rsp *GetGrowthCenterEntryReply, err error)
	GetIdentityCarousel(ctx context.Context, req *GetIdentityCarouselRequest, opts ...http.CallOption) (rsp *GetIdentityCarouselReply, err error)
	GetIdentityRule(ctx context.Context, req *GetIdentityRuleRequest, opts ...http.CallOption) (rsp *GetIdentityRuleReply, err error)
	GetIdentityShare(ctx context.Context, req *GetIdentityShareRequest, opts ...http.CallOption) (rsp *GetIdentityShareReply, err error)
	GetInvitationPopupData(ctx context.Context, req *GetInvitationPopupDataRequest, opts ...http.CallOption) (rsp *GetInvitationPopupDataReply, err error)
	GetInviteRewardBannerData(ctx context.Context, req *GetInviteRewardBannerDataRequest, opts ...http.CallOption) (rsp *GetInviteRewardBannerDataReply, err error)
	GetInvitedRecordData(ctx context.Context, req *GetInvitedRecordDataRequest, opts ...http.CallOption) (rsp *GetInvitedRecordDataReply, err error)
	GetInviterActivityTime(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetInviterActivityTimeReply, err error)
	GetQuizInfo(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetQuizInfoReply, err error)
	GetQuizRecord(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetQuizRecordReply, err error)
	GetRecommendTrader(ctx context.Context, req *GetRecommendTraderRequest, opts ...http.CallOption) (rsp *GetRecommendTraderReply, err error)
	GetRecommenderList(ctx context.Context, req *GetRecommenderListRequest, opts ...http.CallOption) (rsp *GetRecommenderListReply, err error)
	GetRewordDetail(ctx context.Context, req *GetRewordDetailRequest, opts ...http.CallOption) (rsp *GetRewordDetailReply, err error)
	GetRewordPoolDetail(ctx context.Context, req *GetRewordPoolDetailRequest, opts ...http.CallOption) (rsp *GetRewordPoolDetailReply, err error)
	GetShareLinkData(ctx context.Context, req *GetShareLinkDataRequest, opts ...http.CallOption) (rsp *GetShareLinkDataReply, err error)
	GetTraderActivityPage(ctx context.Context, req *GetTraderActivityPageRequest, opts ...http.CallOption) (rsp *GetTraderActivityPageReply, err error)
	GetUpgradeIdentity(ctx context.Context, req *GetUpgradeIdentityRequest, opts ...http.CallOption) (rsp *GetUpgradeIdentityReply, err error)
	GetUserDivisionActivityInfo(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetUserDivisionActivityInfoReply, err error)
	GetUserDivisionActivityList(ctx context.Context, req *GetUserDivisionActivityListRequest, opts ...http.CallOption) (rsp *GetUserDivisionActivityListReply, err error)
	GetUserDivisionEntry(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetUserDivisionEntryReply, err error)
	GetUserDivisionInviteeInfo(ctx context.Context, req *GetUserDivisionInviteeInfoRequest, opts ...http.CallOption) (rsp *GetUserDivisionInviteeInfoReply, err error)
	GetUserDivisionInviterStatisticsInfo(ctx context.Context, req *GetUserDivisionInviterStatisticsInfoRequest, opts ...http.CallOption) (rsp *GetUserDivisionInviterStatisticsInfoReply, err error)
	GetUserDivisionRewardLevelInfo(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetUserDivisionRewardLevelInfoReply, err error)
	GetUserGrowthDetail(ctx context.Context, req *GetUserGrowthDetailRequest, opts ...http.CallOption) (rsp *GetUserGrowthDetailReply, err error)
	GetUserInfo(ctx context.Context, req *GetUserInfoRequest, opts ...http.CallOption) (rsp *GetUserInfoReply, err error)
	GetVpsLevel(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetVpsLevelReply, err error)
	GrandLuckyDraw(ctx context.Context, req *GrandLuckyDrawRequest, opts ...http.CallOption) (rsp *GrandLuckyDrawReply, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	PostUpgradeVPSStatus(ctx context.Context, req *PostUpgradeVPSStatusRequest, opts ...http.CallOption) (rsp *PostUpgradeVPSStatusReply, err error)
	PostUserIdentitySwitch(ctx context.Context, req *PostUserIdentitySwitchRequest, opts ...http.CallOption) (rsp *PostUserIdentitySwitchReply, err error)
	SearchTrader(ctx context.Context, req *SearchTraderRequest, opts ...http.CallOption) (rsp *SearchTraderReply, err error)
	StartDraw(ctx context.Context, req *GrandLuckyDrawRequest, opts ...http.CallOption) (rsp *StartDrawReply, err error)
	StringReply(ctx context.Context, req *StringReplyRequest, opts ...http.CallOption) (rsp *common.StringReply, err error)
	SubmitQuiz(ctx context.Context, req *SubmitQuizRequest, opts ...http.CallOption) (rsp *SubmitQuizReply, err error)
	UpdateInviterActivityTime(ctx context.Context, req *UpdateInviterActivityTimeRequest, opts ...http.CallOption) (rsp *UpdateInviterActivityTimeReply, err error)
	UpdateUserDivisionRewardLevel(ctx context.Context, req *UpdateUserDivisionRewardLevelRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpgradeVPS(ctx context.Context, req *UpgradeVPSRequest, opts ...http.CallOption) (rsp *UpgradeVPSReply, err error)
	UserCheckIn(ctx context.Context, req *UserCheckInRequest, opts ...http.CallOption) (rsp *UserCheckInReply, err error)
	WatchLiveCompleted(ctx context.Context, req *WatchLiveRequest, opts ...http.CallOption) (rsp *WatchLiveReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) Assist(ctx context.Context, in *AssistRequest, opts ...http.CallOption) (*AssistReply, error) {
	var out AssistReply
	pattern := "/v1/app/investment/assist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceAssist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) CreateUserDivisionInvitation(ctx context.Context, in *CreateUserDivisionInvitationRequest, opts ...http.CallOption) (*CreateUserDivisionInvitationReply, error) {
	var out CreateUserDivisionInvitationReply
	pattern := "/v1/userdivision/invitation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceCreateUserDivisionInvitation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) CreateUserDivisionRewardLevel(ctx context.Context, in *CreateUserDivisionRewardLevelRequest, opts ...http.CallOption) (*CreateUserDivisionRewardLevelReply, error) {
	var out CreateUserDivisionRewardLevelReply
	pattern := "/v1/userdivision/reward"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceCreateUserDivisionRewardLevel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) DeleteUserDivisionRewardLevel(ctx context.Context, in *DeleteUserDivisionRewardLevelRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/userdivision/reward/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceDeleteUserDivisionRewardLevel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) FriendAssistance(ctx context.Context, in *FriendAssistanceRequest, opts ...http.CallOption) (*FriendAssistanceReply, error) {
	var out FriendAssistanceReply
	pattern := "/v1/app/investment/friendassistance"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFriendAssistance))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetActivityMain(ctx context.Context, in *GetActivityMainRequest, opts ...http.CallOption) (*GetActivityMainReply, error) {
	var out GetActivityMainReply
	pattern := "/v1/app/investment/main"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetActivityMain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetActivityShare(ctx context.Context, in *GetActivityShareRequest, opts ...http.CallOption) (*GetActivityShareReply, error) {
	var out GetActivityShareReply
	pattern := "/v1/app/investment/share"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetActivityShare))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetBanner(ctx context.Context, in *GetBannerRequest, opts ...http.CallOption) (*GetBannerReply, error) {
	var out GetBannerReply
	pattern := "/v1/app/investment/banner"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetBanner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetContent(ctx context.Context, in *GetContentRequest, opts ...http.CallOption) (*GetContentReply, error) {
	var out GetContentReply
	pattern := "/v1/app/content"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetDepositDetail(ctx context.Context, in *GetDepositDetailRequest, opts ...http.CallOption) (*GetDepositDetailReply, error) {
	var out GetDepositDetailReply
	pattern := "/v1/app/deposit/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetDepositDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetGlobalTrader(ctx context.Context, in *GetGlobalTraderRequest, opts ...http.CallOption) (*GetGlobalTraderReply, error) {
	var out GetGlobalTraderReply
	pattern := "/v1/app/global/trader"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetGlobalTrader))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetGrowthCenterEntry(ctx context.Context, in *GetGrowthCenterEntryRequest, opts ...http.CallOption) (*GetGrowthCenterEntryReply, error) {
	var out GetGrowthCenterEntryReply
	pattern := "/v1/app/identity/switch"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetGrowthCenterEntry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetIdentityCarousel(ctx context.Context, in *GetIdentityCarouselRequest, opts ...http.CallOption) (*GetIdentityCarouselReply, error) {
	var out GetIdentityCarouselReply
	pattern := "/v1/app/identity/carousel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetIdentityCarousel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetIdentityRule(ctx context.Context, in *GetIdentityRuleRequest, opts ...http.CallOption) (*GetIdentityRuleReply, error) {
	var out GetIdentityRuleReply
	pattern := "/v1/app/identity/rule"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetIdentityRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetIdentityShare(ctx context.Context, in *GetIdentityShareRequest, opts ...http.CallOption) (*GetIdentityShareReply, error) {
	var out GetIdentityShareReply
	pattern := "/v1/app/identity/share"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetIdentityShare))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetInvitationPopupData(ctx context.Context, in *GetInvitationPopupDataRequest, opts ...http.CallOption) (*GetInvitationPopupDataReply, error) {
	var out GetInvitationPopupDataReply
	pattern := "/v1/userdivision/getinvitationpopupdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInvitationPopupData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetInviteRewardBannerData(ctx context.Context, in *GetInviteRewardBannerDataRequest, opts ...http.CallOption) (*GetInviteRewardBannerDataReply, error) {
	var out GetInviteRewardBannerDataReply
	pattern := "/v1/userdivision/getinviterewardbannerdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInviteRewardBannerData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetInvitedRecordData(ctx context.Context, in *GetInvitedRecordDataRequest, opts ...http.CallOption) (*GetInvitedRecordDataReply, error) {
	var out GetInvitedRecordDataReply
	pattern := "/v1/userdivision/invitedrecord"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInvitedRecordData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetInviterActivityTime(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetInviterActivityTimeReply, error) {
	var out GetInviterActivityTimeReply
	pattern := "/v1/userdivision/activity/time"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInviterActivityTime))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetQuizInfo(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetQuizInfoReply, error) {
	var out GetQuizInfoReply
	pattern := "/v1/userdivision/getquizinfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetQuizInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetQuizRecord(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetQuizRecordReply, error) {
	var out GetQuizRecordReply
	pattern := "/v1/userdivision/getquizrecord"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetQuizRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetRecommendTrader(ctx context.Context, in *GetRecommendTraderRequest, opts ...http.CallOption) (*GetRecommendTraderReply, error) {
	var out GetRecommendTraderReply
	pattern := "/v1/app/search/recommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRecommendTrader))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetRecommenderList(ctx context.Context, in *GetRecommenderListRequest, opts ...http.CallOption) (*GetRecommenderListReply, error) {
	var out GetRecommenderListReply
	pattern := "/v1/app/recommender/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRecommenderList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetRewordDetail(ctx context.Context, in *GetRewordDetailRequest, opts ...http.CallOption) (*GetRewordDetailReply, error) {
	var out GetRewordDetailReply
	pattern := "/v1/app/rewords/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRewordDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetRewordPoolDetail(ctx context.Context, in *GetRewordPoolDetailRequest, opts ...http.CallOption) (*GetRewordPoolDetailReply, error) {
	var out GetRewordPoolDetailReply
	pattern := "/v1/app/reword/pool"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRewordPoolDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetShareLinkData(ctx context.Context, in *GetShareLinkDataRequest, opts ...http.CallOption) (*GetShareLinkDataReply, error) {
	var out GetShareLinkDataReply
	pattern := "/v1/userdivision/getsharelinkdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetShareLinkData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetTraderActivityPage(ctx context.Context, in *GetTraderActivityPageRequest, opts ...http.CallOption) (*GetTraderActivityPageReply, error) {
	var out GetTraderActivityPageReply
	pattern := "/v1/app/trader/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetTraderActivityPage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUpgradeIdentity(ctx context.Context, in *GetUpgradeIdentityRequest, opts ...http.CallOption) (*GetUpgradeIdentityReply, error) {
	var out GetUpgradeIdentityReply
	pattern := "/v1/app/identity/entry"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUpgradeIdentity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserDivisionActivityInfo(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetUserDivisionActivityInfoReply, error) {
	var out GetUserDivisionActivityInfoReply
	pattern := "/v1/userdivision/activity"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserDivisionActivityInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserDivisionActivityList(ctx context.Context, in *GetUserDivisionActivityListRequest, opts ...http.CallOption) (*GetUserDivisionActivityListReply, error) {
	var out GetUserDivisionActivityListReply
	pattern := "/v1/userdivision/activities"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserDivisionActivityList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserDivisionEntry(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetUserDivisionEntryReply, error) {
	var out GetUserDivisionEntryReply
	pattern := "/v1/userdivision/entry"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserDivisionEntry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserDivisionInviteeInfo(ctx context.Context, in *GetUserDivisionInviteeInfoRequest, opts ...http.CallOption) (*GetUserDivisionInviteeInfoReply, error) {
	var out GetUserDivisionInviteeInfoReply
	pattern := "/v1/userdivision/inviteeInfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserDivisionInviteeInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserDivisionInviterStatisticsInfo(ctx context.Context, in *GetUserDivisionInviterStatisticsInfoRequest, opts ...http.CallOption) (*GetUserDivisionInviterStatisticsInfoReply, error) {
	var out GetUserDivisionInviterStatisticsInfoReply
	pattern := "/v1/userdivision/inviterInfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserDivisionInviterStatisticsInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserDivisionRewardLevelInfo(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetUserDivisionRewardLevelInfoReply, error) {
	var out GetUserDivisionRewardLevelInfoReply
	pattern := "/v1/userdivision/reward"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserDivisionRewardLevelInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserGrowthDetail(ctx context.Context, in *GetUserGrowthDetailRequest, opts ...http.CallOption) (*GetUserGrowthDetailReply, error) {
	var out GetUserGrowthDetailReply
	pattern := "/v1/app/identity/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserGrowthDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...http.CallOption) (*GetUserInfoReply, error) {
	var out GetUserInfoReply
	pattern := "/v1/user_info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetVpsLevel(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetVpsLevelReply, error) {
	var out GetVpsLevelReply
	pattern := "/v1/userdivision/getuservpslevel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetVpsLevel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GrandLuckyDraw(ctx context.Context, in *GrandLuckyDrawRequest, opts ...http.CallOption) (*GrandLuckyDrawReply, error) {
	var out GrandLuckyDrawReply
	pattern := "/v1/app/luckydraw/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGrandLuckyDraw))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) PostUpgradeVPSStatus(ctx context.Context, in *PostUpgradeVPSStatusRequest, opts ...http.CallOption) (*PostUpgradeVPSStatusReply, error) {
	var out PostUpgradeVPSStatusReply
	pattern := "/v1/userdivision/upgrade/callback"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServicePostUpgradeVPSStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) PostUserIdentitySwitch(ctx context.Context, in *PostUserIdentitySwitchRequest, opts ...http.CallOption) (*PostUserIdentitySwitchReply, error) {
	var out PostUserIdentitySwitchReply
	pattern := "/v1/app/identity"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServicePostUserIdentitySwitch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) SearchTrader(ctx context.Context, in *SearchTraderRequest, opts ...http.CallOption) (*SearchTraderReply, error) {
	var out SearchTraderReply
	pattern := "/v1/app/search/trader"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceSearchTrader))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) StartDraw(ctx context.Context, in *GrandLuckyDrawRequest, opts ...http.CallOption) (*StartDrawReply, error) {
	var out StartDrawReply
	pattern := "/v1/app/startdraw"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceStartDraw))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) StringReply(ctx context.Context, in *StringReplyRequest, opts ...http.CallOption) (*common.StringReply, error) {
	var out common.StringReply
	pattern := "/v1/string_reply"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceStringReply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) SubmitQuiz(ctx context.Context, in *SubmitQuizRequest, opts ...http.CallOption) (*SubmitQuizReply, error) {
	var out SubmitQuizReply
	pattern := "/v1/userdivision/submitquiz"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSubmitQuiz))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) UpdateInviterActivityTime(ctx context.Context, in *UpdateInviterActivityTimeRequest, opts ...http.CallOption) (*UpdateInviterActivityTimeReply, error) {
	var out UpdateInviterActivityTimeReply
	pattern := "/v1/userdivision/activity/time"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUpdateInviterActivityTime))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) UpdateUserDivisionRewardLevel(ctx context.Context, in *UpdateUserDivisionRewardLevelRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/userdivision/reward/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUpdateUserDivisionRewardLevel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) UpgradeVPS(ctx context.Context, in *UpgradeVPSRequest, opts ...http.CallOption) (*UpgradeVPSReply, error) {
	var out UpgradeVPSReply
	pattern := "/v1/userdivision/upgrade/vps"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUpgradeVPS))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) UserCheckIn(ctx context.Context, in *UserCheckInRequest, opts ...http.CallOption) (*UserCheckInReply, error) {
	var out UserCheckInReply
	pattern := "/v1/app/user/checkin"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUserCheckIn))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) WatchLiveCompleted(ctx context.Context, in *WatchLiveRequest, opts ...http.CallOption) (*WatchLiveReply, error) {
	var out WatchLiveReply
	pattern := "/v1/app/live/completed"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceWatchLiveCompleted))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
