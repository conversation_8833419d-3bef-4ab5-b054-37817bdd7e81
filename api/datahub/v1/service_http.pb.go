// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: datahub/v1/service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	common "wiki_user_center/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceFindStock = "/api.datahub.v1.Service/FindStock"
const OperationServiceFindStockDescription = "/api.datahub.v1.Service/FindStockDescription"
const OperationServiceFindStockNotice = "/api.datahub.v1.Service/FindStockNotice"
const OperationServiceFindTraderRanking = "/api.datahub.v1.Service/FindTraderRanking"
const OperationServiceGetStock = "/api.datahub.v1.Service/GetStock"
const OperationServiceGetStockFinanceOverview = "/api.datahub.v1.Service/GetStockFinanceOverview"
const OperationServiceGetStockFinanceStatement = "/api.datahub.v1.Service/GetStockFinanceStatement"
const OperationServiceGetStockHolder = "/api.datahub.v1.Service/GetStockHolder"
const OperationServiceGetStockNotice = "/api.datahub.v1.Service/GetStockNotice"
const OperationServiceGetStockQuotes = "/api.datahub.v1.Service/GetStockQuotes"
const OperationServiceGetTraderRankingDetail = "/api.datahub.v1.Service/GetTraderRankingDetail"
const OperationServiceHealthy = "/api.datahub.v1.Service/Healthy"
const OperationServiceTraderRanking = "/api.datahub.v1.Service/TraderRanking"

type ServiceHTTPServer interface {
	// FindStock 获取股票列表
	FindStock(context.Context, *FindStockRequest) (*FindStockReply, error)
	// FindStockDescription 通过股票代码批量获取全文本翻译
	FindStockDescription(context.Context, *FindStockDescriptionRequest) (*FindStockDescriptionReply, error)
	// FindStockNotice 股票公告
	FindStockNotice(context.Context, *FindStockNoticeRequest) (*FindStockNoticeReply, error)
	// FindTraderRanking 交易商排行
	FindTraderRanking(context.Context, *FindTraderRankingRequest) (*FindTraderRankingReply, error)
	// GetStock 股票详情
	GetStock(context.Context, *GetStockRequest) (*GetStockReply, error)
	// GetStockFinanceOverview 股票财务概览
	GetStockFinanceOverview(context.Context, *GetStockFinanceOverviewRequest) (*GetStockFinanceOverviewReply, error)
	// GetStockFinanceStatement 股票财务报表
	GetStockFinanceStatement(context.Context, *GetStockFinanceStatementRequest) (*GetStockFinanceStatementReply, error)
	// GetStockHolder 股票股东
	GetStockHolder(context.Context, *GetStockHolderRequest) (*GetStockHolderReply, error)
	// GetStockNotice 股票详情
	GetStockNotice(context.Context, *GetStockNoticeRequest) (*Notice, error)
	// GetStockQuotes 行情信息
	GetStockQuotes(context.Context, *GetStockQuotesRequest) (*GetStockQuotesReply, error)
	// GetTraderRankingDetail 交易商排行详情
	GetTraderRankingDetail(context.Context, *GetTraderRankingDetailRequest) (*GetTraderRankingDetailReply, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// TraderRanking 实时计算排行
	TraderRanking(context.Context, *TraderRankingRequest) (*TraderRankingReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy2_HTTP_Handler(srv))
	r.GET("/v1/stock", _Service_GetStock0_HTTP_Handler(srv))
	r.GET("/v1/stock/holder", _Service_GetStockHolder0_HTTP_Handler(srv))
	r.GET("/v1/stock/finance/overview", _Service_GetStockFinanceOverview0_HTTP_Handler(srv))
	r.GET("/v1/stock/finance/statement", _Service_GetStockFinanceStatement0_HTTP_Handler(srv))
	r.GET("/v1/stock/notice", _Service_FindStockNotice0_HTTP_Handler(srv))
	r.GET("/v1/stock/notice/detail", _Service_GetStockNotice0_HTTP_Handler(srv))
	r.POST("/v1/stocks", _Service_FindStock0_HTTP_Handler(srv))
	r.POST("/v1/stock/description", _Service_FindStockDescription0_HTTP_Handler(srv))
	r.GET("/v1/trader/ranking", _Service_FindTraderRanking0_HTTP_Handler(srv))
	r.GET("/v1/trader/ranking/detail", _Service_GetTraderRankingDetail0_HTTP_Handler(srv))
	r.GET("/v1/trader/ranking/calculate", _Service_TraderRanking0_HTTP_Handler(srv))
	r.GET("/v1/stock/quotes", _Service_GetStockQuotes0_HTTP_Handler(srv))
}

func _Service_Healthy2_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStock0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStockRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStock)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStock(ctx, req.(*GetStockRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStockReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStockHolder0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStockHolderRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStockHolder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStockHolder(ctx, req.(*GetStockHolderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStockHolderReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStockFinanceOverview0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStockFinanceOverviewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStockFinanceOverview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStockFinanceOverview(ctx, req.(*GetStockFinanceOverviewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStockFinanceOverviewReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStockFinanceStatement0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStockFinanceStatementRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStockFinanceStatement)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStockFinanceStatement(ctx, req.(*GetStockFinanceStatementRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStockFinanceStatementReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindStockNotice0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindStockNoticeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindStockNotice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindStockNotice(ctx, req.(*FindStockNoticeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindStockNoticeReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStockNotice0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStockNoticeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStockNotice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStockNotice(ctx, req.(*GetStockNoticeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Notice)
		return ctx.Result(200, reply)
	}
}

func _Service_FindStock0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindStockRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindStock)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindStock(ctx, req.(*FindStockRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindStockReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindStockDescription0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindStockDescriptionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindStockDescription)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindStockDescription(ctx, req.(*FindStockDescriptionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindStockDescriptionReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindTraderRanking0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindTraderRankingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindTraderRanking)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindTraderRanking(ctx, req.(*FindTraderRankingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindTraderRankingReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetTraderRankingDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTraderRankingDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetTraderRankingDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTraderRankingDetail(ctx, req.(*GetTraderRankingDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTraderRankingDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TraderRanking0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TraderRankingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTraderRanking)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TraderRanking(ctx, req.(*TraderRankingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TraderRankingReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStockQuotes0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStockQuotesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStockQuotes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStockQuotes(ctx, req.(*GetStockQuotesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStockQuotesReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	FindStock(ctx context.Context, req *FindStockRequest, opts ...http.CallOption) (rsp *FindStockReply, err error)
	FindStockDescription(ctx context.Context, req *FindStockDescriptionRequest, opts ...http.CallOption) (rsp *FindStockDescriptionReply, err error)
	FindStockNotice(ctx context.Context, req *FindStockNoticeRequest, opts ...http.CallOption) (rsp *FindStockNoticeReply, err error)
	FindTraderRanking(ctx context.Context, req *FindTraderRankingRequest, opts ...http.CallOption) (rsp *FindTraderRankingReply, err error)
	GetStock(ctx context.Context, req *GetStockRequest, opts ...http.CallOption) (rsp *GetStockReply, err error)
	GetStockFinanceOverview(ctx context.Context, req *GetStockFinanceOverviewRequest, opts ...http.CallOption) (rsp *GetStockFinanceOverviewReply, err error)
	GetStockFinanceStatement(ctx context.Context, req *GetStockFinanceStatementRequest, opts ...http.CallOption) (rsp *GetStockFinanceStatementReply, err error)
	GetStockHolder(ctx context.Context, req *GetStockHolderRequest, opts ...http.CallOption) (rsp *GetStockHolderReply, err error)
	GetStockNotice(ctx context.Context, req *GetStockNoticeRequest, opts ...http.CallOption) (rsp *Notice, err error)
	GetStockQuotes(ctx context.Context, req *GetStockQuotesRequest, opts ...http.CallOption) (rsp *GetStockQuotesReply, err error)
	GetTraderRankingDetail(ctx context.Context, req *GetTraderRankingDetailRequest, opts ...http.CallOption) (rsp *GetTraderRankingDetailReply, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	TraderRanking(ctx context.Context, req *TraderRankingRequest, opts ...http.CallOption) (rsp *TraderRankingReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) FindStock(ctx context.Context, in *FindStockRequest, opts ...http.CallOption) (*FindStockReply, error) {
	var out FindStockReply
	pattern := "/v1/stocks"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceFindStock))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) FindStockDescription(ctx context.Context, in *FindStockDescriptionRequest, opts ...http.CallOption) (*FindStockDescriptionReply, error) {
	var out FindStockDescriptionReply
	pattern := "/v1/stock/description"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceFindStockDescription))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) FindStockNotice(ctx context.Context, in *FindStockNoticeRequest, opts ...http.CallOption) (*FindStockNoticeReply, error) {
	var out FindStockNoticeReply
	pattern := "/v1/stock/notice"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindStockNotice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) FindTraderRanking(ctx context.Context, in *FindTraderRankingRequest, opts ...http.CallOption) (*FindTraderRankingReply, error) {
	var out FindTraderRankingReply
	pattern := "/v1/trader/ranking"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindTraderRanking))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStock(ctx context.Context, in *GetStockRequest, opts ...http.CallOption) (*GetStockReply, error) {
	var out GetStockReply
	pattern := "/v1/stock"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStock))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStockFinanceOverview(ctx context.Context, in *GetStockFinanceOverviewRequest, opts ...http.CallOption) (*GetStockFinanceOverviewReply, error) {
	var out GetStockFinanceOverviewReply
	pattern := "/v1/stock/finance/overview"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStockFinanceOverview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStockFinanceStatement(ctx context.Context, in *GetStockFinanceStatementRequest, opts ...http.CallOption) (*GetStockFinanceStatementReply, error) {
	var out GetStockFinanceStatementReply
	pattern := "/v1/stock/finance/statement"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStockFinanceStatement))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStockHolder(ctx context.Context, in *GetStockHolderRequest, opts ...http.CallOption) (*GetStockHolderReply, error) {
	var out GetStockHolderReply
	pattern := "/v1/stock/holder"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStockHolder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStockNotice(ctx context.Context, in *GetStockNoticeRequest, opts ...http.CallOption) (*Notice, error) {
	var out Notice
	pattern := "/v1/stock/notice/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStockNotice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStockQuotes(ctx context.Context, in *GetStockQuotesRequest, opts ...http.CallOption) (*GetStockQuotesReply, error) {
	var out GetStockQuotesReply
	pattern := "/v1/stock/quotes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStockQuotes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetTraderRankingDetail(ctx context.Context, in *GetTraderRankingDetailRequest, opts ...http.CallOption) (*GetTraderRankingDetailReply, error) {
	var out GetTraderRankingDetailReply
	pattern := "/v1/trader/ranking/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetTraderRankingDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) TraderRanking(ctx context.Context, in *TraderRankingRequest, opts ...http.CallOption) (*TraderRankingReply, error) {
	var out TraderRankingReply
	pattern := "/v1/trader/ranking/calculate"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceTraderRanking))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
