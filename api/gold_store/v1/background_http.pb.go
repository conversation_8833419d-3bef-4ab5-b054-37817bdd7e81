// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: gold_store/v1/background.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	common "wiki_user_center/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationBackgroundAddGoods = "/api.gold_store.v1.Background/AddGoods"
const OperationBackgroundAdminOrderDetail = "/api.gold_store.v1.Background/AdminOrderDetail"
const OperationBackgroundAdminOrderList = "/api.gold_store.v1.Background/AdminOrderList"
const OperationBackgroundCreateSignConfig = "/api.gold_store.v1.Background/CreateSignConfig"
const OperationBackgroundCreateTask = "/api.gold_store.v1.Background/CreateTask"
const OperationBackgroundDeleteGiftCardSetting = "/api.gold_store.v1.Background/DeleteGiftCardSetting"
const OperationBackgroundDeleteGoods = "/api.gold_store.v1.Background/DeleteGoods"
const OperationBackgroundDeleteSignConfig = "/api.gold_store.v1.Background/DeleteSignConfig"
const OperationBackgroundDeleteTask = "/api.gold_store.v1.Background/DeleteTask"
const OperationBackgroundEditGiftCardSetting = "/api.gold_store.v1.Background/EditGiftCardSetting"
const OperationBackgroundFetchReportOrders = "/api.gold_store.v1.Background/FetchReportOrders"
const OperationBackgroundGetGoodsInfo = "/api.gold_store.v1.Background/GetGoodsInfo"
const OperationBackgroundGetGoodsList = "/api.gold_store.v1.Background/GetGoodsList"
const OperationBackgroundGetSingleGiftCardSetting = "/api.gold_store.v1.Background/GetSingleGiftCardSetting"
const OperationBackgroundGetSingleGiftCardSettingIsSend = "/api.gold_store.v1.Background/GetSingleGiftCardSettingIsSend"
const OperationBackgroundGetStaticSpec = "/api.gold_store.v1.Background/GetStaticSpec"
const OperationBackgroundGetTaskDetail = "/api.gold_store.v1.Background/GetTaskDetail"
const OperationBackgroundGetTaskTypes = "/api.gold_store.v1.Background/GetTaskTypes"
const OperationBackgroundGiftCardSettingPageList = "/api.gold_store.v1.Background/GiftCardSettingPageList"
const OperationBackgroundLabelList = "/api.gold_store.v1.Background/LabelList"
const OperationBackgroundListSignConfig = "/api.gold_store.v1.Background/ListSignConfig"
const OperationBackgroundListTask = "/api.gold_store.v1.Background/ListTask"
const OperationBackgroundListTaskGoods = "/api.gold_store.v1.Background/ListTaskGoods"
const OperationBackgroundListTaskProgress = "/api.gold_store.v1.Background/ListTaskProgress"
const OperationBackgroundOrderDeliver = "/api.gold_store.v1.Background/OrderDeliver"
const OperationBackgroundSendGiftCard = "/api.gold_store.v1.Background/SendGiftCard"
const OperationBackgroundSendGiftCardRecord = "/api.gold_store.v1.Background/SendGiftCardRecord"
const OperationBackgroundSetGoodsStatus = "/api.gold_store.v1.Background/SetGoodsStatus"
const OperationBackgroundUpdateGoods = "/api.gold_store.v1.Background/UpdateGoods"
const OperationBackgroundUpdateSignConfig = "/api.gold_store.v1.Background/UpdateSignConfig"
const OperationBackgroundUpdateTaskConfig = "/api.gold_store.v1.Background/UpdateTaskConfig"
const OperationBackgroundUpdateTaskStatus = "/api.gold_store.v1.Background/UpdateTaskStatus"

type BackgroundHTTPServer interface {
	// AddGoods 新增商品
	AddGoods(context.Context, *GoodsInfo) (*AddGoodsReply, error)
	AdminOrderDetail(context.Context, *AdminOrderDetailRequest) (*AdminOrderDetailReply, error)
	// AdminOrderList ===========================================================
	// =========================== 订单 ===========================
	// ===========================================================
	AdminOrderList(context.Context, *AdminOrderListRequest) (*AdminOrderListReply, error)
	// CreateSignConfig 新增签到配置
	CreateSignConfig(context.Context, *CreateSignConfigRequest) (*CreateSignConfigReply, error)
	// CreateTask 新增任务
	CreateTask(context.Context, *AdminCreateTaskRequest) (*AdminCreateTaskReply, error)
	// DeleteGiftCardSetting删除兑换卡配置
	DeleteGiftCardSetting(context.Context, *DeleteGiftCardSettingRequest) (*DeleteGiftCardSettingReply, error)
	// DeleteGoods 删除商品
	DeleteGoods(context.Context, *DeleteGoodsRequest) (*common.EmptyReply, error)
	// DeleteSignConfig 删除签到配置
	DeleteSignConfig(context.Context, *DeleteSignConfigRequest) (*DeleteSignConfigReply, error)
	// DeleteTask 删除任务
	DeleteTask(context.Context, *AdminDeleteTaskRequest) (*common.EmptyReply, error)
	// EditGiftCardSetting 编辑兑换卡配置
	EditGiftCardSetting(context.Context, *EditGiftCardSettingRequest) (*EditGiftCardSettingReply, error)
	FetchReportOrders(context.Context, *common.EmptyRequest) (*common.EmptyReply, error)
	// GetGoodsInfo 商品详情
	GetGoodsInfo(context.Context, *GetGoodsInfoRequest) (*GoodsInfo, error)
	// GetGoodsList 商品列表
	GetGoodsList(context.Context, *GetGoodsListRequest) (*GetGoodsListReply, error)
	// GetSingleGiftCardSetting获取单个兑换卡配置
	GetSingleGiftCardSetting(context.Context, *GetSingleGiftCardSettingRequest) (*GetSingleGiftCardSettingReply, error)
	// GetSingleGiftCardSettingIsSend  获取单个兑换卡是否发放
	GetSingleGiftCardSettingIsSend(context.Context, *GetSingleGiftCardSettingIsSendRequest) (*GetSingleGiftCardSettingIsSendReply, error)
	// GetStaticSpec ===========================================================
	// =========================== 商品 ===========================
	// ===========================================================
	// 商品静态规格
	GetStaticSpec(context.Context, *GetStaticSpecRequest) (*GetStaticSpecReply, error)
	// GetTaskDetail 获取任务详情
	GetTaskDetail(context.Context, *AdminGetTaskDetailRequest) (*AdminGetTaskDetailReply, error)
	// GetTaskTypes ===========================================================
	// =========================== 任务 ===========================
	// ===========================================================
	// 获取任务类型列表
	GetTaskTypes(context.Context, *GetTaskTypesRequest) (*GetTaskTypesResponse, error)
	// GiftCardSettingPageList ===========================================================
	// =========================== 兑换卡 ===========================
	// ===========================================================
	//兑换卡配置列表
	GiftCardSettingPageList(context.Context, *GiftCardSettingPageListRequest) (*GiftCardSettingPageListReply, error)
	// LabelList ===========================================================
	// =========================== 标签 ===========================
	// ===========================================================
	LabelList(context.Context, *common.EmptyRequest) (*LabelListReply, error)
	// ListSignConfig ===========================================================
	// =========================== 签到 ===========================
	// ===========================================================
	// 获取签到配置列表
	ListSignConfig(context.Context, *ListSignConfigRequest) (*ListSignConfigReply, error)
	// ListTask 查询任务列表
	ListTask(context.Context, *AdminListTaskRequest) (*AdminListTaskReply, error)
	// ListTaskGoods 查询任务配置的商品 实物，虚拟
	ListTaskGoods(context.Context, *common.EmptyRequest) (*AdminListTaskGoodsReply, error)
	// ListTaskProgress 查询任务进度列表
	ListTaskProgress(context.Context, *AdminListTaskProgressRequest) (*AdminListTaskProgressReply, error)
	OrderDeliver(context.Context, *OrderDeliverRequest) (*common.EmptyReply, error)
	// SendGiftCard 发放兑换卡
	SendGiftCard(context.Context, *SendGiftCardRequest) (*SendGiftCardReply, error)
	// SendGiftCardRecord 发放记录
	SendGiftCardRecord(context.Context, *SendGiftCardRecordRequest) (*SendGiftCardRecordReply, error)
	// SetGoodsStatus 修改商品上下架
	SetGoodsStatus(context.Context, *GoodsStatusRequest) (*common.EmptyReply, error)
	// UpdateGoods 修改商品
	UpdateGoods(context.Context, *GoodsInfo) (*common.EmptyReply, error)
	// UpdateSignConfig 修改签到配置
	UpdateSignConfig(context.Context, *UpdateSignConfigRequest) (*UpdateSignConfigReply, error)
	// UpdateTaskConfig 修改任务信息
	UpdateTaskConfig(context.Context, *AdminUpdateTaskConfigRequest) (*common.EmptyReply, error)
	// UpdateTaskStatus 修改任务状态
	UpdateTaskStatus(context.Context, *AdminUpdateTaskStatusRequest) (*common.EmptyReply, error)
}

func RegisterBackgroundHTTPServer(s *http.Server, srv BackgroundHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/admin/label", _Background_LabelList0_HTTP_Handler(srv))
	r.GET("/v1/admin/goods/static/spec", _Background_GetStaticSpec0_HTTP_Handler(srv))
	r.POST("/v1/admin/goods", _Background_AddGoods0_HTTP_Handler(srv))
	r.PUT("/v1/admin/goods/{id}", _Background_UpdateGoods0_HTTP_Handler(srv))
	r.GET("/v1/admin/goods/{id}", _Background_GetGoodsInfo0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/goods/{id}", _Background_DeleteGoods0_HTTP_Handler(srv))
	r.GET("/v1/admin/goods", _Background_GetGoodsList0_HTTP_Handler(srv))
	r.PUT("/v1/admin/goods/{id}/status", _Background_SetGoodsStatus0_HTTP_Handler(srv))
	r.GET("/v1/admin/task/types", _Background_GetTaskTypes0_HTTP_Handler(srv))
	r.GET("/v1/admin/task/list", _Background_ListTask0_HTTP_Handler(srv))
	r.GET("/v1/admin/task/progress/list", _Background_ListTaskProgress0_HTTP_Handler(srv))
	r.POST("/v1/admin/task/status/update", _Background_UpdateTaskStatus0_HTTP_Handler(srv))
	r.POST("/v1/admin/task/config/update", _Background_UpdateTaskConfig0_HTTP_Handler(srv))
	r.POST("/v1/admin/task/config/delete", _Background_DeleteTask0_HTTP_Handler(srv))
	r.POST("/v1/admin/task/create", _Background_CreateTask0_HTTP_Handler(srv))
	r.GET("/v1/admin/task/detail", _Background_GetTaskDetail0_HTTP_Handler(srv))
	r.GET("/v1/admin/task/goods/list", _Background_ListTaskGoods0_HTTP_Handler(srv))
	r.GET("/v1/admin/sign-configs/list", _Background_ListSignConfig0_HTTP_Handler(srv))
	r.POST("/v1/admin/sign/config/create", _Background_CreateSignConfig0_HTTP_Handler(srv))
	r.POST("/v1/admin/sign/config/update", _Background_UpdateSignConfig0_HTTP_Handler(srv))
	r.POST("/v1/admin/sign/config/delete", _Background_DeleteSignConfig0_HTTP_Handler(srv))
	r.GET("/v1/admin/gift-card/list", _Background_GiftCardSettingPageList0_HTTP_Handler(srv))
	r.POST("/v1/admin/gift-card/edit", _Background_EditGiftCardSetting0_HTTP_Handler(srv))
	r.POST("/v1/admin/gift-card/delete", _Background_DeleteGiftCardSetting0_HTTP_Handler(srv))
	r.GET("/v1/admin/gift-card/single", _Background_GetSingleGiftCardSetting0_HTTP_Handler(srv))
	r.GET("/v1/admin/gift-card/is-send", _Background_GetSingleGiftCardSettingIsSend0_HTTP_Handler(srv))
	r.POST("/v1/admin/gift-card/send", _Background_SendGiftCard0_HTTP_Handler(srv))
	r.GET("/v1/admin/gift-card/send-record", _Background_SendGiftCardRecord0_HTTP_Handler(srv))
	r.GET("/v1/admin/order/list", _Background_AdminOrderList0_HTTP_Handler(srv))
	r.GET("/v1/admin/order/detail", _Background_AdminOrderDetail0_HTTP_Handler(srv))
	r.POST("/v1/admin/order/deliver", _Background_OrderDeliver0_HTTP_Handler(srv))
	r.GET("/v1/admin/order/report/fetch", _Background_FetchReportOrders0_HTTP_Handler(srv))
}

func _Background_LabelList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundLabelList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LabelList(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LabelListReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetStaticSpec0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStaticSpecRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetStaticSpec)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStaticSpec(ctx, req.(*GetStaticSpecRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStaticSpecReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddGoods0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddGoods)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddGoods(ctx, req.(*GoodsInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddGoodsReply)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateGoods0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateGoods)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateGoods(ctx, req.(*GoodsInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetGoodsInfo0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGoodsInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetGoodsInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsInfo(ctx, req.(*GetGoodsInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteGoods0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteGoodsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteGoods)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteGoods(ctx, req.(*DeleteGoodsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetGoodsList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGoodsListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetGoodsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsList(ctx, req.(*GetGoodsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGoodsListReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetGoodsStatus0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetGoodsStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetGoodsStatus(ctx, req.(*GoodsStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetTaskTypes0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTaskTypesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetTaskTypes)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTaskTypes(ctx, req.(*GetTaskTypesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTaskTypesResponse)
		return ctx.Result(200, reply)
	}
}

func _Background_ListTask0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminListTaskRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTask(ctx, req.(*AdminListTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminListTaskReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListTaskProgress0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminListTaskProgressRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListTaskProgress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTaskProgress(ctx, req.(*AdminListTaskProgressRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminListTaskProgressReply)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateTaskStatus0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminUpdateTaskStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateTaskStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateTaskStatus(ctx, req.(*AdminUpdateTaskStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateTaskConfig0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminUpdateTaskConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateTaskConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateTaskConfig(ctx, req.(*AdminUpdateTaskConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteTask0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminDeleteTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteTask(ctx, req.(*AdminDeleteTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_CreateTask0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminCreateTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundCreateTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTask(ctx, req.(*AdminCreateTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminCreateTaskReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetTaskDetail0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminGetTaskDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetTaskDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTaskDetail(ctx, req.(*AdminGetTaskDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminGetTaskDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListTaskGoods0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListTaskGoods)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTaskGoods(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminListTaskGoodsReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListSignConfig0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSignConfigRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSignConfig(ctx, req.(*ListSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _Background_CreateSignConfig0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateSignConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundCreateSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSignConfig(ctx, req.(*CreateSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateSignConfig0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateSignConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSignConfig(ctx, req.(*UpdateSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteSignConfig0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSignConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSignConfig(ctx, req.(*DeleteSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GiftCardSettingPageList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GiftCardSettingPageListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGiftCardSettingPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GiftCardSettingPageList(ctx, req.(*GiftCardSettingPageListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GiftCardSettingPageListReply)
		return ctx.Result(200, reply)
	}
}

func _Background_EditGiftCardSetting0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EditGiftCardSettingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundEditGiftCardSetting)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EditGiftCardSetting(ctx, req.(*EditGiftCardSettingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EditGiftCardSettingReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteGiftCardSetting0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteGiftCardSettingRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteGiftCardSetting)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteGiftCardSetting(ctx, req.(*DeleteGiftCardSettingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteGiftCardSettingReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetSingleGiftCardSetting0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSingleGiftCardSettingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetSingleGiftCardSetting)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSingleGiftCardSetting(ctx, req.(*GetSingleGiftCardSettingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSingleGiftCardSettingReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetSingleGiftCardSettingIsSend0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSingleGiftCardSettingIsSendRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetSingleGiftCardSettingIsSend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSingleGiftCardSettingIsSend(ctx, req.(*GetSingleGiftCardSettingIsSendRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSingleGiftCardSettingIsSendReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SendGiftCard0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendGiftCardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSendGiftCard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendGiftCard(ctx, req.(*SendGiftCardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendGiftCardReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SendGiftCardRecord0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendGiftCardRecordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSendGiftCardRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendGiftCardRecord(ctx, req.(*SendGiftCardRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendGiftCardRecordReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AdminOrderList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminOrderListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAdminOrderList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AdminOrderList(ctx, req.(*AdminOrderListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminOrderListReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AdminOrderDetail0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdminOrderDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAdminOrderDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AdminOrderDetail(ctx, req.(*AdminOrderDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdminOrderDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Background_OrderDeliver0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderDeliverRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundOrderDeliver)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrderDeliver(ctx, req.(*OrderDeliverRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_FetchReportOrders0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundFetchReportOrders)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FetchReportOrders(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

type BackgroundHTTPClient interface {
	AddGoods(ctx context.Context, req *GoodsInfo, opts ...http.CallOption) (rsp *AddGoodsReply, err error)
	AdminOrderDetail(ctx context.Context, req *AdminOrderDetailRequest, opts ...http.CallOption) (rsp *AdminOrderDetailReply, err error)
	AdminOrderList(ctx context.Context, req *AdminOrderListRequest, opts ...http.CallOption) (rsp *AdminOrderListReply, err error)
	CreateSignConfig(ctx context.Context, req *CreateSignConfigRequest, opts ...http.CallOption) (rsp *CreateSignConfigReply, err error)
	CreateTask(ctx context.Context, req *AdminCreateTaskRequest, opts ...http.CallOption) (rsp *AdminCreateTaskReply, err error)
	DeleteGiftCardSetting(ctx context.Context, req *DeleteGiftCardSettingRequest, opts ...http.CallOption) (rsp *DeleteGiftCardSettingReply, err error)
	DeleteGoods(ctx context.Context, req *DeleteGoodsRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteSignConfig(ctx context.Context, req *DeleteSignConfigRequest, opts ...http.CallOption) (rsp *DeleteSignConfigReply, err error)
	DeleteTask(ctx context.Context, req *AdminDeleteTaskRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	EditGiftCardSetting(ctx context.Context, req *EditGiftCardSettingRequest, opts ...http.CallOption) (rsp *EditGiftCardSettingReply, err error)
	FetchReportOrders(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	GetGoodsInfo(ctx context.Context, req *GetGoodsInfoRequest, opts ...http.CallOption) (rsp *GoodsInfo, err error)
	GetGoodsList(ctx context.Context, req *GetGoodsListRequest, opts ...http.CallOption) (rsp *GetGoodsListReply, err error)
	GetSingleGiftCardSetting(ctx context.Context, req *GetSingleGiftCardSettingRequest, opts ...http.CallOption) (rsp *GetSingleGiftCardSettingReply, err error)
	GetSingleGiftCardSettingIsSend(ctx context.Context, req *GetSingleGiftCardSettingIsSendRequest, opts ...http.CallOption) (rsp *GetSingleGiftCardSettingIsSendReply, err error)
	GetStaticSpec(ctx context.Context, req *GetStaticSpecRequest, opts ...http.CallOption) (rsp *GetStaticSpecReply, err error)
	GetTaskDetail(ctx context.Context, req *AdminGetTaskDetailRequest, opts ...http.CallOption) (rsp *AdminGetTaskDetailReply, err error)
	GetTaskTypes(ctx context.Context, req *GetTaskTypesRequest, opts ...http.CallOption) (rsp *GetTaskTypesResponse, err error)
	GiftCardSettingPageList(ctx context.Context, req *GiftCardSettingPageListRequest, opts ...http.CallOption) (rsp *GiftCardSettingPageListReply, err error)
	LabelList(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *LabelListReply, err error)
	ListSignConfig(ctx context.Context, req *ListSignConfigRequest, opts ...http.CallOption) (rsp *ListSignConfigReply, err error)
	ListTask(ctx context.Context, req *AdminListTaskRequest, opts ...http.CallOption) (rsp *AdminListTaskReply, err error)
	ListTaskGoods(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *AdminListTaskGoodsReply, err error)
	ListTaskProgress(ctx context.Context, req *AdminListTaskProgressRequest, opts ...http.CallOption) (rsp *AdminListTaskProgressReply, err error)
	OrderDeliver(ctx context.Context, req *OrderDeliverRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SendGiftCard(ctx context.Context, req *SendGiftCardRequest, opts ...http.CallOption) (rsp *SendGiftCardReply, err error)
	SendGiftCardRecord(ctx context.Context, req *SendGiftCardRecordRequest, opts ...http.CallOption) (rsp *SendGiftCardRecordReply, err error)
	SetGoodsStatus(ctx context.Context, req *GoodsStatusRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateGoods(ctx context.Context, req *GoodsInfo, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateSignConfig(ctx context.Context, req *UpdateSignConfigRequest, opts ...http.CallOption) (rsp *UpdateSignConfigReply, err error)
	UpdateTaskConfig(ctx context.Context, req *AdminUpdateTaskConfigRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateTaskStatus(ctx context.Context, req *AdminUpdateTaskStatusRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
}

type BackgroundHTTPClientImpl struct {
	cc *http.Client
}

func NewBackgroundHTTPClient(client *http.Client) BackgroundHTTPClient {
	return &BackgroundHTTPClientImpl{client}
}

func (c *BackgroundHTTPClientImpl) AddGoods(ctx context.Context, in *GoodsInfo, opts ...http.CallOption) (*AddGoodsReply, error) {
	var out AddGoodsReply
	pattern := "/v1/admin/goods"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddGoods))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AdminOrderDetail(ctx context.Context, in *AdminOrderDetailRequest, opts ...http.CallOption) (*AdminOrderDetailReply, error) {
	var out AdminOrderDetailReply
	pattern := "/v1/admin/order/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundAdminOrderDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AdminOrderList(ctx context.Context, in *AdminOrderListRequest, opts ...http.CallOption) (*AdminOrderListReply, error) {
	var out AdminOrderListReply
	pattern := "/v1/admin/order/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundAdminOrderList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) CreateSignConfig(ctx context.Context, in *CreateSignConfigRequest, opts ...http.CallOption) (*CreateSignConfigReply, error) {
	var out CreateSignConfigReply
	pattern := "/v1/admin/sign/config/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundCreateSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) CreateTask(ctx context.Context, in *AdminCreateTaskRequest, opts ...http.CallOption) (*AdminCreateTaskReply, error) {
	var out AdminCreateTaskReply
	pattern := "/v1/admin/task/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundCreateTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteGiftCardSetting(ctx context.Context, in *DeleteGiftCardSettingRequest, opts ...http.CallOption) (*DeleteGiftCardSettingReply, error) {
	var out DeleteGiftCardSettingReply
	pattern := "/v1/admin/gift-card/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundDeleteGiftCardSetting))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteGoods(ctx context.Context, in *DeleteGoodsRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/goods/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteGoods))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteSignConfig(ctx context.Context, in *DeleteSignConfigRequest, opts ...http.CallOption) (*DeleteSignConfigReply, error) {
	var out DeleteSignConfigReply
	pattern := "/v1/admin/sign/config/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundDeleteSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteTask(ctx context.Context, in *AdminDeleteTaskRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/task/config/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundDeleteTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) EditGiftCardSetting(ctx context.Context, in *EditGiftCardSettingRequest, opts ...http.CallOption) (*EditGiftCardSettingReply, error) {
	var out EditGiftCardSettingReply
	pattern := "/v1/admin/gift-card/edit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundEditGiftCardSetting))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) FetchReportOrders(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/order/report/fetch"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundFetchReportOrders))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetGoodsInfo(ctx context.Context, in *GetGoodsInfoRequest, opts ...http.CallOption) (*GoodsInfo, error) {
	var out GoodsInfo
	pattern := "/v1/admin/goods/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetGoodsInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetGoodsList(ctx context.Context, in *GetGoodsListRequest, opts ...http.CallOption) (*GetGoodsListReply, error) {
	var out GetGoodsListReply
	pattern := "/v1/admin/goods"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetGoodsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetSingleGiftCardSetting(ctx context.Context, in *GetSingleGiftCardSettingRequest, opts ...http.CallOption) (*GetSingleGiftCardSettingReply, error) {
	var out GetSingleGiftCardSettingReply
	pattern := "/v1/admin/gift-card/single"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetSingleGiftCardSetting))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetSingleGiftCardSettingIsSend(ctx context.Context, in *GetSingleGiftCardSettingIsSendRequest, opts ...http.CallOption) (*GetSingleGiftCardSettingIsSendReply, error) {
	var out GetSingleGiftCardSettingIsSendReply
	pattern := "/v1/admin/gift-card/is-send"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetSingleGiftCardSettingIsSend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetStaticSpec(ctx context.Context, in *GetStaticSpecRequest, opts ...http.CallOption) (*GetStaticSpecReply, error) {
	var out GetStaticSpecReply
	pattern := "/v1/admin/goods/static/spec"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetStaticSpec))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetTaskDetail(ctx context.Context, in *AdminGetTaskDetailRequest, opts ...http.CallOption) (*AdminGetTaskDetailReply, error) {
	var out AdminGetTaskDetailReply
	pattern := "/v1/admin/task/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetTaskDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetTaskTypes(ctx context.Context, in *GetTaskTypesRequest, opts ...http.CallOption) (*GetTaskTypesResponse, error) {
	var out GetTaskTypesResponse
	pattern := "/v1/admin/task/types"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetTaskTypes))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GiftCardSettingPageList(ctx context.Context, in *GiftCardSettingPageListRequest, opts ...http.CallOption) (*GiftCardSettingPageListReply, error) {
	var out GiftCardSettingPageListReply
	pattern := "/v1/admin/gift-card/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGiftCardSettingPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) LabelList(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*LabelListReply, error) {
	var out LabelListReply
	pattern := "/v1/admin/label"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundLabelList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListSignConfig(ctx context.Context, in *ListSignConfigRequest, opts ...http.CallOption) (*ListSignConfigReply, error) {
	var out ListSignConfigReply
	pattern := "/v1/admin/sign-configs/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListTask(ctx context.Context, in *AdminListTaskRequest, opts ...http.CallOption) (*AdminListTaskReply, error) {
	var out AdminListTaskReply
	pattern := "/v1/admin/task/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListTaskGoods(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*AdminListTaskGoodsReply, error) {
	var out AdminListTaskGoodsReply
	pattern := "/v1/admin/task/goods/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListTaskGoods))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListTaskProgress(ctx context.Context, in *AdminListTaskProgressRequest, opts ...http.CallOption) (*AdminListTaskProgressReply, error) {
	var out AdminListTaskProgressReply
	pattern := "/v1/admin/task/progress/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListTaskProgress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) OrderDeliver(ctx context.Context, in *OrderDeliverRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/order/deliver"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundOrderDeliver))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SendGiftCard(ctx context.Context, in *SendGiftCardRequest, opts ...http.CallOption) (*SendGiftCardReply, error) {
	var out SendGiftCardReply
	pattern := "/v1/admin/gift-card/send"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSendGiftCard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SendGiftCardRecord(ctx context.Context, in *SendGiftCardRecordRequest, opts ...http.CallOption) (*SendGiftCardRecordReply, error) {
	var out SendGiftCardRecordReply
	pattern := "/v1/admin/gift-card/send-record"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundSendGiftCardRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetGoodsStatus(ctx context.Context, in *GoodsStatusRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/goods/{id}/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetGoodsStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateGoods(ctx context.Context, in *GoodsInfo, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/goods/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateGoods))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateSignConfig(ctx context.Context, in *UpdateSignConfigRequest, opts ...http.CallOption) (*UpdateSignConfigReply, error) {
	var out UpdateSignConfigReply
	pattern := "/v1/admin/sign/config/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateTaskConfig(ctx context.Context, in *AdminUpdateTaskConfigRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/task/config/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateTaskConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateTaskStatus(ctx context.Context, in *AdminUpdateTaskStatusRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/task/status/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateTaskStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
