// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: user_profile/v1/models.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReportAction int32

const (
	ReportAction_Init           ReportAction = 0 // 新生成push_id
	ReportAction_Login          ReportAction = 1 // 登录
	ReportAction_LanguageChange ReportAction = 2 // 语言变更
	ReportAction_CountryChange  ReportAction = 3 // 国家变更
)

// Enum value maps for ReportAction.
var (
	ReportAction_name = map[int32]string{
		0: "Init",
		1: "Login",
		2: "LanguageChange",
		3: "CountryChange",
	}
	ReportAction_value = map[string]int32{
		"Init":           0,
		"Login":          1,
		"LanguageChange": 2,
		"CountryChange":  3,
	}
)

func (x ReportAction) Enum() *ReportAction {
	p := new(ReportAction)
	*p = x
	return p
}

func (x ReportAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportAction) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[0].Descriptor()
}

func (ReportAction) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[0]
}

func (x ReportAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportAction.Descriptor instead.
func (ReportAction) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{0}
}

type UserProfileFieldCategory int32

const (
	UserProfileFieldCategory_UserProfileFieldCategoryUser   UserProfileFieldCategory = 0 // 用户
	UserProfileFieldCategory_UserProfileFieldCategoryDevice UserProfileFieldCategory = 1 // 设备
)

// Enum value maps for UserProfileFieldCategory.
var (
	UserProfileFieldCategory_name = map[int32]string{
		0: "UserProfileFieldCategoryUser",
		1: "UserProfileFieldCategoryDevice",
	}
	UserProfileFieldCategory_value = map[string]int32{
		"UserProfileFieldCategoryUser":   0,
		"UserProfileFieldCategoryDevice": 1,
	}
)

func (x UserProfileFieldCategory) Enum() *UserProfileFieldCategory {
	p := new(UserProfileFieldCategory)
	*p = x
	return p
}

func (x UserProfileFieldCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserProfileFieldCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[1].Descriptor()
}

func (UserProfileFieldCategory) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[1]
}

func (x UserProfileFieldCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserProfileFieldCategory.Descriptor instead.
func (UserProfileFieldCategory) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{1}
}

type LabelStatus int32

const (
	LabelStatus_LabelsStatusDisabled LabelStatus = 0
	LabelStatus_LabelsStatusEnable   LabelStatus = 1
)

// Enum value maps for LabelStatus.
var (
	LabelStatus_name = map[int32]string{
		0: "LabelsStatusDisabled",
		1: "LabelsStatusEnable",
	}
	LabelStatus_value = map[string]int32{
		"LabelsStatusDisabled": 0,
		"LabelsStatusEnable":   1,
	}
)

func (x LabelStatus) Enum() *LabelStatus {
	p := new(LabelStatus)
	*p = x
	return p
}

func (x LabelStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[2].Descriptor()
}

func (LabelStatus) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[2]
}

func (x LabelStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LabelStatus.Descriptor instead.
func (LabelStatus) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{2}
}

type RecallWay int32

const (
	RecallWay_RecallWayApp   RecallWay = 0 //Whatsapp召回
	RecallWay_RecallWaySMS   RecallWay = 1 //短信召回
	RecallWay_RecallWayEmail RecallWay = 2 //邮件召回
)

// Enum value maps for RecallWay.
var (
	RecallWay_name = map[int32]string{
		0: "RecallWayApp",
		1: "RecallWaySMS",
		2: "RecallWayEmail",
	}
	RecallWay_value = map[string]int32{
		"RecallWayApp":   0,
		"RecallWaySMS":   1,
		"RecallWayEmail": 2,
	}
)

func (x RecallWay) Enum() *RecallWay {
	p := new(RecallWay)
	*p = x
	return p
}

func (x RecallWay) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecallWay) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[3].Descriptor()
}

func (RecallWay) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[3]
}

func (x RecallWay) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecallWay.Descriptor instead.
func (RecallWay) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{3}
}

type RecallTimeType int32

const (
	RecallTimeType_RecallTimeTypeAtOnce RecallTimeType = 0 //立即
	RecallTimeType_RecallTimeTypeOnce   RecallTimeType = 1 //定时
	RecallTimeType_RecallTimeTypeLoop   RecallTimeType = 2 //定时循环
)

// Enum value maps for RecallTimeType.
var (
	RecallTimeType_name = map[int32]string{
		0: "RecallTimeTypeAtOnce",
		1: "RecallTimeTypeOnce",
		2: "RecallTimeTypeLoop",
	}
	RecallTimeType_value = map[string]int32{
		"RecallTimeTypeAtOnce": 0,
		"RecallTimeTypeOnce":   1,
		"RecallTimeTypeLoop":   2,
	}
)

func (x RecallTimeType) Enum() *RecallTimeType {
	p := new(RecallTimeType)
	*p = x
	return p
}

func (x RecallTimeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecallTimeType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[4].Descriptor()
}

func (RecallTimeType) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[4]
}

func (x RecallTimeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecallTimeType.Descriptor instead.
func (RecallTimeType) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{4}
}

type JumpType int32

const (
	JumpType_JumpTypeUnknown       JumpType = 0
	JumpType_JumpTypeNews          JumpType = 1 //新闻
	JumpType_JumpTypeLive          JumpType = 3 //直播
	JumpType_JumpTypeTraderDetail  JumpType = 4 //交易商详情页
	JumpType_JumpTypeExposure      JumpType = 5 //曝光
	JumpType_JumpTypeSurvey        JumpType = 6 //实勘
	JumpType_JumpTypeDisCover      JumpType = 8 //发现
	JumpType_JumpTypeServiceDetail JumpType = 9 //服务商详情页
)

// Enum value maps for JumpType.
var (
	JumpType_name = map[int32]string{
		0: "JumpTypeUnknown",
		1: "JumpTypeNews",
		3: "JumpTypeLive",
		4: "JumpTypeTraderDetail",
		5: "JumpTypeExposure",
		6: "JumpTypeSurvey",
		8: "JumpTypeDisCover",
		9: "JumpTypeServiceDetail",
	}
	JumpType_value = map[string]int32{
		"JumpTypeUnknown":       0,
		"JumpTypeNews":          1,
		"JumpTypeLive":          3,
		"JumpTypeTraderDetail":  4,
		"JumpTypeExposure":      5,
		"JumpTypeSurvey":        6,
		"JumpTypeDisCover":      8,
		"JumpTypeServiceDetail": 9,
	}
)

func (x JumpType) Enum() *JumpType {
	p := new(JumpType)
	*p = x
	return p
}

func (x JumpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JumpType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[5].Descriptor()
}

func (JumpType) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[5]
}

func (x JumpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JumpType.Descriptor instead.
func (JumpType) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{5}
}

type TimeRange int32

const (
	TimeRange_TimeRangeUnknown        TimeRange = 0 //未知
	TimeRange_TimeRangeEveryDay       TimeRange = 1 //每天
	TimeRange_TimeRangeEveryThreeDays TimeRange = 2 //每三天
	TimeRange_TimeRangeEveryMonday    TimeRange = 3 //每周一
	TimeRange_TimeRangeEveryTuesday   TimeRange = 4 //每周二
	TimeRange_TimeRangeEveryWednesday TimeRange = 5 //每周三
	TimeRange_TimeRangeEveryThursday  TimeRange = 6 //每周四
	TimeRange_TimeRangeEveryFriday    TimeRange = 7 //每周五
)

// Enum value maps for TimeRange.
var (
	TimeRange_name = map[int32]string{
		0: "TimeRangeUnknown",
		1: "TimeRangeEveryDay",
		2: "TimeRangeEveryThreeDays",
		3: "TimeRangeEveryMonday",
		4: "TimeRangeEveryTuesday",
		5: "TimeRangeEveryWednesday",
		6: "TimeRangeEveryThursday",
		7: "TimeRangeEveryFriday",
	}
	TimeRange_value = map[string]int32{
		"TimeRangeUnknown":        0,
		"TimeRangeEveryDay":       1,
		"TimeRangeEveryThreeDays": 2,
		"TimeRangeEveryMonday":    3,
		"TimeRangeEveryTuesday":   4,
		"TimeRangeEveryWednesday": 5,
		"TimeRangeEveryThursday":  6,
		"TimeRangeEveryFriday":    7,
	}
)

func (x TimeRange) Enum() *TimeRange {
	p := new(TimeRange)
	*p = x
	return p
}

func (x TimeRange) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeRange) Descriptor() protoreflect.EnumDescriptor {
	return file_user_profile_v1_models_proto_enumTypes[6].Descriptor()
}

func (TimeRange) Type() protoreflect.EnumType {
	return &file_user_profile_v1_models_proto_enumTypes[6]
}

func (x TimeRange) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeRange.Descriptor instead.
func (TimeRange) EnumDescriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{6}
}

type PushIdReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PushId string       `protobuf:"bytes,1,opt,name=push_id,json=pushId,proto3" json:"push_id"`
	Action ReportAction `protobuf:"varint,2,opt,name=action,json=action,proto3,enum=api.user_profile.v1.ReportAction" json:"action"`
}

func (x *PushIdReportRequest) Reset() {
	*x = PushIdReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushIdReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushIdReportRequest) ProtoMessage() {}

func (x *PushIdReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushIdReportRequest.ProtoReflect.Descriptor instead.
func (*PushIdReportRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{0}
}

func (x *PushIdReportRequest) GetPushId() string {
	if x != nil {
		return x.PushId
	}
	return ""
}

func (x *PushIdReportRequest) GetAction() ReportAction {
	if x != nil {
		return x.Action
	}
	return ReportAction_Init
}

type PushIdReportReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PushIdReportReply) Reset() {
	*x = PushIdReportReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushIdReportReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushIdReportReply) ProtoMessage() {}

func (x *PushIdReportReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushIdReportReply.ProtoReflect.Descriptor instead.
func (*PushIdReportReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{1}
}

type CustomFilterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Activity     string   `protobuf:"bytes,1,opt,name=activity,json=activity,proto3" json:"activity"`
	Size         int32    `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Offset       string   `protobuf:"bytes,3,opt,name=offset,json=offset,proto3" json:"offset"`
	Labels       []string `protobuf:"bytes,4,rep,name=labels,json=labels,proto3" json:"labels"`
	LanguageCode string   `protobuf:"bytes,5,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	CountryCode  string   `protobuf:"bytes,6,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
}

func (x *CustomFilterRequest) Reset() {
	*x = CustomFilterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomFilterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomFilterRequest) ProtoMessage() {}

func (x *CustomFilterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomFilterRequest.ProtoReflect.Descriptor instead.
func (*CustomFilterRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{2}
}

func (x *CustomFilterRequest) GetActivity() string {
	if x != nil {
		return x.Activity
	}
	return ""
}

func (x *CustomFilterRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CustomFilterRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *CustomFilterRequest) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CustomFilterRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *CustomFilterRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

type CustomFilterItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId    string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	PushId      string `protobuf:"bytes,3,opt,name=push_id,json=pushId,proto3" json:"push_id"`
	CountryCode string `protobuf:"bytes,4,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
}

func (x *CustomFilterItem) Reset() {
	*x = CustomFilterItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomFilterItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomFilterItem) ProtoMessage() {}

func (x *CustomFilterItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomFilterItem.ProtoReflect.Descriptor instead.
func (*CustomFilterItem) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{3}
}

func (x *CustomFilterItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CustomFilterItem) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CustomFilterItem) GetPushId() string {
	if x != nil {
		return x.PushId
	}
	return ""
}

func (x *CustomFilterItem) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

type CustomFilterReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items  []*CustomFilterItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Offset string              `protobuf:"bytes,2,opt,name=offset,json=offset,proto3" json:"offset"`
}

func (x *CustomFilterReply) Reset() {
	*x = CustomFilterReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomFilterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomFilterReply) ProtoMessage() {}

func (x *CustomFilterReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomFilterReply.ProtoReflect.Descriptor instead.
func (*CustomFilterReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{4}
}

func (x *CustomFilterReply) GetItems() []*CustomFilterItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *CustomFilterReply) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

type CountByLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *CountByLabelRequest) Reset() {
	*x = CountByLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountByLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountByLabelRequest) ProtoMessage() {}

func (x *CountByLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountByLabelRequest.ProtoReflect.Descriptor instead.
func (*CountByLabelRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{5}
}

func (x *CountByLabelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,json=value,proto3" json:"value"`
	Count int32  `protobuf:"varint,2,opt,name=count,json=count,proto3" json:"count"`
}

func (x *Value) Reset() {
	*x = Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{6}
}

func (x *Value) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Value) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CountByLabelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count     int64    `protobuf:"varint,1,opt,name=count,json=count,proto3" json:"count"`
	Countries []*Value `protobuf:"bytes,2,rep,name=countries,json=countries,proto3" json:"countries"`
	Languages []*Value `protobuf:"bytes,3,rep,name=languages,json=languages,proto3" json:"languages"`
}

func (x *CountByLabelReply) Reset() {
	*x = CountByLabelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountByLabelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountByLabelReply) ProtoMessage() {}

func (x *CountByLabelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountByLabelReply.ProtoReflect.Descriptor instead.
func (*CountByLabelReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{7}
}

func (x *CountByLabelReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *CountByLabelReply) GetCountries() []*Value {
	if x != nil {
		return x.Countries
	}
	return nil
}

func (x *CountByLabelReply) GetLanguages() []*Value {
	if x != nil {
		return x.Languages
	}
	return nil
}

type FilterLabelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FilterLabelsRequest) Reset() {
	*x = FilterLabelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterLabelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterLabelsRequest) ProtoMessage() {}

func (x *FilterLabelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterLabelsRequest.ProtoReflect.Descriptor instead.
func (*FilterLabelsRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{8}
}

type FilterLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Description string `protobuf:"bytes,2,opt,name=description,json=description,proto3" json:"description"`
	Label       string `protobuf:"bytes,3,opt,name=label,json=label,proto3" json:"label"`
}

func (x *FilterLabel) Reset() {
	*x = FilterLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterLabel) ProtoMessage() {}

func (x *FilterLabel) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterLabel.ProtoReflect.Descriptor instead.
func (*FilterLabel) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{9}
}

func (x *FilterLabel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FilterLabel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FilterLabel) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

type FilterLabelsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels []*FilterLabel `protobuf:"bytes,1,rep,name=labels,json=labels,proto3" json:"labels"`
}

func (x *FilterLabelsReply) Reset() {
	*x = FilterLabelsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterLabelsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterLabelsReply) ProtoMessage() {}

func (x *FilterLabelsReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterLabelsReply.ProtoReflect.Descriptor instead.
func (*FilterLabelsReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{10}
}

func (x *FilterLabelsReply) GetLabels() []*FilterLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

type FieldValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Value string `protobuf:"bytes,2,opt,name=value,json=value,proto3" json:"value"`
}

func (x *FieldValue) Reset() {
	*x = FieldValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldValue) ProtoMessage() {}

func (x *FieldValue) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldValue.ProtoReflect.Descriptor instead.
func (*FieldValue) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{11}
}

func (x *FieldValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FieldValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FieldName   string        `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name"`
	FieldType   string        `protobuf:"bytes,2,opt,name=field_type,json=fieldType,proto3" json:"field_type"`
	Description string        `protobuf:"bytes,3,opt,name=description,json=description,proto3" json:"description"`
	ShowName    string        `protobuf:"bytes,4,opt,name=show_name,json=showName,proto3" json:"show_name"`
	Values      []*FieldValue `protobuf:"bytes,5,rep,name=values,json=values,proto3" json:"values"`
}

func (x *Field) Reset() {
	*x = Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Field) ProtoMessage() {}

func (x *Field) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Field.ProtoReflect.Descriptor instead.
func (*Field) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{12}
}

func (x *Field) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *Field) GetFieldType() string {
	if x != nil {
		return x.FieldType
	}
	return ""
}

func (x *Field) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Field) GetShowName() string {
	if x != nil {
		return x.ShowName
	}
	return ""
}

func (x *Field) GetValues() []*FieldValue {
	if x != nil {
		return x.Values
	}
	return nil
}

type UserProfileFieldRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category UserProfileFieldCategory `protobuf:"varint,1,opt,name=category,json=category,proto3,enum=api.user_profile.v1.UserProfileFieldCategory" json:"category"`
}

func (x *UserProfileFieldRequest) Reset() {
	*x = UserProfileFieldRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProfileFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProfileFieldRequest) ProtoMessage() {}

func (x *UserProfileFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProfileFieldRequest.ProtoReflect.Descriptor instead.
func (*UserProfileFieldRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{13}
}

func (x *UserProfileFieldRequest) GetCategory() UserProfileFieldCategory {
	if x != nil {
		return x.Category
	}
	return UserProfileFieldCategory_UserProfileFieldCategoryUser
}

type UserProfileFieldReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fields []*Field `protobuf:"bytes,1,rep,name=fields,json=fields,proto3" json:"fields"`
}

func (x *UserProfileFieldReply) Reset() {
	*x = UserProfileFieldReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProfileFieldReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProfileFieldReply) ProtoMessage() {}

func (x *UserProfileFieldReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProfileFieldReply.ProtoReflect.Descriptor instead.
func (*UserProfileFieldReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{14}
}

func (x *UserProfileFieldReply) GetFields() []*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

type GetByUserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
}

func (x *GetByUserIdRequest) Reset() {
	*x = GetByUserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetByUserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetByUserIdRequest) ProtoMessage() {}

func (x *GetByUserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetByUserIdRequest.ProtoReflect.Descriptor instead.
func (*GetByUserIdRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{15}
}

func (x *GetByUserIdRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UserProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId                  string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	Identify                int32    `protobuf:"varint,2,opt,name=identify,json=identify,proto3" json:"identify"`
	RegisterTime            int64    `protobuf:"varint,3,opt,name=register_time,json=registerTime,proto3" json:"register_time"`
	RegisterIp              string   `protobuf:"bytes,4,opt,name=register_ip,json=registerIp,proto3" json:"register_ip"`
	RegisterCountryCode     string   `protobuf:"bytes,5,opt,name=register_country_code,json=registerCountryCode,proto3" json:"register_country_code"`
	DeviceId                string   `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	ClientIp                string   `protobuf:"bytes,7,opt,name=client_ip,json=clientIp,proto3" json:"client_ip"`
	Platform                string   `protobuf:"bytes,8,opt,name=platform,json=platform,proto3" json:"platform"`
	LanguageCode            string   `protobuf:"bytes,9,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	PreferLanguageCodes     []string `protobuf:"bytes,10,rep,name=prefer_language_codes,json=preferLanguageCodes,proto3" json:"prefer_language_codes"`
	CountryCode             string   `protobuf:"bytes,11,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	FirstOpenAppTime        int64    `protobuf:"varint,12,opt,name=first_open_app_time,json=firstOpenAppTime,proto3" json:"first_open_app_time"`
	LastOpenAppTime         int64    `protobuf:"varint,13,opt,name=last_open_app_time,json=lastOpenAppTime,proto3" json:"last_open_app_time"`
	LastLoginTime           int64    `protobuf:"varint,14,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time"`
	LastActiveTime          int64    `protobuf:"varint,15,opt,name=last_active_time,json=lastActiveTime,proto3" json:"last_active_time"`
	FansCount               int64    `protobuf:"varint,16,opt,name=fans_count,json=fansCount,proto3" json:"fans_count"`
	FollowCount             int64    `protobuf:"varint,17,opt,name=follow_count,json=followCount,proto3" json:"follow_count"`
	PostsCount              int64    `protobuf:"varint,18,opt,name=posts_count,json=postsCount,proto3" json:"posts_count"`
	PostsLikeCount          int64    `protobuf:"varint,19,opt,name=posts_like_count,json=postsLikeCount,proto3" json:"posts_like_count"`
	PostsSharedCount        int64    `protobuf:"varint,20,opt,name=posts_shared_count,json=postsSharedCount,proto3" json:"posts_shared_count"`
	PostsReplyCount         int64    `protobuf:"varint,21,opt,name=posts_reply_count,json=postsReplyCount,proto3" json:"posts_reply_count"`
	PostsViewCount          int64    `protobuf:"varint,22,opt,name=posts_view_count,json=postsViewCount,proto3" json:"posts_view_count"`
	PostsCollectCount       int64    `protobuf:"varint,23,opt,name=posts_collect_count,json=postsCollectCount,proto3" json:"posts_collect_count"`
	ReplyCount              int64    `protobuf:"varint,24,opt,name=reply_count,json=replyCount,proto3" json:"reply_count"`
	LikeCount               int64    `protobuf:"varint,25,opt,name=like_count,json=likeCount,proto3" json:"like_count"`
	PayAmount               float32  `protobuf:"fixed32,26,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	PayCount                int64    `protobuf:"varint,27,opt,name=pay_count,json=payCount,proto3" json:"pay_count"`
	FeedbackCount           int64    `protobuf:"varint,28,opt,name=feedback_count,json=feedbackCount,proto3" json:"feedback_count"`
	ReplyTradeCount         int64    `protobuf:"varint,29,opt,name=reply_trade_count,json=replyTradeCount,proto3" json:"reply_trade_count"`
	OpenAppCount_7Day       int64    `protobuf:"varint,30,opt,name=open_app_count_7day,json=openAppCount7day,proto3" json:"open_app_count_7day"`
	PostsCount_7Day         int64    `protobuf:"varint,31,opt,name=posts_count_7day,json=postsCount7day,proto3" json:"posts_count_7day"`
	ActionCount_7Day        int64    `protobuf:"varint,32,opt,name=action_count_7day,json=actionCount7day,proto3" json:"action_count_7day"`
	SearchTradeCount_7Day   int64    `protobuf:"varint,33,opt,name=search_trade_count_7day,json=searchTradeCount7day,proto3" json:"search_trade_count_7day"`
	ClickTradeCount_7Day    int64    `protobuf:"varint,34,opt,name=click_trade_count_7day,json=clickTradeCount7day,proto3" json:"click_trade_count_7day"`
	ClickExposureCount_7Day int64    `protobuf:"varint,35,opt,name=click_exposure_count_7day,json=clickExposureCount7day,proto3" json:"click_exposure_count_7day"`
	ClickAdCount_7Day       int64    `protobuf:"varint,36,opt,name=click_ad_count_7day,json=clickAdCount7day,proto3" json:"click_ad_count_7day"`
	MockTradeCount_7Day     int64    `protobuf:"varint,37,opt,name=mock_trade_count_7day,json=mockTradeCount7day,proto3" json:"mock_trade_count_7day"`
	UsedDeviceIds           []string `protobuf:"bytes,38,rep,name=used_device_ids,json=usedDeviceIds,proto3" json:"used_device_ids"`
	UsedLanguageCodes       []string `protobuf:"bytes,39,rep,name=used_language_codes,json=usedLanguageCodes,proto3" json:"used_language_codes"`
	UsedPreferLanguageCodes []string `protobuf:"bytes,40,rep,name=used_prefer_language_codes,json=usedPreferLanguageCodes,proto3" json:"used_prefer_language_codes"`
	UsedCountryCodes        []string `protobuf:"bytes,41,rep,name=used_country_codes,json=usedCountryCodes,proto3" json:"used_country_codes"`
	UsedIps                 []string `protobuf:"bytes,42,rep,name=used_ips,json=usedIps,proto3" json:"used_ips"`
	PushId                  string   `protobuf:"bytes,43,opt,name=push_id,json=pushId,proto3" json:"push_id"`
	WikiId                  string   `protobuf:"bytes,44,opt,name=wiki_id,json=wikiId,proto3" json:"wiki_id"`
}

func (x *UserProfile) Reset() {
	*x = UserProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProfile) ProtoMessage() {}

func (x *UserProfile) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProfile.ProtoReflect.Descriptor instead.
func (*UserProfile) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{16}
}

func (x *UserProfile) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserProfile) GetIdentify() int32 {
	if x != nil {
		return x.Identify
	}
	return 0
}

func (x *UserProfile) GetRegisterTime() int64 {
	if x != nil {
		return x.RegisterTime
	}
	return 0
}

func (x *UserProfile) GetRegisterIp() string {
	if x != nil {
		return x.RegisterIp
	}
	return ""
}

func (x *UserProfile) GetRegisterCountryCode() string {
	if x != nil {
		return x.RegisterCountryCode
	}
	return ""
}

func (x *UserProfile) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserProfile) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *UserProfile) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *UserProfile) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *UserProfile) GetPreferLanguageCodes() []string {
	if x != nil {
		return x.PreferLanguageCodes
	}
	return nil
}

func (x *UserProfile) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserProfile) GetFirstOpenAppTime() int64 {
	if x != nil {
		return x.FirstOpenAppTime
	}
	return 0
}

func (x *UserProfile) GetLastOpenAppTime() int64 {
	if x != nil {
		return x.LastOpenAppTime
	}
	return 0
}

func (x *UserProfile) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

func (x *UserProfile) GetLastActiveTime() int64 {
	if x != nil {
		return x.LastActiveTime
	}
	return 0
}

func (x *UserProfile) GetFansCount() int64 {
	if x != nil {
		return x.FansCount
	}
	return 0
}

func (x *UserProfile) GetFollowCount() int64 {
	if x != nil {
		return x.FollowCount
	}
	return 0
}

func (x *UserProfile) GetPostsCount() int64 {
	if x != nil {
		return x.PostsCount
	}
	return 0
}

func (x *UserProfile) GetPostsLikeCount() int64 {
	if x != nil {
		return x.PostsLikeCount
	}
	return 0
}

func (x *UserProfile) GetPostsSharedCount() int64 {
	if x != nil {
		return x.PostsSharedCount
	}
	return 0
}

func (x *UserProfile) GetPostsReplyCount() int64 {
	if x != nil {
		return x.PostsReplyCount
	}
	return 0
}

func (x *UserProfile) GetPostsViewCount() int64 {
	if x != nil {
		return x.PostsViewCount
	}
	return 0
}

func (x *UserProfile) GetPostsCollectCount() int64 {
	if x != nil {
		return x.PostsCollectCount
	}
	return 0
}

func (x *UserProfile) GetReplyCount() int64 {
	if x != nil {
		return x.ReplyCount
	}
	return 0
}

func (x *UserProfile) GetLikeCount() int64 {
	if x != nil {
		return x.LikeCount
	}
	return 0
}

func (x *UserProfile) GetPayAmount() float32 {
	if x != nil {
		return x.PayAmount
	}
	return 0
}

func (x *UserProfile) GetPayCount() int64 {
	if x != nil {
		return x.PayCount
	}
	return 0
}

func (x *UserProfile) GetFeedbackCount() int64 {
	if x != nil {
		return x.FeedbackCount
	}
	return 0
}

func (x *UserProfile) GetReplyTradeCount() int64 {
	if x != nil {
		return x.ReplyTradeCount
	}
	return 0
}

func (x *UserProfile) GetOpenAppCount_7Day() int64 {
	if x != nil {
		return x.OpenAppCount_7Day
	}
	return 0
}

func (x *UserProfile) GetPostsCount_7Day() int64 {
	if x != nil {
		return x.PostsCount_7Day
	}
	return 0
}

func (x *UserProfile) GetActionCount_7Day() int64 {
	if x != nil {
		return x.ActionCount_7Day
	}
	return 0
}

func (x *UserProfile) GetSearchTradeCount_7Day() int64 {
	if x != nil {
		return x.SearchTradeCount_7Day
	}
	return 0
}

func (x *UserProfile) GetClickTradeCount_7Day() int64 {
	if x != nil {
		return x.ClickTradeCount_7Day
	}
	return 0
}

func (x *UserProfile) GetClickExposureCount_7Day() int64 {
	if x != nil {
		return x.ClickExposureCount_7Day
	}
	return 0
}

func (x *UserProfile) GetClickAdCount_7Day() int64 {
	if x != nil {
		return x.ClickAdCount_7Day
	}
	return 0
}

func (x *UserProfile) GetMockTradeCount_7Day() int64 {
	if x != nil {
		return x.MockTradeCount_7Day
	}
	return 0
}

func (x *UserProfile) GetUsedDeviceIds() []string {
	if x != nil {
		return x.UsedDeviceIds
	}
	return nil
}

func (x *UserProfile) GetUsedLanguageCodes() []string {
	if x != nil {
		return x.UsedLanguageCodes
	}
	return nil
}

func (x *UserProfile) GetUsedPreferLanguageCodes() []string {
	if x != nil {
		return x.UsedPreferLanguageCodes
	}
	return nil
}

func (x *UserProfile) GetUsedCountryCodes() []string {
	if x != nil {
		return x.UsedCountryCodes
	}
	return nil
}

func (x *UserProfile) GetUsedIps() []string {
	if x != nil {
		return x.UsedIps
	}
	return nil
}

func (x *UserProfile) GetPushId() string {
	if x != nil {
		return x.PushId
	}
	return ""
}

func (x *UserProfile) GetWikiId() string {
	if x != nil {
		return x.WikiId
	}
	return ""
}

type GetUserprofileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
}

func (x *GetUserprofileRequest) Reset() {
	*x = GetUserprofileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserprofileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserprofileRequest) ProtoMessage() {}

func (x *GetUserprofileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserprofileRequest.ProtoReflect.Descriptor instead.
func (*GetUserprofileRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserprofileRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserprofileRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string      `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Status      LabelStatus `protobuf:"varint,2,opt,name=status,json=status,proto3,enum=api.user_profile.v1.LabelStatus" json:"status"`
	Name        string      `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Desc        string      `protobuf:"bytes,4,opt,name=desc,json=desc,proto3" json:"desc"`
	Filter      string      `protobuf:"bytes,5,opt,name=filter,json=filter,proto3" json:"filter"`
	CreatedAt   int64       `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	UpdatedAt   int64       `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	Editor      string      `protobuf:"bytes,8,opt,name=editor,json=editor,proto3" json:"editor"`
	PersonCount string      `protobuf:"bytes,9,opt,name=person_count,json=personCount,proto3" json:"person_count"`
	TaskCount   string      `protobuf:"bytes,10,opt,name=task_count,json=taskCount,proto3" json:"task_count"`
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{18}
}

func (x *Label) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Label) GetStatus() LabelStatus {
	if x != nil {
		return x.Status
	}
	return LabelStatus_LabelsStatusDisabled
}

func (x *Label) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Label) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Label) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *Label) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Label) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Label) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

func (x *Label) GetPersonCount() string {
	if x != nil {
		return x.PersonCount
	}
	return ""
}

func (x *Label) GetTaskCount() string {
	if x != nil {
		return x.TaskCount
	}
	return ""
}

type AddLabelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddLabelReply) Reset() {
	*x = AddLabelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelReply) ProtoMessage() {}

func (x *AddLabelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelReply.ProtoReflect.Descriptor instead.
func (*AddLabelReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{19}
}

func (x *AddLabelReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetLabelRequest) Reset() {
	*x = GetLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLabelRequest) ProtoMessage() {}

func (x *GetLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLabelRequest.ProtoReflect.Descriptor instead.
func (*GetLabelRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{20}
}

func (x *GetLabelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateLabelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *UpdateLabelReply) Reset() {
	*x = UpdateLabelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLabelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLabelReply) ProtoMessage() {}

func (x *UpdateLabelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLabelReply.ProtoReflect.Descriptor instead.
func (*UpdateLabelReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateLabelReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateLabelStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string      `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Status LabelStatus `protobuf:"varint,2,opt,name=status,json=status,proto3,enum=api.user_profile.v1.LabelStatus" json:"status"`
	Editor string      `protobuf:"bytes,3,opt,name=editor,json=editor,proto3" json:"editor"`
}

func (x *UpdateLabelStatusRequest) Reset() {
	*x = UpdateLabelStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLabelStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLabelStatusRequest) ProtoMessage() {}

func (x *UpdateLabelStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLabelStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateLabelStatusRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateLabelStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateLabelStatusRequest) GetStatus() LabelStatus {
	if x != nil {
		return x.Status
	}
	return LabelStatus_LabelsStatusDisabled
}

func (x *UpdateLabelStatusRequest) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

type UpdateLabelStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateLabelStatusReply) Reset() {
	*x = UpdateLabelStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLabelStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLabelStatusReply) ProtoMessage() {}

func (x *UpdateLabelStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLabelStatusReply.ProtoReflect.Descriptor instead.
func (*UpdateLabelStatusReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{23}
}

type DeleteLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteLabelRequest) Reset() {
	*x = DeleteLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLabelRequest) ProtoMessage() {}

func (x *DeleteLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLabelRequest.ProtoReflect.Descriptor instead.
func (*DeleteLabelRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteLabelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteLabelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteLabelReply) Reset() {
	*x = DeleteLabelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLabelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLabelReply) ProtoMessage() {}

func (x *DeleteLabelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLabelReply.ProtoReflect.Descriptor instead.
func (*DeleteLabelReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{25}
}

type ListLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size   int64  `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
	Page   int64  `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Editor string `protobuf:"bytes,3,opt,name=editor,json=editor,proto3" json:"editor"`
	Name   string `protobuf:"bytes,4,opt,name=name,json=name,proto3" json:"name"`
}

func (x *ListLabelRequest) Reset() {
	*x = ListLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLabelRequest) ProtoMessage() {}

func (x *ListLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLabelRequest.ProtoReflect.Descriptor instead.
func (*ListLabelRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{26}
}

func (x *ListLabelRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListLabelRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLabelRequest) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

func (x *ListLabelRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListLabelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels    []*Label `protobuf:"bytes,1,rep,name=labels,json=labels,proto3" json:"labels"`
	TotalSize int64    `protobuf:"varint,2,opt,name=total_size,json=totalSize,proto3" json:"total_size"`
}

func (x *ListLabelReply) Reset() {
	*x = ListLabelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLabelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLabelReply) ProtoMessage() {}

func (x *ListLabelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLabelReply.ProtoReflect.Descriptor instead.
func (*ListLabelReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{27}
}

func (x *ListLabelReply) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ListLabelReply) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type FilterByLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string   `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Size   int32    `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Offset string   `protobuf:"bytes,3,opt,name=offset,json=offset,proto3" json:"offset"`
	Fields []string `protobuf:"bytes,4,rep,name=fields,json=fields,proto3" json:"fields"`
}

func (x *FilterByLabelRequest) Reset() {
	*x = FilterByLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterByLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterByLabelRequest) ProtoMessage() {}

func (x *FilterByLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterByLabelRequest.ProtoReflect.Descriptor instead.
func (*FilterByLabelRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{28}
}

func (x *FilterByLabelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FilterByLabelRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FilterByLabelRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *FilterByLabelRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type FilterByLabelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items  []*UserProfile `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Offset string         `protobuf:"bytes,2,opt,name=offset,json=offset,proto3" json:"offset"`
}

func (x *FilterByLabelReply) Reset() {
	*x = FilterByLabelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterByLabelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterByLabelReply) ProtoMessage() {}

func (x *FilterByLabelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterByLabelReply.ProtoReflect.Descriptor instead.
func (*FilterByLabelReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{29}
}

func (x *FilterByLabelReply) GetItems() []*UserProfile {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FilterByLabelReply) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

type EditorListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EditorListRequest) Reset() {
	*x = EditorListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditorListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditorListRequest) ProtoMessage() {}

func (x *EditorListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditorListRequest.ProtoReflect.Descriptor instead.
func (*EditorListRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{30}
}

type EditorListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Editors []string `protobuf:"bytes,1,rep,name=editors,json=editors,proto3" json:"editors"`
}

func (x *EditorListReply) Reset() {
	*x = EditorListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditorListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditorListReply) ProtoMessage() {}

func (x *EditorListReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditorListReply.ProtoReflect.Descriptor instead.
func (*EditorListReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{31}
}

func (x *EditorListReply) GetEditors() []string {
	if x != nil {
		return x.Editors
	}
	return nil
}

type LabelCountryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *LabelCountryRequest) Reset() {
	*x = LabelCountryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelCountryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelCountryRequest) ProtoMessage() {}

func (x *LabelCountryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelCountryRequest.ProtoReflect.Descriptor instead.
func (*LabelCountryRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{32}
}

func (x *LabelCountryRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LabelCountry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	Name        string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Count       int32  `protobuf:"varint,3,opt,name=count,json=count,proto3" json:"count"`
}

func (x *LabelCountry) Reset() {
	*x = LabelCountry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelCountry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelCountry) ProtoMessage() {}

func (x *LabelCountry) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelCountry.ProtoReflect.Descriptor instead.
func (*LabelCountry) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{33}
}

func (x *LabelCountry) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *LabelCountry) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LabelCountry) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type LabelCountryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Countries []*LabelCountry `protobuf:"bytes,1,rep,name=countries,json=countries,proto3" json:"countries"`
}

func (x *LabelCountryReply) Reset() {
	*x = LabelCountryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelCountryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelCountryReply) ProtoMessage() {}

func (x *LabelCountryReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelCountryReply.ProtoReflect.Descriptor instead.
func (*LabelCountryReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{34}
}

func (x *LabelCountryReply) GetCountries() []*LabelCountry {
	if x != nil {
		return x.Countries
	}
	return nil
}

type LabelLanguageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	CountryCode string `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
}

func (x *LabelLanguageRequest) Reset() {
	*x = LabelLanguageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelLanguageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelLanguageRequest) ProtoMessage() {}

func (x *LabelLanguageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelLanguageRequest.ProtoReflect.Descriptor instead.
func (*LabelLanguageRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{35}
}

func (x *LabelLanguageRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LabelLanguageRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

type LabelLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageCode string `protobuf:"bytes,1,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	Name         string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Count        int32  `protobuf:"varint,3,opt,name=count,json=count,proto3" json:"count"`
}

func (x *LabelLanguage) Reset() {
	*x = LabelLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelLanguage) ProtoMessage() {}

func (x *LabelLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelLanguage.ProtoReflect.Descriptor instead.
func (*LabelLanguage) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{36}
}

func (x *LabelLanguage) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *LabelLanguage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LabelLanguage) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type LabelLanguageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Languages []*LabelLanguage `protobuf:"bytes,1,rep,name=languages,json=languages,proto3" json:"languages"`
}

func (x *LabelLanguageReply) Reset() {
	*x = LabelLanguageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelLanguageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelLanguageReply) ProtoMessage() {}

func (x *LabelLanguageReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelLanguageReply.ProtoReflect.Descriptor instead.
func (*LabelLanguageReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{37}
}

func (x *LabelLanguageReply) GetLanguages() []*LabelLanguage {
	if x != nil {
		return x.Languages
	}
	return nil
}

type Recall struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string         `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	LabelId           string         `protobuf:"bytes,2,opt,name=label_id,json=labelId,proto3" json:"label_id"`
	UserArea          []string       `protobuf:"bytes,3,rep,name=user_area,json=userArea,proto3" json:"user_area"`
	Language          string         `protobuf:"bytes,4,opt,name=language,json=language,proto3" json:"language"`
	Way               RecallWay      `protobuf:"varint,5,opt,name=way,json=way,proto3,enum=api.user_profile.v1.RecallWay" json:"way"`
	TimeType          RecallTimeType `protobuf:"varint,6,opt,name=time_type,json=timeType,proto3,enum=api.user_profile.v1.RecallTimeType" json:"time_type"`
	TimeRange         TimeRange      `protobuf:"varint,7,opt,name=time_range,json=timeRange,proto3,enum=api.user_profile.v1.TimeRange" json:"time_range"`
	TimeNodes         []string       `protobuf:"bytes,8,rep,name=time_nodes,json=timeNodes,proto3" json:"time_nodes"`
	Title             string         `protobuf:"bytes,9,opt,name=title,json=title,proto3" json:"title"`
	Content           string         `protobuf:"bytes,10,opt,name=content,json=content,proto3" json:"content"`
	Image             string         `protobuf:"bytes,11,opt,name=image,json=image,proto3" json:"image"`
	CoverUsers        int64          `protobuf:"varint,12,opt,name=cover_users,json=coverUsers,proto3" json:"cover_users"`
	JumpType          JumpType       `protobuf:"varint,13,opt,name=jump_type,json=jumpType,proto3,enum=api.user_profile.v1.JumpType" json:"jump_type"`
	JumpLink          string         `protobuf:"bytes,14,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link"`
	Creator           string         `protobuf:"bytes,15,opt,name=creator,json=creator,proto3" json:"creator"`
	CreatedAt         int64          `protobuf:"varint,16,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Status            bool           `protobuf:"varint,17,opt,name=status,json=status,proto3" json:"status"`
	SmsTemplate       string         `protobuf:"bytes,18,opt,name=sms_template,json=smsTemplate,proto3" json:"sms_template"`
	Link2             string         `protobuf:"bytes,19,opt,name=link2,json=link2,proto3" json:"link2"`
	EmailTemplateEnum int32          `protobuf:"varint,20,opt,name=email_template_enum,json=emailTemplateEnum,proto3" json:"email_template_enum"`
}

func (x *Recall) Reset() {
	*x = Recall{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Recall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recall) ProtoMessage() {}

func (x *Recall) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recall.ProtoReflect.Descriptor instead.
func (*Recall) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{38}
}

func (x *Recall) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Recall) GetLabelId() string {
	if x != nil {
		return x.LabelId
	}
	return ""
}

func (x *Recall) GetUserArea() []string {
	if x != nil {
		return x.UserArea
	}
	return nil
}

func (x *Recall) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Recall) GetWay() RecallWay {
	if x != nil {
		return x.Way
	}
	return RecallWay_RecallWayApp
}

func (x *Recall) GetTimeType() RecallTimeType {
	if x != nil {
		return x.TimeType
	}
	return RecallTimeType_RecallTimeTypeAtOnce
}

func (x *Recall) GetTimeRange() TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return TimeRange_TimeRangeUnknown
}

func (x *Recall) GetTimeNodes() []string {
	if x != nil {
		return x.TimeNodes
	}
	return nil
}

func (x *Recall) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Recall) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Recall) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Recall) GetCoverUsers() int64 {
	if x != nil {
		return x.CoverUsers
	}
	return 0
}

func (x *Recall) GetJumpType() JumpType {
	if x != nil {
		return x.JumpType
	}
	return JumpType_JumpTypeUnknown
}

func (x *Recall) GetJumpLink() string {
	if x != nil {
		return x.JumpLink
	}
	return ""
}

func (x *Recall) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Recall) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Recall) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *Recall) GetSmsTemplate() string {
	if x != nil {
		return x.SmsTemplate
	}
	return ""
}

func (x *Recall) GetLink2() string {
	if x != nil {
		return x.Link2
	}
	return ""
}

func (x *Recall) GetEmailTemplateEnum() int32 {
	if x != nil {
		return x.EmailTemplateEnum
	}
	return 0
}

type AddRecallReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddRecallReply) Reset() {
	*x = AddRecallReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRecallReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRecallReply) ProtoMessage() {}

func (x *AddRecallReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRecallReply.ProtoReflect.Descriptor instead.
func (*AddRecallReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{39}
}

type GetRecallsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LabelId string `protobuf:"bytes,1,opt,name=label_id,json=labelId,proto3" json:"label_id"`
	Way     int64  `protobuf:"varint,2,opt,name=way,json=way,proto3" json:"way"`
	Size    int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page    int64  `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
}

func (x *GetRecallsRequest) Reset() {
	*x = GetRecallsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallsRequest) ProtoMessage() {}

func (x *GetRecallsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallsRequest.ProtoReflect.Descriptor instead.
func (*GetRecallsRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{40}
}

func (x *GetRecallsRequest) GetLabelId() string {
	if x != nil {
		return x.LabelId
	}
	return ""
}

func (x *GetRecallsRequest) GetWay() int64 {
	if x != nil {
		return x.Way
	}
	return 0
}

func (x *GetRecallsRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetRecallsRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GetRecallsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Recalls   []*Recall `protobuf:"bytes,1,rep,name=recalls,json=recalls,proto3" json:"recalls"`
	TotalSize int64     `protobuf:"varint,2,opt,name=total_size,json=totalSize,proto3" json:"total_size"`
}

func (x *GetRecallsReply) Reset() {
	*x = GetRecallsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallsReply) ProtoMessage() {}

func (x *GetRecallsReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallsReply.ProtoReflect.Descriptor instead.
func (*GetRecallsReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{41}
}

func (x *GetRecallsReply) GetRecalls() []*Recall {
	if x != nil {
		return x.Recalls
	}
	return nil
}

func (x *GetRecallsReply) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type GetRecallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetRecallRequest) Reset() {
	*x = GetRecallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallRequest) ProtoMessage() {}

func (x *GetRecallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallRequest.ProtoReflect.Descriptor instead.
func (*GetRecallRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{42}
}

func (x *GetRecallRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ExecRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string    `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	RecallId   string    `protobuf:"bytes,2,opt,name=recall_id,json=recallId,proto3" json:"recall_id"`
	ExecAt     string    `protobuf:"bytes,3,opt,name=exec_at,json=execAt,proto3" json:"exec_at"`
	UserArea   string    `protobuf:"bytes,4,opt,name=user_area,json=userArea,proto3" json:"user_area"`
	Language   string    `protobuf:"bytes,5,opt,name=language,json=language,proto3" json:"language"`
	Way        RecallWay `protobuf:"varint,6,opt,name=way,json=way,proto3,enum=api.user_profile.v1.RecallWay" json:"way"`
	CoverUsers int64     `protobuf:"varint,7,opt,name=cover_users,json=coverUsers,proto3" json:"cover_users"`
}

func (x *ExecRecord) Reset() {
	*x = ExecRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecRecord) ProtoMessage() {}

func (x *ExecRecord) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecRecord.ProtoReflect.Descriptor instead.
func (*ExecRecord) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{43}
}

func (x *ExecRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExecRecord) GetRecallId() string {
	if x != nil {
		return x.RecallId
	}
	return ""
}

func (x *ExecRecord) GetExecAt() string {
	if x != nil {
		return x.ExecAt
	}
	return ""
}

func (x *ExecRecord) GetUserArea() string {
	if x != nil {
		return x.UserArea
	}
	return ""
}

func (x *ExecRecord) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ExecRecord) GetWay() RecallWay {
	if x != nil {
		return x.Way
	}
	return RecallWay_RecallWayApp
}

func (x *ExecRecord) GetCoverUsers() int64 {
	if x != nil {
		return x.CoverUsers
	}
	return 0
}

type GetRecallExecRecordsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecallId string `protobuf:"bytes,1,opt,name=recall_id,json=recallId,proto3" json:"recall_id"`
	Size     int64  `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Page     int64  `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`
}

func (x *GetRecallExecRecordsRequest) Reset() {
	*x = GetRecallExecRecordsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallExecRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallExecRecordsRequest) ProtoMessage() {}

func (x *GetRecallExecRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallExecRecordsRequest.ProtoReflect.Descriptor instead.
func (*GetRecallExecRecordsRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{44}
}

func (x *GetRecallExecRecordsRequest) GetRecallId() string {
	if x != nil {
		return x.RecallId
	}
	return ""
}

func (x *GetRecallExecRecordsRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetRecallExecRecordsRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GetRecallExecRecordsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records   []*ExecRecord `protobuf:"bytes,1,rep,name=records,json=records,proto3" json:"records"`
	TotalSize int64         `protobuf:"varint,2,opt,name=total_size,json=totalSize,proto3" json:"total_size"`
}

func (x *GetRecallExecRecordsReply) Reset() {
	*x = GetRecallExecRecordsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallExecRecordsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallExecRecordsReply) ProtoMessage() {}

func (x *GetRecallExecRecordsReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallExecRecordsReply.ProtoReflect.Descriptor instead.
func (*GetRecallExecRecordsReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{45}
}

func (x *GetRecallExecRecordsReply) GetRecords() []*ExecRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *GetRecallExecRecordsReply) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type DeleteRecallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Deleter string `protobuf:"bytes,2,opt,name=deleter,json=deleter,proto3" json:"deleter"`
}

func (x *DeleteRecallRequest) Reset() {
	*x = DeleteRecallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecallRequest) ProtoMessage() {}

func (x *DeleteRecallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecallRequest.ProtoReflect.Descriptor instead.
func (*DeleteRecallRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{46}
}

func (x *DeleteRecallRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteRecallRequest) GetDeleter() string {
	if x != nil {
		return x.Deleter
	}
	return ""
}

type DeleteRecallReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRecallReply) Reset() {
	*x = DeleteRecallReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecallReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecallReply) ProtoMessage() {}

func (x *DeleteRecallReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecallReply.ProtoReflect.Descriptor instead.
func (*DeleteRecallReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{47}
}

type GetRecallTouchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LabelId  string   `protobuf:"bytes,1,opt,name=label_id,json=labelId,proto3" json:"label_id"`
	Way      int64    `protobuf:"varint,2,opt,name=way,json=way,proto3" json:"way"`
	UserArea []string `protobuf:"bytes,3,rep,name=user_area,json=userArea,proto3" json:"user_area"`
	Language string   `protobuf:"bytes,4,opt,name=language,json=language,proto3" json:"language"`
}

func (x *GetRecallTouchRequest) Reset() {
	*x = GetRecallTouchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallTouchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallTouchRequest) ProtoMessage() {}

func (x *GetRecallTouchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallTouchRequest.ProtoReflect.Descriptor instead.
func (*GetRecallTouchRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{48}
}

func (x *GetRecallTouchRequest) GetLabelId() string {
	if x != nil {
		return x.LabelId
	}
	return ""
}

func (x *GetRecallTouchRequest) GetWay() int64 {
	if x != nil {
		return x.Way
	}
	return 0
}

func (x *GetRecallTouchRequest) GetUserArea() []string {
	if x != nil {
		return x.UserArea
	}
	return nil
}

func (x *GetRecallTouchRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type GetRecallTouchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result string `protobuf:"bytes,1,opt,name=result,json=result,proto3" json:"result"`
}

func (x *GetRecallTouchReply) Reset() {
	*x = GetRecallTouchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallTouchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallTouchReply) ProtoMessage() {}

func (x *GetRecallTouchReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallTouchReply.ProtoReflect.Descriptor instead.
func (*GetRecallTouchReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{49}
}

func (x *GetRecallTouchReply) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

type GetRecallCoverUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecallId string `protobuf:"bytes,1,opt,name=recall_id,json=recallId,proto3" json:"recall_id"`
	Size     int32  `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Offset   string `protobuf:"bytes,3,opt,name=offset,json=offset,proto3" json:"offset"`
}

func (x *GetRecallCoverUsersRequest) Reset() {
	*x = GetRecallCoverUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallCoverUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallCoverUsersRequest) ProtoMessage() {}

func (x *GetRecallCoverUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallCoverUsersRequest.ProtoReflect.Descriptor instead.
func (*GetRecallCoverUsersRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{50}
}

func (x *GetRecallCoverUsersRequest) GetRecallId() string {
	if x != nil {
		return x.RecallId
	}
	return ""
}

func (x *GetRecallCoverUsersRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetRecallCoverUsersRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

type GetRecallCoverUsersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items  []*UserProfile `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Offset string         `protobuf:"bytes,2,opt,name=offset,json=offset,proto3" json:"offset"`
}

func (x *GetRecallCoverUsersReply) Reset() {
	*x = GetRecallCoverUsersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecallCoverUsersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecallCoverUsersReply) ProtoMessage() {}

func (x *GetRecallCoverUsersReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecallCoverUsersReply.ProtoReflect.Descriptor instead.
func (*GetRecallCoverUsersReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{51}
}

func (x *GetRecallCoverUsersReply) GetItems() []*UserProfile {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GetRecallCoverUsersReply) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

type SetRecallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Status   int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	Modifier string `protobuf:"bytes,3,opt,name=modifier,json=modifier,proto3" json:"modifier"`
}

func (x *SetRecallRequest) Reset() {
	*x = SetRecallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecallRequest) ProtoMessage() {}

func (x *SetRecallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecallRequest.ProtoReflect.Descriptor instead.
func (*SetRecallRequest) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{52}
}

func (x *SetRecallRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetRecallRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SetRecallRequest) GetModifier() string {
	if x != nil {
		return x.Modifier
	}
	return ""
}

type SetRecallReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetRecallReply) Reset() {
	*x = SetRecallReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_profile_v1_models_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRecallReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRecallReply) ProtoMessage() {}

func (x *SetRecallReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_profile_v1_models_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRecallReply.ProtoReflect.Descriptor instead.
func (*SetRecallReply) Descriptor() ([]byte, []int) {
	return file_user_profile_v1_models_proto_rawDescGZIP(), []int{53}
}

var File_user_profile_v1_models_proto protoreflect.FileDescriptor

var file_user_profile_v1_models_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xdc, 0x01, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x49, 0x64, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x70,
	0x75, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x49, 0x44, 0x52, 0x06, 0x70, 0x75, 0x73,
	0x68, 0x49, 0x64, 0x12, 0x9c, 0x01, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x92, 0x41, 0x5e, 0x2a, 0x5c, 0xe8, 0xa1,
	0x8c, 0xe4, 0xb8, 0xba, 0xe5, 0x8f, 0x98, 0xe6, 0x9b, 0xb4, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x96, 0xb0, 0xe7, 0x94, 0x9f, 0xe6, 0x88, 0x90,
	0x70, 0x75, 0x73, 0x68, 0x5f, 0x69, 0x64, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe7, 0x99,
	0xbb, 0xe5, 0xbd, 0x95, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8,
	0x80, 0xe5, 0x8f, 0x98, 0xe6, 0x9b, 0xb4, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0x9b,
	0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x8f, 0x98, 0xe6, 0x9b, 0xb4, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x13, 0x0a, 0x11, 0x50, 0x75, 0x73, 0x68, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xc3, 0x02, 0x0a, 0x13, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x29, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0x49, 0x44,
	0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x87, 0x8f, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x2a, 0x25, 0xe5, 0x81, 0x8f, 0xe7, 0xa7,
	0xbb, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe7, 0xa9, 0xba, 0xef, 0xbc, 0x8c, 0xe4, 0xbb,
	0x8e, 0xe7, 0xac, 0xac, 0xe4, 0xb8, 0x80, 0xe9, 0xa1, 0xb5, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a,
	0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xc2, 0x01,
	0x0a, 0x10, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0x52, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0x8e,
	0xa8, 0xe9, 0x80, 0x81, 0x49, 0x44, 0x52, 0x06, 0x70, 0x75, 0x73, 0x68, 0x49, 0x64, 0x12, 0x32,
	0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae,
	0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x68, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x34, 0x0a, 0x13,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x4d, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xc4, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x09, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x12, 0x45, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x09, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x80, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12,
	0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xae, 0x80, 0xe4,
	0xbb, 0x8b, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x22, 0x4d, 0x0a, 0x11, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x22, 0x5f, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x9f, 0x02, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x36, 0x0a, 0x0a,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe7, 0x9a, 0x84,
	0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x90, 0x8d, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x09, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x09, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x80, 0xbc, 0x52, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x22, 0x64, 0x0a, 0x17, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x49, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x5e, 0x0a, 0x15, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x45, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x3c, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xd9, 0x17, 0x0a, 0x0b, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x87, 0x01, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x6b, 0x92, 0x41, 0x68, 0x2a, 0x66, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0x31, 0xef, 0xbc, 0x9a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1,
	0xe5, 0x95, 0x86, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93,
	0xe5, 0x95, 0x86, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe7, 0x9b, 0x91, 0xe7, 0xae, 0xa1,
	0xe6, 0x9c, 0xba, 0xe6, 0x9e, 0x84, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xae, 0x98,
	0xe6, 0x96, 0xb9, 0xe5, 0x8f, 0xb7, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0x4b, 0x4f, 0x4c,
	0xef, 0xbc, 0x9b, 0x36, 0xef, 0xbc, 0x9a, 0xe6, 0x8a, 0x95, 0xe8, 0xb5, 0x84, 0xe8, 0x80, 0x85,
	0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x12, 0x36, 0x0a, 0x0d, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb3, 0xa8, 0xe5, 0x86, 0x8c, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe6, 0xb3,
	0xa8, 0xe5, 0x86, 0x8c, 0xe6, 0x97, 0xb6, 0xe7, 0x9a, 0x84, 0x49, 0x50, 0x52, 0x0a, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x49, 0x70, 0x12, 0x4f, 0x0a, 0x15, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe6, 0xb3,
	0xa8, 0xe5, 0x86, 0x8c, 0xe6, 0x97, 0xb6, 0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x13, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x45, 0x0a, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0x92, 0x41,
	0x25, 0x2a, 0x23, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6, 0xac, 0xa1, 0xe7,
	0x99, 0xbb, 0xe5, 0xbd, 0x95, 0xe7, 0x9a, 0x84, 0xe7, 0xa7, 0xbb, 0xe5, 0x8a, 0xa8, 0xe8, 0xae,
	0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe5, 0xae, 0xa2, 0xe6, 0x88, 0xb7,
	0xe7, 0xab, 0xaf, 0x49, 0x50, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12,
	0x36, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0xb9, 0xb3, 0xe5, 0x8f, 0xb0, 0x3a, 0x70,
	0x63, 0x3b, 0x69, 0x6f, 0x73, 0x3b, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x36, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8, 0xaf, 0xad, 0xe8, 0xa8,
	0x80, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x4b, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe5, 0x81, 0x8f, 0xe5, 0xa5,
	0xbd, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe5,
	0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x4f, 0x0a, 0x13, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe7, 0xac, 0xac, 0xe4, 0xb8, 0x80, 0xe6, 0xac, 0xa1, 0xe6,
	0x89, 0x93, 0xe5, 0xbc, 0x80, 0x41, 0x50, 0x50, 0xe7, 0x9a, 0x84, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6,
	0xac, 0xa1, 0xe6, 0x89, 0x93, 0xe5, 0xbc, 0x80, 0x41, 0x50, 0x50, 0xe7, 0x9a, 0x84, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d,
	0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6, 0xac,
	0xa1, 0xe7, 0x99, 0xbb, 0xe5, 0xbd, 0x95, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0d, 0x6c,
	0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x10,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x9c, 0x80,
	0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6, 0xac, 0xa1, 0xe6, 0xb4, 0xbb, 0xe8, 0xb7, 0x83, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x0a, 0x66, 0x61, 0x6e, 0x73, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09,
	0xe7, 0xb2, 0x89, 0xe4, 0xb8, 0x9d, 0xe6, 0x95, 0xb0, 0x52, 0x09, 0x66, 0x61, 0x6e, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe5, 0x85, 0xb3, 0xe6, 0xb3, 0xa8, 0xe6, 0x95, 0xb0, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x73,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x96, 0xe6, 0x95, 0xb0, 0x52, 0x0a, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x10, 0x70, 0x6f, 0x73, 0x74,
	0x73, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5,
	0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0x80, 0xbb, 0xe7, 0x82, 0xb9, 0xe8, 0xb5, 0x9e, 0xe6, 0x95,
	0xb0, 0x52, 0x0e, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x4c, 0x69, 0x6b, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x4b, 0x0a, 0x12, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92,
	0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90,
	0xe6, 0x80, 0xbb, 0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab, 0xe6, 0x95, 0xb0, 0x52, 0x10, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x49,
	0x0a, 0x11, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18,
	0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0x80, 0xbb, 0xe5,
	0x9b, 0x9e, 0xe5, 0xa4, 0x8d, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x10, 0x70, 0x6f, 0x73,
	0x74, 0x73, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89,
	0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0x80, 0xbb, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe6,
	0x95, 0xb0, 0x52, 0x0e, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x13, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5, 0xb8, 0x96, 0xe5,
	0xad, 0x90, 0xe6, 0x80, 0xbb, 0xe6, 0x94, 0xb6, 0xe8, 0x97, 0x8f, 0xe6, 0x95, 0xb0, 0x52, 0x11,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x35, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xaf, 0x84,
	0xe8, 0xae, 0xba, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0x95, 0xb0, 0x52, 0x0a, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x6c, 0x69, 0x6b, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x2a, 0x0f, 0xe7, 0x82, 0xb9, 0xe8, 0xb5, 0x9e, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6,
	0x95, 0xb0, 0x52, 0x09, 0x6c, 0x69, 0x6b, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a,
	0x0a, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x80,
	0xbb, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x31, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x94, 0xaf, 0xe4,
	0xbb, 0x98, 0xe6, 0x80, 0xbb, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x70, 0x61, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0e, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe6, 0x8f, 0x90, 0xe4, 0xba, 0xa4, 0xe5, 0x8f, 0x8d, 0xe9, 0xa6, 0x88,
	0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a,
	0x13, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x37, 0x64, 0x61, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a,
	0x16, 0x37, 0xe5, 0xa4, 0xa9, 0xe6, 0x89, 0x93, 0xe5, 0xbc, 0x80, 0xe8, 0xbf, 0x87, 0x61, 0x70,
	0x70, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x70,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x42, 0x0a, 0x10, 0x70, 0x6f, 0x73,
	0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x13, 0x37, 0xe5, 0xa4, 0xa9, 0xe5, 0x86,
	0x85, 0xe7, 0x9a, 0x84, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x96, 0xe6, 0x95, 0xb0, 0x52, 0x0e, 0x70,
	0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x4a, 0x0a,
	0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x37, 0x64,
	0x61, 0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x19, 0x37,
	0xe5, 0xa4, 0xa9, 0xe5, 0x86, 0x85, 0xe7, 0x9a, 0x84, 0xe8, 0xbd, 0xac, 0xe8, 0xb5, 0x9e, 0xe8,
	0xaf, 0x84, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x5e, 0x0a, 0x17, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x37, 0x64, 0x61, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28, 0x03, 0x42, 0x27, 0x92, 0x41, 0x24, 0x2a,
	0x22, 0x37, 0xe5, 0xa4, 0xa9, 0xe5, 0x86, 0x85, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe8, 0xbf,
	0x87, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe7, 0x9a, 0x84, 0xe6, 0xac, 0xa1,
	0xe6, 0x95, 0xb0, 0x52, 0x14, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x5f, 0x0a, 0x16, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x37,
	0x64, 0x61, 0x79, 0x18, 0x22, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x2a, 0x25,
	0x37, 0xe5, 0xa4, 0xa9, 0xe5, 0x86, 0x85, 0xe7, 0x82, 0xb9, 0xe5, 0x87, 0xbb, 0xe8, 0xbf, 0x87,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe6,
	0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x13, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x5c, 0x0a, 0x19, 0x63, 0x6c,
	0x69, 0x63, 0x6b, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x18, 0x23, 0x20, 0x01, 0x28, 0x03, 0x42, 0x21, 0x92,
	0x41, 0x1e, 0x2a, 0x1c, 0x37, 0xe5, 0xa4, 0xa9, 0xe5, 0x86, 0x85, 0xe7, 0x82, 0xb9, 0xe5, 0x87,
	0xbb, 0xe8, 0xbf, 0x87, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0,
	0x52, 0x16, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x50, 0x0a, 0x13, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x5f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x03, 0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a, 0x1c, 0x37, 0xe5, 0xa4, 0xa9,
	0xe5, 0x86, 0x85, 0xe7, 0x82, 0xb9, 0xe5, 0x87, 0xbb, 0xe8, 0xbf, 0x87, 0xe5, 0xb9, 0xbf, 0xe5,
	0x91, 0x8a, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x41,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x51, 0x0a, 0x15, 0x6d, 0x6f,
	0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x37,
	0x64, 0x61, 0x79, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x19,
	0x37, 0xe5, 0xa4, 0xa9, 0xe5, 0x86, 0x85, 0xe6, 0xa8, 0xa1, 0xe6, 0x8b, 0x9f, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x12, 0x6d, 0x6f, 0x63, 0x6b, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x37, 0x64, 0x61, 0x79, 0x12, 0x41, 0x0a,
	0x0f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x26, 0x20, 0x03, 0x28, 0x09, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe4, 0xbd, 0xbf,
	0xe7, 0x94, 0xa8, 0xe8, 0xbf, 0x87, 0xe7, 0x9a, 0x84, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49,
	0x44, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x47, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x27, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe8, 0xbf, 0x87, 0xe7, 0x9a, 0x84,
	0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x11, 0x75, 0x73, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x1a, 0x75, 0x73, 0x65,
	0x64, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x28, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1d, 0x92,
	0x41, 0x1a, 0x2a, 0x18, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe8, 0xbf, 0x87, 0xe7, 0x9a, 0x84,
	0xe5, 0x81, 0x8f, 0xe5, 0xa5, 0xbd, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x17, 0x75, 0x73,
	0x65, 0x64, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe8, 0xbf,
	0x87, 0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x10,
	0x75, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73,
	0x12, 0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x2a, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe8,
	0xbf, 0x87, 0xe7, 0x9a, 0x84, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x64, 0x49, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x07, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0x8e,
	0xa8, 0xe9, 0x80, 0x81, 0x49, 0x44, 0x52, 0x06, 0x70, 0x75, 0x73, 0x68, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x07, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x77, 0x69, 0x6b, 0x69, 0x49, 0x44, 0x52, 0x06, 0x77, 0x69,
	0x6b, 0x69, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8,
	0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x8d, 0x05, 0x0a, 0x05, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x35, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x20, 0xe6, 0xa0,
	0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0xef, 0xbc, 0x8c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6,
	0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x5d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0x3a, 0xe7, 0xa6, 0x81, 0xe7, 0x94, 0xa8,
	0x3b, 0x31, 0x3a, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x45, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2d, 0x92, 0x41, 0x2a, 0x2a, 0x28, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0xaf, 0xb9, 0xe5,
	0xba, 0x94, 0xe7, 0x9a, 0x84, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0xe6, 0x9d, 0xa1, 0xe4, 0xbb,
	0xb6, 0xef, 0xbc, 0x8c, 0x6a, 0x73, 0x6f, 0x6e, 0xe6, 0xa0, 0xbc, 0xe5, 0xbc, 0x8f, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x33, 0x92, 0x41, 0x30, 0x2a,
	0x2e, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3,
	0xef, 0xbc, 0x88, 0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0xef, 0xbc, 0x89, 0x2c,
	0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x97, 0xb6, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4e, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2f,
	0x92, 0x41, 0x2c, 0x2a, 0x2a, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6, 0xac,
	0xa1, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x8c,
	0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x97, 0xb6, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x44, 0x0a, 0x06, 0x65, 0x64,
	0x69, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x2a,
	0x27, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6, 0xac, 0xa1, 0xe6, 0x9b, 0xb4,
	0xe6, 0x96, 0xb0, 0xe4, 0xba, 0xba, 0xef, 0xbc, 0x8c, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6,
	0x97, 0xb6, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72,
	0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52,
	0x0b, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0a,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x2e, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x30, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0xcc, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x5d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0x3a, 0xe7, 0xa6, 0x81, 0xe7, 0x94, 0xa8,
	0x3b, 0x31, 0x3a, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8,
	0x80, 0xe6, 0xac, 0xa1, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe4, 0xba, 0xba, 0x52, 0x06, 0x65,
	0x64, 0x69, 0x74, 0x6f, 0x72, 0x22, 0x18, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x33, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xd8, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x22, 0x92, 0x41, 0x1f,
	0x2a, 0x1d, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe6, 0x95,
	0xb0, 0xe9, 0x87, 0x8f, 0xef, 0xbc, 0x9b, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x06,
	0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41,
	0x17, 0x2a, 0x15, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe7, 0xbc, 0x96, 0xe8, 0xbe, 0x91, 0xe4,
	0xba, 0xba, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72,
	0x12, 0x31, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d,
	0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x45, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2d, 0x0a,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0xe9, 0x87,
	0x8f, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xda, 0x02, 0x0a,
	0x14, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x69, 0x64,
	0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a, 0x18,
	0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x87, 0x8f, 0x2c,
	0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x3a, 0x02, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x2a, 0x25, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x2c,
	0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe7, 0xa9, 0xba, 0xef, 0xbc, 0x8c, 0xe4, 0xbb, 0x8e, 0xe7,
	0xac, 0xac, 0xe4, 0xb8, 0x80, 0xe9, 0xa1, 0xb5, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x52, 0x06,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0xa0, 0x01, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x87, 0x01, 0x92, 0x41, 0x83, 0x01, 0x2a, 0x80,
	0x01, 0xe8, 0xa6, 0x81, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x9a, 0x84, 0xe5, 0xad, 0x97,
	0xe6, 0xae, 0xb5, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x2c, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c,
	0xe4, 0xb8, 0x8d, 0xe5, 0xa1, 0xab, 0xe5, 0x86, 0x99, 0xe5, 0x88, 0x99, 0xe4, 0xbc, 0x9a, 0xe6,
	0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe5, 0xad, 0x97, 0xe6, 0xae,
	0xb5, 0xef, 0xbc, 0x8c, 0xe5, 0xbb, 0xba, 0xe8, 0xae, 0xae, 0xe6, 0x8c, 0x89, 0xe7, 0x85, 0xa7,
	0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe9, 0x83, 0xa8, 0xe5,
	0x88, 0x86, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x2c, 0xe5, 0x8f, 0xaf, 0xe4, 0xbb, 0xa5, 0xe6,
	0x8f, 0x90, 0xe9, 0xab, 0x98, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe6, 0x80, 0xa7, 0xe8, 0x83,
	0xbd, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x12, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x56, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94,
	0xbb, 0xe5, 0x83, 0x8f, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75,
	0x65, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x0f, 0xe4,
	0xb8, 0x8b, 0xe4, 0xb8, 0x80, 0xe9, 0xa1, 0xb5, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0xd2, 0x01,
	0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x13, 0x0a,
	0x11, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x41, 0x0a, 0x0f, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x07, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0xbc, 0x96,
	0xe8, 0xbe, 0x91, 0xe4, 0xba, 0xba, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07, 0x65, 0x64,
	0x69, 0x74, 0x6f, 0x72, 0x73, 0x22, 0x3b, 0x0a, 0x13, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x08, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x02,
	0x69, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x0c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x0c,
	0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0xd2, 0x01, 0x04, 0x74,
	0x72, 0x75, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18,
	0x92, 0x41, 0x15, 0x2a, 0x0c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x12, 0x92,
	0x41, 0x0f, 0x2a, 0x06, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75,
	0x65, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x11, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3f, 0x0a,
	0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0x78,
	0x0a, 0x14, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49,
	0x44, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63,
	0x6f, 0x64, 0x65, 0xd2, 0x01, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x5e, 0x0a, 0x0d, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x56, 0x0a, 0x12, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x40,
	0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73,
	0x22, 0x85, 0x0b, 0x0a, 0x06, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12, 0x35, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x20, 0xe5, 0x8f,
	0xac, 0xe5, 0x9b, 0x9e, 0x49, 0x44, 0xef, 0xbc, 0x8c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6,
	0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x28, 0x0a, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0x69, 0x64, 0x52, 0x07, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x8c, 0xba, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x41, 0x72,
	0x65, 0x61, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0xad, 0xe8, 0xa8,
	0x80, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x71, 0x0a, 0x03, 0x77,
	0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x63, 0x61, 0x6c, 0x6c, 0x57, 0x61, 0x79, 0x42, 0x3f, 0x92, 0x41, 0x3c, 0x2a, 0x3a, 0xe5,
	0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x30, 0x3a, 0x57, 0x68, 0x61,
	0x74, 0x73, 0x61, 0x70, 0x70, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x2c, 0x31, 0x3a, 0xe7, 0x9f,
	0xad, 0xe4, 0xbf, 0xa1, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x2c, 0x32, 0x3a, 0xe9, 0x82, 0xae,
	0xe4, 0xbb, 0xb6, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x52, 0x03, 0x77, 0x61, 0x79, 0x12, 0x7f,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x3d, 0x92, 0x41, 0x3a, 0x2a, 0x38, 0xe5, 0x8f, 0xac,
	0xe5, 0x9b, 0x9e, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x30, 0x3a, 0xe7, 0xab, 0x8b, 0xe5, 0x8d, 0xb3, 0x2c, 0x31, 0x3a,
	0xe5, 0xae, 0x9a, 0xe6, 0x97, 0xb6, 0x2c, 0x32, 0x3a, 0xe5, 0xae, 0x9a, 0xe6, 0x97, 0xb6, 0xe5,
	0xbe, 0xaa, 0xe7, 0x8e, 0xaf, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0xb0, 0x01, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x42, 0x71, 0x92, 0x41, 0x6e, 0x2a, 0x6c, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0xe9, 0x97, 0xb4, 0xe9, 0x9a, 0x94, 0x30, 0x3a, 0xe6, 0x97, 0xa0, 0xef, 0xbc, 0x8c, 0x31,
	0x3a, 0xe6, 0xaf, 0x8f, 0xe5, 0xa4, 0xa9, 0x2c, 0x32, 0x3a, 0xe6, 0xaf, 0x8f, 0xe4, 0xb8, 0x89,
	0xe5, 0xa4, 0xa9, 0x2c, 0x33, 0x3a, 0xe6, 0xaf, 0x8f, 0xe5, 0x91, 0xa8, 0xe4, 0xb8, 0x80, 0xef,
	0xbc, 0x8c, 0x34, 0x3a, 0xe6, 0xaf, 0x8f, 0xe5, 0x91, 0xa8, 0xe4, 0xba, 0x8c, 0xef, 0xbc, 0x8c,
	0x35, 0x3a, 0xe6, 0xaf, 0x8f, 0xe5, 0x91, 0xa8, 0xe4, 0xb8, 0x89, 0xef, 0xbc, 0x8c, 0x36, 0x3a,
	0xe6, 0xaf, 0x8f, 0xe5, 0x91, 0xa8, 0xe5, 0x9b, 0x9b, 0xef, 0xbc, 0x8c, 0x37, 0x3a, 0xe6, 0xaf,
	0x8f, 0xe5, 0x91, 0xa8, 0xe4, 0xba, 0x94, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x97, 0xb6,
	0xe9, 0x97, 0xb4, 0xe8, 0x8a, 0x82, 0xe7, 0x82, 0xb9, 0xe9, 0x9b, 0x86, 0xe5, 0x90, 0x88, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a,
	0x19, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0xad, 0xa3,
	0xe6, 0x96, 0x87, 0x26, 0xe6, 0x91, 0x98, 0xe8, 0xa6, 0x81, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x35, 0x92, 0x41, 0x32,
	0x2a, 0x30, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x87,
	0x8f, 0xef, 0xbc, 0x88, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e,
	0xe6, 0x97, 0xb6, 0xe6, 0x97, 0xa0, 0xe9, 0x9c, 0x80, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0xef,
	0xbc, 0x89, 0x52, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0xa3,
	0x01, 0x0a, 0x09, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x67, 0x92, 0x41, 0x64, 0x2a, 0x62, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0x31, 0x3a, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x2c, 0x33, 0x3a, 0xe7,
	0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x2c, 0x34, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95,
	0x86, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe9, 0xa1, 0xb5, 0x2c, 0x35, 0x3a, 0xe6, 0x9b, 0x9d,
	0xe5, 0x85, 0x89, 0x2c, 0x36, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x2c, 0x38, 0x3a, 0xe5,
	0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0x2c, 0x39, 0x3a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95,
	0x86, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe9, 0xa1, 0xb5, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb7,
	0xb3, 0xe8, 0xbd, 0xac, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5,
	0xbb, 0xba, 0xe4, 0xba, 0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x30,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf,
	0xe7, 0x94, 0xa8, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x0c, 0x73,
	0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x9f, 0xad, 0xe4, 0xbf, 0xa1, 0xe6, 0xa8,
	0xa1, 0xe6, 0x9d, 0xbf, 0x52, 0x0b, 0x73, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x22, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x6b, 0x32, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0c, 0x92, 0x41, 0x09, 0x2a, 0x07, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x32, 0x52, 0x05,
	0x6c, 0x69, 0x6e, 0x6b, 0x32, 0x12, 0x41, 0x0a, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x82, 0xae, 0xe4, 0xbb, 0xb6, 0xe6,
	0xa8, 0xa1, 0xe6, 0x9d, 0xbf, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x22, 0x10, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x52,
	0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xdd, 0x01, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x69,
	0x64, 0x52, 0x07, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x03, 0x77, 0x61,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x48, 0x92, 0x41, 0x45, 0x2a, 0x43, 0xe5, 0x8f,
	0xac, 0xe5, 0x9b, 0x9e, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x2d, 0x31, 0xe5, 0x85, 0xa8, 0xe9,
	0x83, 0xa8, 0x2c, 0x30, 0x3a, 0x57, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0xe5, 0x8f, 0xac,
	0xe5, 0x9b, 0x9e, 0x2c, 0x31, 0x3a, 0xe7, 0x9f, 0xad, 0xe4, 0xbf, 0xa1, 0xe5, 0x8f, 0xac, 0xe5,
	0x9b, 0x9e, 0x2c, 0x32, 0x3a, 0xe9, 0x82, 0xae, 0xe4, 0xbb, 0xb6, 0xe5, 0x8f, 0xac, 0xe5, 0x9b,
	0x9e, 0x52, 0x03, 0x77, 0x61, 0x79, 0x12, 0x21, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe9, 0xbb, 0x98, 0xe8, 0xae,
	0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1,
	0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x48,
	0x0a, 0x07, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x52,
	0x07, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x2d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x09, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x37, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5, 0x8f,
	0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xb6, 0x03, 0x0a, 0x0a, 0x45, 0x78, 0x65, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x3b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0x92, 0x41, 0x28,
	0x2a, 0x26, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0x49, 0x44,
	0xef, 0xbc, 0x8c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7,
	0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x09,
	0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0x69, 0x64, 0x52, 0x08, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x07, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x89, 0xa7, 0xe8,
	0xa1, 0x8c, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x06, 0x65, 0x78, 0x65, 0x63, 0x41, 0x74,
	0x12, 0x34, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x8c, 0xba, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x41, 0x72, 0x65, 0x61, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8,
	0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x71, 0x0a, 0x03, 0x77, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x57, 0x61, 0x79, 0x42, 0x3f, 0x92, 0x41,
	0x3c, 0x2a, 0x3a, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x30,
	0x3a, 0x57, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x2c,
	0x31, 0x3a, 0xe7, 0x9f, 0xad, 0xe4, 0xbf, 0xa1, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x2c, 0x32,
	0x3a, 0xe9, 0x82, 0xae, 0xe4, 0xbb, 0xb6, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x52, 0x03, 0x77,
	0x61, 0x79, 0x12, 0x35, 0x0a, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xa6,
	0x86, 0xe7, 0x9b, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x87, 0x8f, 0x52, 0x0a, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x78, 0x65, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41,
	0x10, 0x2a, 0x0e, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49,
	0x44, 0x52, 0x08, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22,
	0xa4, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x78, 0x65,
	0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x58, 0x0a,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x52, 0x07,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x09, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x64, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e,
	0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x28, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4,
	0xe4, 0xba, 0xba, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x72, 0x22, 0x13, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0xfc, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54,
	0x6f, 0x75, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x08, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x69, 0x64, 0x52, 0x07, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x03, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x48, 0x92, 0x41, 0x45, 0x2a, 0x43, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe6,
	0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x2d, 0x31, 0xe5, 0x85, 0xa8, 0xe9, 0x83, 0xa8, 0x2c, 0x30, 0x3a,
	0x57, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x2c, 0x31,
	0x3a, 0xe7, 0x9f, 0xad, 0xe4, 0xbf, 0xa1, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x2c, 0x32, 0x3a,
	0xe9, 0x82, 0xae, 0xe4, 0xbb, 0xb6, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x52, 0x03, 0x77, 0x61,
	0x79, 0x12, 0x34, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8f, 0xac, 0xe5, 0x9b,
	0x9e, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x8c, 0xba, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x41, 0x72, 0x65, 0x61, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x22, 0x3d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x75,
	0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xa6,
	0x86, 0xe7, 0x9b, 0x96, 0xe7, 0x8e, 0x87, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0xc9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x09, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x08, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x21,
	0x92, 0x41, 0x1e, 0x2a, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0xe9, 0x87, 0x8f, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x3a, 0x02, 0x31,
	0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x2a, 0x25, 0xe5, 0x81,
	0x8f, 0xe7, 0xa7, 0xbb, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe7, 0xa9, 0xba, 0xef, 0xbc,
	0x8c, 0xe4, 0xbb, 0x8e, 0xe7, 0xac, 0xac, 0xe4, 0xb8, 0x80, 0xe9, 0xa1, 0xb5, 0xe5, 0xbc, 0x80,
	0xe5, 0xa7, 0x8b, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x99, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xbb, 0xe5, 0x83, 0x8f, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe4, 0xb8, 0x8b, 0xe4, 0xb8, 0x80, 0xe9, 0xa1, 0xb5, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x52,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x9d, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5,
	0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc,
	0x8c, 0x20, 0x31, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0xef, 0xbc, 0x8c, 0x32, 0xe7, 0xa6, 0x81,
	0xe7, 0x94, 0xa8, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92,
	0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xba, 0xba, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x10, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x4a, 0x0a, 0x0c, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x6e, 0x69,
	0x74, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x10, 0x03, 0x2a, 0x60, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x10, 0x01, 0x2a, 0x3f, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x01, 0x2a, 0x43, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x61,
	0x6c, 0x6c, 0x57, 0x61, 0x79, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x57,
	0x61, 0x79, 0x41, 0x70, 0x70, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x61, 0x6c,
	0x6c, 0x57, 0x61, 0x79, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x57, 0x61, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x10, 0x02, 0x2a, 0x5a, 0x0a,
	0x0e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x41, 0x74, 0x4f, 0x6e, 0x63, 0x65, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x6e, 0x63, 0x65, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x6f, 0x6f, 0x70, 0x10, 0x02, 0x2a, 0xb8, 0x01, 0x0a, 0x08, 0x4a, 0x75,
	0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4a,
	0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x65, 0x77, 0x73, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x10, 0x03, 0x12,
	0x18, 0x0a, 0x14, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x4a, 0x75, 0x6d,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x10, 0x05, 0x12,
	0x12, 0x0a, 0x0e, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x44,
	0x69, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x10, 0x08, 0x12, 0x19, 0x0a, 0x15, 0x4a, 0x75, 0x6d,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x10, 0x09, 0x2a, 0xdd, 0x01, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x79, 0x10, 0x01, 0x12,
	0x1b, 0x0a, 0x17, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x72,
	0x79, 0x54, 0x68, 0x72, 0x65, 0x65, 0x44, 0x61, 0x79, 0x73, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x72, 0x79, 0x4d, 0x6f,
	0x6e, 0x64, 0x61, 0x79, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x72, 0x79, 0x54, 0x75, 0x65, 0x73, 0x64, 0x61, 0x79, 0x10,
	0x04, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76,
	0x65, 0x72, 0x79, 0x57, 0x65, 0x64, 0x6e, 0x65, 0x73, 0x64, 0x61, 0x79, 0x10, 0x05, 0x12, 0x1a,
	0x0a, 0x16, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x72, 0x79,
	0x54, 0x68, 0x75, 0x72, 0x73, 0x64, 0x61, 0x79, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x72, 0x79, 0x46, 0x72, 0x69, 0x64,
	0x61, 0x79, 0x10, 0x07, 0x42, 0x18, 0x5a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_profile_v1_models_proto_rawDescOnce sync.Once
	file_user_profile_v1_models_proto_rawDescData = file_user_profile_v1_models_proto_rawDesc
)

func file_user_profile_v1_models_proto_rawDescGZIP() []byte {
	file_user_profile_v1_models_proto_rawDescOnce.Do(func() {
		file_user_profile_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_profile_v1_models_proto_rawDescData)
	})
	return file_user_profile_v1_models_proto_rawDescData
}

var file_user_profile_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_user_profile_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 54)
var file_user_profile_v1_models_proto_goTypes = []interface{}{
	(ReportAction)(0),                   // 0: api.user_profile.v1.ReportAction
	(UserProfileFieldCategory)(0),       // 1: api.user_profile.v1.UserProfileFieldCategory
	(LabelStatus)(0),                    // 2: api.user_profile.v1.LabelStatus
	(RecallWay)(0),                      // 3: api.user_profile.v1.RecallWay
	(RecallTimeType)(0),                 // 4: api.user_profile.v1.RecallTimeType
	(JumpType)(0),                       // 5: api.user_profile.v1.JumpType
	(TimeRange)(0),                      // 6: api.user_profile.v1.TimeRange
	(*PushIdReportRequest)(nil),         // 7: api.user_profile.v1.PushIdReportRequest
	(*PushIdReportReply)(nil),           // 8: api.user_profile.v1.PushIdReportReply
	(*CustomFilterRequest)(nil),         // 9: api.user_profile.v1.CustomFilterRequest
	(*CustomFilterItem)(nil),            // 10: api.user_profile.v1.CustomFilterItem
	(*CustomFilterReply)(nil),           // 11: api.user_profile.v1.CustomFilterReply
	(*CountByLabelRequest)(nil),         // 12: api.user_profile.v1.CountByLabelRequest
	(*Value)(nil),                       // 13: api.user_profile.v1.Value
	(*CountByLabelReply)(nil),           // 14: api.user_profile.v1.CountByLabelReply
	(*FilterLabelsRequest)(nil),         // 15: api.user_profile.v1.FilterLabelsRequest
	(*FilterLabel)(nil),                 // 16: api.user_profile.v1.FilterLabel
	(*FilterLabelsReply)(nil),           // 17: api.user_profile.v1.FilterLabelsReply
	(*FieldValue)(nil),                  // 18: api.user_profile.v1.FieldValue
	(*Field)(nil),                       // 19: api.user_profile.v1.Field
	(*UserProfileFieldRequest)(nil),     // 20: api.user_profile.v1.UserProfileFieldRequest
	(*UserProfileFieldReply)(nil),       // 21: api.user_profile.v1.UserProfileFieldReply
	(*GetByUserIdRequest)(nil),          // 22: api.user_profile.v1.GetByUserIdRequest
	(*UserProfile)(nil),                 // 23: api.user_profile.v1.UserProfile
	(*GetUserprofileRequest)(nil),       // 24: api.user_profile.v1.GetUserprofileRequest
	(*Label)(nil),                       // 25: api.user_profile.v1.Label
	(*AddLabelReply)(nil),               // 26: api.user_profile.v1.AddLabelReply
	(*GetLabelRequest)(nil),             // 27: api.user_profile.v1.GetLabelRequest
	(*UpdateLabelReply)(nil),            // 28: api.user_profile.v1.UpdateLabelReply
	(*UpdateLabelStatusRequest)(nil),    // 29: api.user_profile.v1.UpdateLabelStatusRequest
	(*UpdateLabelStatusReply)(nil),      // 30: api.user_profile.v1.UpdateLabelStatusReply
	(*DeleteLabelRequest)(nil),          // 31: api.user_profile.v1.DeleteLabelRequest
	(*DeleteLabelReply)(nil),            // 32: api.user_profile.v1.DeleteLabelReply
	(*ListLabelRequest)(nil),            // 33: api.user_profile.v1.ListLabelRequest
	(*ListLabelReply)(nil),              // 34: api.user_profile.v1.ListLabelReply
	(*FilterByLabelRequest)(nil),        // 35: api.user_profile.v1.FilterByLabelRequest
	(*FilterByLabelReply)(nil),          // 36: api.user_profile.v1.FilterByLabelReply
	(*EditorListRequest)(nil),           // 37: api.user_profile.v1.EditorListRequest
	(*EditorListReply)(nil),             // 38: api.user_profile.v1.EditorListReply
	(*LabelCountryRequest)(nil),         // 39: api.user_profile.v1.LabelCountryRequest
	(*LabelCountry)(nil),                // 40: api.user_profile.v1.LabelCountry
	(*LabelCountryReply)(nil),           // 41: api.user_profile.v1.LabelCountryReply
	(*LabelLanguageRequest)(nil),        // 42: api.user_profile.v1.LabelLanguageRequest
	(*LabelLanguage)(nil),               // 43: api.user_profile.v1.LabelLanguage
	(*LabelLanguageReply)(nil),          // 44: api.user_profile.v1.LabelLanguageReply
	(*Recall)(nil),                      // 45: api.user_profile.v1.Recall
	(*AddRecallReply)(nil),              // 46: api.user_profile.v1.AddRecallReply
	(*GetRecallsRequest)(nil),           // 47: api.user_profile.v1.GetRecallsRequest
	(*GetRecallsReply)(nil),             // 48: api.user_profile.v1.GetRecallsReply
	(*GetRecallRequest)(nil),            // 49: api.user_profile.v1.GetRecallRequest
	(*ExecRecord)(nil),                  // 50: api.user_profile.v1.ExecRecord
	(*GetRecallExecRecordsRequest)(nil), // 51: api.user_profile.v1.GetRecallExecRecordsRequest
	(*GetRecallExecRecordsReply)(nil),   // 52: api.user_profile.v1.GetRecallExecRecordsReply
	(*DeleteRecallRequest)(nil),         // 53: api.user_profile.v1.DeleteRecallRequest
	(*DeleteRecallReply)(nil),           // 54: api.user_profile.v1.DeleteRecallReply
	(*GetRecallTouchRequest)(nil),       // 55: api.user_profile.v1.GetRecallTouchRequest
	(*GetRecallTouchReply)(nil),         // 56: api.user_profile.v1.GetRecallTouchReply
	(*GetRecallCoverUsersRequest)(nil),  // 57: api.user_profile.v1.GetRecallCoverUsersRequest
	(*GetRecallCoverUsersReply)(nil),    // 58: api.user_profile.v1.GetRecallCoverUsersReply
	(*SetRecallRequest)(nil),            // 59: api.user_profile.v1.SetRecallRequest
	(*SetRecallReply)(nil),              // 60: api.user_profile.v1.SetRecallReply
}
var file_user_profile_v1_models_proto_depIdxs = []int32{
	0,  // 0: api.user_profile.v1.PushIdReportRequest.action:type_name -> api.user_profile.v1.ReportAction
	10, // 1: api.user_profile.v1.CustomFilterReply.items:type_name -> api.user_profile.v1.CustomFilterItem
	13, // 2: api.user_profile.v1.CountByLabelReply.countries:type_name -> api.user_profile.v1.Value
	13, // 3: api.user_profile.v1.CountByLabelReply.languages:type_name -> api.user_profile.v1.Value
	16, // 4: api.user_profile.v1.FilterLabelsReply.labels:type_name -> api.user_profile.v1.FilterLabel
	18, // 5: api.user_profile.v1.Field.values:type_name -> api.user_profile.v1.FieldValue
	1,  // 6: api.user_profile.v1.UserProfileFieldRequest.category:type_name -> api.user_profile.v1.UserProfileFieldCategory
	19, // 7: api.user_profile.v1.UserProfileFieldReply.fields:type_name -> api.user_profile.v1.Field
	2,  // 8: api.user_profile.v1.Label.status:type_name -> api.user_profile.v1.LabelStatus
	2,  // 9: api.user_profile.v1.UpdateLabelStatusRequest.status:type_name -> api.user_profile.v1.LabelStatus
	25, // 10: api.user_profile.v1.ListLabelReply.labels:type_name -> api.user_profile.v1.Label
	23, // 11: api.user_profile.v1.FilterByLabelReply.items:type_name -> api.user_profile.v1.UserProfile
	40, // 12: api.user_profile.v1.LabelCountryReply.countries:type_name -> api.user_profile.v1.LabelCountry
	43, // 13: api.user_profile.v1.LabelLanguageReply.languages:type_name -> api.user_profile.v1.LabelLanguage
	3,  // 14: api.user_profile.v1.Recall.way:type_name -> api.user_profile.v1.RecallWay
	4,  // 15: api.user_profile.v1.Recall.time_type:type_name -> api.user_profile.v1.RecallTimeType
	6,  // 16: api.user_profile.v1.Recall.time_range:type_name -> api.user_profile.v1.TimeRange
	5,  // 17: api.user_profile.v1.Recall.jump_type:type_name -> api.user_profile.v1.JumpType
	45, // 18: api.user_profile.v1.GetRecallsReply.recalls:type_name -> api.user_profile.v1.Recall
	3,  // 19: api.user_profile.v1.ExecRecord.way:type_name -> api.user_profile.v1.RecallWay
	50, // 20: api.user_profile.v1.GetRecallExecRecordsReply.records:type_name -> api.user_profile.v1.ExecRecord
	23, // 21: api.user_profile.v1.GetRecallCoverUsersReply.items:type_name -> api.user_profile.v1.UserProfile
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_user_profile_v1_models_proto_init() }
func file_user_profile_v1_models_proto_init() {
	if File_user_profile_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_profile_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushIdReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushIdReportReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomFilterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomFilterItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomFilterReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountByLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountByLabelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterLabelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterLabelsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProfileFieldRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProfileFieldReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetByUserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserprofileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLabelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLabelStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLabelStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLabelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLabelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterByLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterByLabelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditorListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditorListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelCountryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelCountry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelCountryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelLanguageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelLanguageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Recall); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRecallReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallExecRecordsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallExecRecordsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecallReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallTouchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallTouchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallCoverUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecallCoverUsersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_profile_v1_models_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRecallReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_profile_v1_models_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   54,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_profile_v1_models_proto_goTypes,
		DependencyIndexes: file_user_profile_v1_models_proto_depIdxs,
		EnumInfos:         file_user_profile_v1_models_proto_enumTypes,
		MessageInfos:      file_user_profile_v1_models_proto_msgTypes,
	}.Build()
	File_user_profile_v1_models_proto = out.File
	file_user_profile_v1_models_proto_rawDesc = nil
	file_user_profile_v1_models_proto_goTypes = nil
	file_user_profile_v1_models_proto_depIdxs = nil
}
