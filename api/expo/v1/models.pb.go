// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: expo/v1/models.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	_ "wiki_user_center/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 赞助等级
type SponsorLevel int32

const (
	SponsorLevel_SponsorLevel_NONE     SponsorLevel = 0 // 无赞助
	SponsorLevel_SponsorLevel_SILVER   SponsorLevel = 1 // 白银
	SponsorLevel_SponsorLevel_GOLD     SponsorLevel = 2 // 黄金
	SponsorLevel_SponsorLevel_PLATINUM SponsorLevel = 3 // 铂金
	SponsorLevel_SponsorLevel_DIAMOND  SponsorLevel = 4 // 钻石
	SponsorLevel_SponsorLevel_GOLOBAL  SponsorLevel = 5 // 全球
)

// Enum value maps for SponsorLevel.
var (
	SponsorLevel_name = map[int32]string{
		0: "SponsorLevel_NONE",
		1: "SponsorLevel_SILVER",
		2: "SponsorLevel_GOLD",
		3: "SponsorLevel_PLATINUM",
		4: "SponsorLevel_DIAMOND",
		5: "SponsorLevel_GOLOBAL",
	}
	SponsorLevel_value = map[string]int32{
		"SponsorLevel_NONE":     0,
		"SponsorLevel_SILVER":   1,
		"SponsorLevel_GOLD":     2,
		"SponsorLevel_PLATINUM": 3,
		"SponsorLevel_DIAMOND":  4,
		"SponsorLevel_GOLOBAL":  5,
	}
)

func (x SponsorLevel) Enum() *SponsorLevel {
	p := new(SponsorLevel)
	*p = x
	return p
}

func (x SponsorLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SponsorLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[0].Descriptor()
}

func (SponsorLevel) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[0]
}

func (x SponsorLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SponsorLevel.Descriptor instead.
func (SponsorLevel) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{0}
}

type Industry int32

const (
	Industry_INDUSTRY_UNKNOWN Industry = 0 // 未知
	Industry_INDUSTRY_STOCK   Industry = 1 // Stock
	Industry_INDUSTRY_FOREX   Industry = 2 // Forex
	Industry_INDUSTRY_CRYPTO  Industry = 3 // Crypto
	Industry_INDUSTRY_FINTECH Industry = 4 // Fintech
)

// Enum value maps for Industry.
var (
	Industry_name = map[int32]string{
		0: "INDUSTRY_UNKNOWN",
		1: "INDUSTRY_STOCK",
		2: "INDUSTRY_FOREX",
		3: "INDUSTRY_CRYPTO",
		4: "INDUSTRY_FINTECH",
	}
	Industry_value = map[string]int32{
		"INDUSTRY_UNKNOWN": 0,
		"INDUSTRY_STOCK":   1,
		"INDUSTRY_FOREX":   2,
		"INDUSTRY_CRYPTO":  3,
		"INDUSTRY_FINTECH": 4,
	}
)

func (x Industry) Enum() *Industry {
	p := new(Industry)
	*p = x
	return p
}

func (x Industry) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Industry) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[1].Descriptor()
}

func (Industry) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[1]
}

func (x Industry) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Industry.Descriptor instead.
func (Industry) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{1}
}

type Identity int32

const (
	Identity_IDENTITY_UNKNOWN          Identity = 0 // 未知
	Identity_IDENTITY_TRADER           Identity = 1 // 交易商
	Identity_IDENTITY_INVESTOR         Identity = 2 // 投资者
	Identity_IDENTITY_SERVICE_PROVIDER Identity = 3 // 服务商
	Identity_IDENTITY_KOL              Identity = 4 // KOL
)

// Enum value maps for Identity.
var (
	Identity_name = map[int32]string{
		0: "IDENTITY_UNKNOWN",
		1: "IDENTITY_TRADER",
		2: "IDENTITY_INVESTOR",
		3: "IDENTITY_SERVICE_PROVIDER",
		4: "IDENTITY_KOL",
	}
	Identity_value = map[string]int32{
		"IDENTITY_UNKNOWN":          0,
		"IDENTITY_TRADER":           1,
		"IDENTITY_INVESTOR":         2,
		"IDENTITY_SERVICE_PROVIDER": 3,
		"IDENTITY_KOL":              4,
	}
)

func (x Identity) Enum() *Identity {
	p := new(Identity)
	*p = x
	return p
}

func (x Identity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Identity) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[2].Descriptor()
}

func (Identity) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[2]
}

func (x Identity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Identity.Descriptor instead.
func (Identity) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{2}
}

type SubIdentity int32

const (
	SubIdentity_SUB_IDENTITY_UNKNOWN          SubIdentity = 0     // 未知
	SubIdentity_SUB_IDENTITY_FOREX            SubIdentity = 10001 // Forex Broker
	SubIdentity_SUB_IDENTITY_SERVICE_PROVIDER SubIdentity = 30001 // Service Provider（Press/Media/Cloud/Bank/Wealth management/CRM）
	SubIdentity_SUB_IDENTITY_FINTECH          SubIdentity = 30002 // Fintech（Payment/Al/Liquidity/Trading platform）
	SubIdentity_SUB_IDENTITY_CRYPTO           SubIdentity = 30003 // Crypto / Digital Assets
	SubIdentity_SUB_IDENTITY_SERVICE_IB       SubIdentity = 30004 // IB / Affiliate
	SubIdentity_SUB_IDENTITY_INVESTOR         SubIdentity = 30005 // Investor / VC
	SubIdentity_SUB_IDENTITY_TRADER           SubIdentity = 30006 // Trader
	SubIdentity_SUB_IDENTITY_OTHER            SubIdentity = 30007 // Other
	SubIdentity_SUB_IDENTITY_KOL              SubIdentity = 40001 // KOL
)

// Enum value maps for SubIdentity.
var (
	SubIdentity_name = map[int32]string{
		0:     "SUB_IDENTITY_UNKNOWN",
		10001: "SUB_IDENTITY_FOREX",
		30001: "SUB_IDENTITY_SERVICE_PROVIDER",
		30002: "SUB_IDENTITY_FINTECH",
		30003: "SUB_IDENTITY_CRYPTO",
		30004: "SUB_IDENTITY_SERVICE_IB",
		30005: "SUB_IDENTITY_INVESTOR",
		30006: "SUB_IDENTITY_TRADER",
		30007: "SUB_IDENTITY_OTHER",
		40001: "SUB_IDENTITY_KOL",
	}
	SubIdentity_value = map[string]int32{
		"SUB_IDENTITY_UNKNOWN":          0,
		"SUB_IDENTITY_FOREX":            10001,
		"SUB_IDENTITY_SERVICE_PROVIDER": 30001,
		"SUB_IDENTITY_FINTECH":          30002,
		"SUB_IDENTITY_CRYPTO":           30003,
		"SUB_IDENTITY_SERVICE_IB":       30004,
		"SUB_IDENTITY_INVESTOR":         30005,
		"SUB_IDENTITY_TRADER":           30006,
		"SUB_IDENTITY_OTHER":            30007,
		"SUB_IDENTITY_KOL":              40001,
	}
)

func (x SubIdentity) Enum() *SubIdentity {
	p := new(SubIdentity)
	*p = x
	return p
}

func (x SubIdentity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubIdentity) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[3].Descriptor()
}

func (SubIdentity) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[3]
}

func (x SubIdentity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubIdentity.Descriptor instead.
func (SubIdentity) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{3}
}

// 人脸矩形位置
type FaceRect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X      int32 `protobuf:"varint,1,opt,name=x,json=x,proto3" json:"x"`                // 左上角X坐标
	Y      int32 `protobuf:"varint,2,opt,name=y,json=y,proto3" json:"y"`                // 左上角Y坐标
	Width  int32 `protobuf:"varint,3,opt,name=width,json=width,proto3" json:"width"`    // 宽度
	Height int32 `protobuf:"varint,4,opt,name=height,json=height,proto3" json:"height"` // 高度
}

func (x *FaceRect) Reset() {
	*x = FaceRect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceRect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceRect) ProtoMessage() {}

func (x *FaceRect) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceRect.ProtoReflect.Descriptor instead.
func (*FaceRect) Descriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{0}
}

func (x *FaceRect) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *FaceRect) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *FaceRect) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *FaceRect) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

var File_expo_v1_models_proto protoreflect.FileDescriptor

var file_expo_v1_models_proto_rawDesc = []byte{
	0x0a, 0x14, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x54, 0x0a, 0x08, 0x46, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78,
	0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x2a, 0xa4, 0x01, 0x0a,
	0x0c, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x15, 0x0a,
	0x11, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x47, 0x4f,
	0x4c, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x49, 0x4e, 0x55, 0x4d, 0x10, 0x03, 0x12,
	0x18, 0x0a, 0x14, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f,
	0x44, 0x49, 0x41, 0x4d, 0x4f, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x70, 0x6f,
	0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x47, 0x4f, 0x4c, 0x4f, 0x42, 0x41,
	0x4c, 0x10, 0x05, 0x2a, 0x73, 0x0a, 0x08, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12,
	0x14, 0x0a, 0x10, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52,
	0x59, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x44,
	0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x10, 0x02, 0x12, 0x13, 0x0a,
	0x0f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f,
	0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x46,
	0x49, 0x4e, 0x54, 0x45, 0x43, 0x48, 0x10, 0x04, 0x2a, 0x7d, 0x0a, 0x08, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12,
	0x15, 0x0a, 0x11, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x56, 0x45,
	0x53, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49,
	0x44, 0x45, 0x52, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x5f, 0x4b, 0x4f, 0x4c, 0x10, 0x04, 0x2a, 0xa5, 0x02, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x55, 0x42, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x17, 0x0a, 0x12, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x10, 0x91, 0x4e, 0x12, 0x23, 0x0a, 0x1d, 0x53, 0x55,
	0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0xb1, 0xea, 0x01, 0x12,
	0x1a, 0x0a, 0x14, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x46, 0x49, 0x4e, 0x54, 0x45, 0x43, 0x48, 0x10, 0xb2, 0xea, 0x01, 0x12, 0x19, 0x0a, 0x13, 0x53,
	0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x59, 0x50,
	0x54, 0x4f, 0x10, 0xb3, 0xea, 0x01, 0x12, 0x1d, 0x0a, 0x17, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49,
	0x42, 0x10, 0xb4, 0xea, 0x01, 0x12, 0x1b, 0x0a, 0x15, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x10, 0xb5,
	0xea, 0x01, 0x12, 0x19, 0x0a, 0x13, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x52, 0x10, 0xb6, 0xea, 0x01, 0x12, 0x18, 0x0a,
	0x12, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x10, 0xb7, 0xea, 0x01, 0x12, 0x16, 0x0a, 0x10, 0x53, 0x55, 0x42, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4b, 0x4f, 0x4c, 0x10, 0xc1, 0xb8, 0x02, 0x42,
	0x10, 0x5a, 0x0e, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_expo_v1_models_proto_rawDescOnce sync.Once
	file_expo_v1_models_proto_rawDescData = file_expo_v1_models_proto_rawDesc
)

func file_expo_v1_models_proto_rawDescGZIP() []byte {
	file_expo_v1_models_proto_rawDescOnce.Do(func() {
		file_expo_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_expo_v1_models_proto_rawDescData)
	})
	return file_expo_v1_models_proto_rawDescData
}

var file_expo_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_expo_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_expo_v1_models_proto_goTypes = []interface{}{
	(SponsorLevel)(0), // 0: api.expo.v1.SponsorLevel
	(Industry)(0),     // 1: api.expo.v1.Industry
	(Identity)(0),     // 2: api.expo.v1.Identity
	(SubIdentity)(0),  // 3: api.expo.v1.SubIdentity
	(*FaceRect)(nil),  // 4: api.expo.v1.FaceRect
}
var file_expo_v1_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_expo_v1_models_proto_init() }
func file_expo_v1_models_proto_init() {
	if File_expo_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_expo_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceRect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_expo_v1_models_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_expo_v1_models_proto_goTypes,
		DependencyIndexes: file_expo_v1_models_proto_depIdxs,
		EnumInfos:         file_expo_v1_models_proto_enumTypes,
		MessageInfos:      file_expo_v1_models_proto_msgTypes,
	}.Build()
	File_expo_v1_models_proto = out.File
	file_expo_v1_models_proto_rawDesc = nil
	file_expo_v1_models_proto_goTypes = nil
	file_expo_v1_models_proto_depIdxs = nil
}
