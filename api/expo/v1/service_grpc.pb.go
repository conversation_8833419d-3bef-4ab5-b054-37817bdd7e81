// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: expo/v1/service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "wiki_user_center/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName                = "/api.expo.v1.Service/Healthy"
	Service_GetOpenExpo_FullMethodName            = "/api.expo.v1.Service/GetOpenExpo"
	Service_UserExpoRight_FullMethodName          = "/api.expo.v1.Service/UserExpoRight"
	Service_ExpoTab_FullMethodName                = "/api.expo.v1.Service/ExpoTab"
	Service_ExpoList_FullMethodName               = "/api.expo.v1.Service/ExpoList"
	Service_GetExpoDetail_FullMethodName          = "/api.expo.v1.Service/GetExpoDetail"
	Service_ExpoUserRegistration_FullMethodName   = "/api.expo.v1.Service/ExpoUserRegistration"
	Service_ExhibitorRegistration_FullMethodName  = "/api.expo.v1.Service/ExhibitorRegistration"
	Service_ExpoInteraction_FullMethodName        = "/api.expo.v1.Service/ExpoInteraction"
	Service_ExpoInteractionPreview_FullMethodName = "/api.expo.v1.Service/ExpoInteractionPreview"
	Service_PostExpoInteraction_FullMethodName    = "/api.expo.v1.Service/PostExpoInteraction"
	Service_ExpoSchedule_FullMethodName           = "/api.expo.v1.Service/ExpoSchedule"
	Service_ExpoGuide_FullMethodName              = "/api.expo.v1.Service/ExpoGuide"
	Service_ExpoPartner_FullMethodName            = "/api.expo.v1.Service/ExpoPartner"
	Service_ExpoTopic_FullMethodName              = "/api.expo.v1.Service/ExpoTopic"
	Service_GetSpeakerList_FullMethodName         = "/api.expo.v1.Service/GetSpeakerList"
	Service_GetSpeakerDetail_FullMethodName       = "/api.expo.v1.Service/GetSpeakerDetail"
	Service_GetAudienceTab_FullMethodName         = "/api.expo.v1.Service/GetAudienceTab"
	Service_GetAudienceList_FullMethodName        = "/api.expo.v1.Service/GetAudienceList"
	Service_ExhibitorList_FullMethodName          = "/api.expo.v1.Service/ExhibitorList"
	Service_ExhibitorEmployeeList_FullMethodName  = "/api.expo.v1.Service/ExhibitorEmployeeList"
	Service_GetExhibitorBoothList_FullMethodName  = "/api.expo.v1.Service/GetExhibitorBoothList"
	Service_TicketList_FullMethodName             = "/api.expo.v1.Service/TicketList"
	Service_GetTicketDetail_FullMethodName        = "/api.expo.v1.Service/GetTicketDetail"
	Service_FaceSearch_FullMethodName             = "/api.expo.v1.Service/FaceSearch"
	Service_GetLiveImages_FullMethodName          = "/api.expo.v1.Service/GetLiveImages"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// ===========================================================
	// =========================== 展会 ===========================
	// ===========================================================
	// 获取开屏展会
	GetOpenExpo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*OpenExpoReply, error)
	// 用户展会权益
	UserExpoRight(ctx context.Context, in *UserExpoRightRequest, opts ...grpc.CallOption) (*UserExpoRightReply, error)
	// 展会tab
	ExpoTab(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*ExpoTabReply, error)
	// 获取展会列表
	ExpoList(ctx context.Context, in *ExpoListRequest, opts ...grpc.CallOption) (*ExpoListReply, error)
	// 展会详情
	GetExpoDetail(ctx context.Context, in *ExpoDetailRequest, opts ...grpc.CallOption) (*ExpoDetail, error)
	// 展会用户报名
	ExpoUserRegistration(ctx context.Context, in *ExpoUserSignUpRequest, opts ...grpc.CallOption) (*ExpoUserSignUpReply, error)
	// 展会参展商报名
	ExhibitorRegistration(ctx context.Context, in *ExhibitorSignUpRequest, opts ...grpc.CallOption) (*ExhibitorSignUpReply, error)
	// 展会互动
	ExpoInteraction(ctx context.Context, in *ExpoInteractionRequest, opts ...grpc.CallOption) (*ExpoInteractionReply, error)
	// 展会互动预告短语
	ExpoInteractionPreview(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*ExpoInteractionPreviewReply, error)
	// 发表展会互动
	PostExpoInteraction(ctx context.Context, in *PostExpoInteractionRequest, opts ...grpc.CallOption) (*PostExpoInteractionReply, error)
	// 展会议程
	ExpoSchedule(ctx context.Context, in *ExpoScheduleRequest, opts ...grpc.CallOption) (*ExpoScheduleReply, error)
	// 展会指南
	ExpoGuide(ctx context.Context, in *ExpoGuideRequest, opts ...grpc.CallOption) (*ExpoGuideReply, error)
	// 获取伙伴
	ExpoPartner(ctx context.Context, in *ExpoPartnerRequest, opts ...grpc.CallOption) (*ExpoPartnerReply, error)
	// 展会话题
	ExpoTopic(ctx context.Context, in *ExpoTopicRequest, opts ...grpc.CallOption) (*ExpoTopicReply, error)
	// 演讲嘉宾列表
	GetSpeakerList(ctx context.Context, in *GetSpeakerListRequest, opts ...grpc.CallOption) (*GetSpeakerListReply, error)
	// 演讲嘉宾主页
	GetSpeakerDetail(ctx context.Context, in *GetSpeakerDetailRequest, opts ...grpc.CallOption) (*GetSpeakerDetailReply, error)
	// 观展用户tab
	GetAudienceTab(ctx context.Context, in *GetAudienceTabRequest, opts ...grpc.CallOption) (*GetAudienceTabReply, error)
	// 观展用户列表
	GetAudienceList(ctx context.Context, in *GetAudienceRequest, opts ...grpc.CallOption) (*GetAudienceReply, error)
	// 参展商列表
	ExhibitorList(ctx context.Context, in *GetExhibitorListRequest, opts ...grpc.CallOption) (*GetExhibitorListReply, error)
	// 参展商员工列表
	ExhibitorEmployeeList(ctx context.Context, in *GetExhibitorEmployeeListRequest, opts ...grpc.CallOption) (*GetExhibitorEmployeeListReply, error)
	// 展位列表
	GetExhibitorBoothList(ctx context.Context, in *GetExhibitorBoothListRequest, opts ...grpc.CallOption) (*GetExhibitorBoothListReply, error)
	// ===========================================================
	// =========================== 票夹 ===========================
	// ===========================================================
	// 票夹列表
	TicketList(ctx context.Context, in *GetTicketListRequest, opts ...grpc.CallOption) (*GetTicketListReply, error)
	// 票夹详情
	GetTicketDetail(ctx context.Context, in *GetTicketDetailRequest, opts ...grpc.CallOption) (*TicketDetail, error)
	// 用户人脸搜索接口
	FaceSearch(ctx context.Context, in *FaceSearchRequest, opts ...grpc.CallOption) (*FaceSearchReply, error)
	// 分页图片直播列表
	GetLiveImages(ctx context.Context, in *GetLiveImagesRequest, opts ...grpc.CallOption) (*GetLiveImagesReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOpenExpo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*OpenExpoReply, error) {
	out := new(OpenExpoReply)
	err := c.cc.Invoke(ctx, Service_GetOpenExpo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserExpoRight(ctx context.Context, in *UserExpoRightRequest, opts ...grpc.CallOption) (*UserExpoRightReply, error) {
	out := new(UserExpoRightReply)
	err := c.cc.Invoke(ctx, Service_UserExpoRight_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoTab(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*ExpoTabReply, error) {
	out := new(ExpoTabReply)
	err := c.cc.Invoke(ctx, Service_ExpoTab_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoList(ctx context.Context, in *ExpoListRequest, opts ...grpc.CallOption) (*ExpoListReply, error) {
	out := new(ExpoListReply)
	err := c.cc.Invoke(ctx, Service_ExpoList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetExpoDetail(ctx context.Context, in *ExpoDetailRequest, opts ...grpc.CallOption) (*ExpoDetail, error) {
	out := new(ExpoDetail)
	err := c.cc.Invoke(ctx, Service_GetExpoDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoUserRegistration(ctx context.Context, in *ExpoUserSignUpRequest, opts ...grpc.CallOption) (*ExpoUserSignUpReply, error) {
	out := new(ExpoUserSignUpReply)
	err := c.cc.Invoke(ctx, Service_ExpoUserRegistration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExhibitorRegistration(ctx context.Context, in *ExhibitorSignUpRequest, opts ...grpc.CallOption) (*ExhibitorSignUpReply, error) {
	out := new(ExhibitorSignUpReply)
	err := c.cc.Invoke(ctx, Service_ExhibitorRegistration_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoInteraction(ctx context.Context, in *ExpoInteractionRequest, opts ...grpc.CallOption) (*ExpoInteractionReply, error) {
	out := new(ExpoInteractionReply)
	err := c.cc.Invoke(ctx, Service_ExpoInteraction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoInteractionPreview(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*ExpoInteractionPreviewReply, error) {
	out := new(ExpoInteractionPreviewReply)
	err := c.cc.Invoke(ctx, Service_ExpoInteractionPreview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) PostExpoInteraction(ctx context.Context, in *PostExpoInteractionRequest, opts ...grpc.CallOption) (*PostExpoInteractionReply, error) {
	out := new(PostExpoInteractionReply)
	err := c.cc.Invoke(ctx, Service_PostExpoInteraction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoSchedule(ctx context.Context, in *ExpoScheduleRequest, opts ...grpc.CallOption) (*ExpoScheduleReply, error) {
	out := new(ExpoScheduleReply)
	err := c.cc.Invoke(ctx, Service_ExpoSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoGuide(ctx context.Context, in *ExpoGuideRequest, opts ...grpc.CallOption) (*ExpoGuideReply, error) {
	out := new(ExpoGuideReply)
	err := c.cc.Invoke(ctx, Service_ExpoGuide_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoPartner(ctx context.Context, in *ExpoPartnerRequest, opts ...grpc.CallOption) (*ExpoPartnerReply, error) {
	out := new(ExpoPartnerReply)
	err := c.cc.Invoke(ctx, Service_ExpoPartner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpoTopic(ctx context.Context, in *ExpoTopicRequest, opts ...grpc.CallOption) (*ExpoTopicReply, error) {
	out := new(ExpoTopicReply)
	err := c.cc.Invoke(ctx, Service_ExpoTopic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetSpeakerList(ctx context.Context, in *GetSpeakerListRequest, opts ...grpc.CallOption) (*GetSpeakerListReply, error) {
	out := new(GetSpeakerListReply)
	err := c.cc.Invoke(ctx, Service_GetSpeakerList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetSpeakerDetail(ctx context.Context, in *GetSpeakerDetailRequest, opts ...grpc.CallOption) (*GetSpeakerDetailReply, error) {
	out := new(GetSpeakerDetailReply)
	err := c.cc.Invoke(ctx, Service_GetSpeakerDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAudienceTab(ctx context.Context, in *GetAudienceTabRequest, opts ...grpc.CallOption) (*GetAudienceTabReply, error) {
	out := new(GetAudienceTabReply)
	err := c.cc.Invoke(ctx, Service_GetAudienceTab_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAudienceList(ctx context.Context, in *GetAudienceRequest, opts ...grpc.CallOption) (*GetAudienceReply, error) {
	out := new(GetAudienceReply)
	err := c.cc.Invoke(ctx, Service_GetAudienceList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExhibitorList(ctx context.Context, in *GetExhibitorListRequest, opts ...grpc.CallOption) (*GetExhibitorListReply, error) {
	out := new(GetExhibitorListReply)
	err := c.cc.Invoke(ctx, Service_ExhibitorList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExhibitorEmployeeList(ctx context.Context, in *GetExhibitorEmployeeListRequest, opts ...grpc.CallOption) (*GetExhibitorEmployeeListReply, error) {
	out := new(GetExhibitorEmployeeListReply)
	err := c.cc.Invoke(ctx, Service_ExhibitorEmployeeList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetExhibitorBoothList(ctx context.Context, in *GetExhibitorBoothListRequest, opts ...grpc.CallOption) (*GetExhibitorBoothListReply, error) {
	out := new(GetExhibitorBoothListReply)
	err := c.cc.Invoke(ctx, Service_GetExhibitorBoothList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TicketList(ctx context.Context, in *GetTicketListRequest, opts ...grpc.CallOption) (*GetTicketListReply, error) {
	out := new(GetTicketListReply)
	err := c.cc.Invoke(ctx, Service_TicketList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetTicketDetail(ctx context.Context, in *GetTicketDetailRequest, opts ...grpc.CallOption) (*TicketDetail, error) {
	out := new(TicketDetail)
	err := c.cc.Invoke(ctx, Service_GetTicketDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FaceSearch(ctx context.Context, in *FaceSearchRequest, opts ...grpc.CallOption) (*FaceSearchReply, error) {
	out := new(FaceSearchReply)
	err := c.cc.Invoke(ctx, Service_FaceSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLiveImages(ctx context.Context, in *GetLiveImagesRequest, opts ...grpc.CallOption) (*GetLiveImagesReply, error) {
	out := new(GetLiveImagesReply)
	err := c.cc.Invoke(ctx, Service_GetLiveImages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// ===========================================================
	// =========================== 展会 ===========================
	// ===========================================================
	// 获取开屏展会
	GetOpenExpo(context.Context, *common.EmptyRequest) (*OpenExpoReply, error)
	// 用户展会权益
	UserExpoRight(context.Context, *UserExpoRightRequest) (*UserExpoRightReply, error)
	// 展会tab
	ExpoTab(context.Context, *common.EmptyRequest) (*ExpoTabReply, error)
	// 获取展会列表
	ExpoList(context.Context, *ExpoListRequest) (*ExpoListReply, error)
	// 展会详情
	GetExpoDetail(context.Context, *ExpoDetailRequest) (*ExpoDetail, error)
	// 展会用户报名
	ExpoUserRegistration(context.Context, *ExpoUserSignUpRequest) (*ExpoUserSignUpReply, error)
	// 展会参展商报名
	ExhibitorRegistration(context.Context, *ExhibitorSignUpRequest) (*ExhibitorSignUpReply, error)
	// 展会互动
	ExpoInteraction(context.Context, *ExpoInteractionRequest) (*ExpoInteractionReply, error)
	// 展会互动预告短语
	ExpoInteractionPreview(context.Context, *common.EmptyRequest) (*ExpoInteractionPreviewReply, error)
	// 发表展会互动
	PostExpoInteraction(context.Context, *PostExpoInteractionRequest) (*PostExpoInteractionReply, error)
	// 展会议程
	ExpoSchedule(context.Context, *ExpoScheduleRequest) (*ExpoScheduleReply, error)
	// 展会指南
	ExpoGuide(context.Context, *ExpoGuideRequest) (*ExpoGuideReply, error)
	// 获取伙伴
	ExpoPartner(context.Context, *ExpoPartnerRequest) (*ExpoPartnerReply, error)
	// 展会话题
	ExpoTopic(context.Context, *ExpoTopicRequest) (*ExpoTopicReply, error)
	// 演讲嘉宾列表
	GetSpeakerList(context.Context, *GetSpeakerListRequest) (*GetSpeakerListReply, error)
	// 演讲嘉宾主页
	GetSpeakerDetail(context.Context, *GetSpeakerDetailRequest) (*GetSpeakerDetailReply, error)
	// 观展用户tab
	GetAudienceTab(context.Context, *GetAudienceTabRequest) (*GetAudienceTabReply, error)
	// 观展用户列表
	GetAudienceList(context.Context, *GetAudienceRequest) (*GetAudienceReply, error)
	// 参展商列表
	ExhibitorList(context.Context, *GetExhibitorListRequest) (*GetExhibitorListReply, error)
	// 参展商员工列表
	ExhibitorEmployeeList(context.Context, *GetExhibitorEmployeeListRequest) (*GetExhibitorEmployeeListReply, error)
	// 展位列表
	GetExhibitorBoothList(context.Context, *GetExhibitorBoothListRequest) (*GetExhibitorBoothListReply, error)
	// ===========================================================
	// =========================== 票夹 ===========================
	// ===========================================================
	// 票夹列表
	TicketList(context.Context, *GetTicketListRequest) (*GetTicketListReply, error)
	// 票夹详情
	GetTicketDetail(context.Context, *GetTicketDetailRequest) (*TicketDetail, error)
	// 用户人脸搜索接口
	FaceSearch(context.Context, *FaceSearchRequest) (*FaceSearchReply, error)
	// 分页图片直播列表
	GetLiveImages(context.Context, *GetLiveImagesRequest) (*GetLiveImagesReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) GetOpenExpo(context.Context, *common.EmptyRequest) (*OpenExpoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpenExpo not implemented")
}
func (UnimplementedServiceServer) UserExpoRight(context.Context, *UserExpoRightRequest) (*UserExpoRightReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserExpoRight not implemented")
}
func (UnimplementedServiceServer) ExpoTab(context.Context, *common.EmptyRequest) (*ExpoTabReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoTab not implemented")
}
func (UnimplementedServiceServer) ExpoList(context.Context, *ExpoListRequest) (*ExpoListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoList not implemented")
}
func (UnimplementedServiceServer) GetExpoDetail(context.Context, *ExpoDetailRequest) (*ExpoDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoDetail not implemented")
}
func (UnimplementedServiceServer) ExpoUserRegistration(context.Context, *ExpoUserSignUpRequest) (*ExpoUserSignUpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoUserRegistration not implemented")
}
func (UnimplementedServiceServer) ExhibitorRegistration(context.Context, *ExhibitorSignUpRequest) (*ExhibitorSignUpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExhibitorRegistration not implemented")
}
func (UnimplementedServiceServer) ExpoInteraction(context.Context, *ExpoInteractionRequest) (*ExpoInteractionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoInteraction not implemented")
}
func (UnimplementedServiceServer) ExpoInteractionPreview(context.Context, *common.EmptyRequest) (*ExpoInteractionPreviewReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoInteractionPreview not implemented")
}
func (UnimplementedServiceServer) PostExpoInteraction(context.Context, *PostExpoInteractionRequest) (*PostExpoInteractionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostExpoInteraction not implemented")
}
func (UnimplementedServiceServer) ExpoSchedule(context.Context, *ExpoScheduleRequest) (*ExpoScheduleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoSchedule not implemented")
}
func (UnimplementedServiceServer) ExpoGuide(context.Context, *ExpoGuideRequest) (*ExpoGuideReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoGuide not implemented")
}
func (UnimplementedServiceServer) ExpoPartner(context.Context, *ExpoPartnerRequest) (*ExpoPartnerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoPartner not implemented")
}
func (UnimplementedServiceServer) ExpoTopic(context.Context, *ExpoTopicRequest) (*ExpoTopicReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpoTopic not implemented")
}
func (UnimplementedServiceServer) GetSpeakerList(context.Context, *GetSpeakerListRequest) (*GetSpeakerListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpeakerList not implemented")
}
func (UnimplementedServiceServer) GetSpeakerDetail(context.Context, *GetSpeakerDetailRequest) (*GetSpeakerDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpeakerDetail not implemented")
}
func (UnimplementedServiceServer) GetAudienceTab(context.Context, *GetAudienceTabRequest) (*GetAudienceTabReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAudienceTab not implemented")
}
func (UnimplementedServiceServer) GetAudienceList(context.Context, *GetAudienceRequest) (*GetAudienceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAudienceList not implemented")
}
func (UnimplementedServiceServer) ExhibitorList(context.Context, *GetExhibitorListRequest) (*GetExhibitorListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExhibitorList not implemented")
}
func (UnimplementedServiceServer) ExhibitorEmployeeList(context.Context, *GetExhibitorEmployeeListRequest) (*GetExhibitorEmployeeListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExhibitorEmployeeList not implemented")
}
func (UnimplementedServiceServer) GetExhibitorBoothList(context.Context, *GetExhibitorBoothListRequest) (*GetExhibitorBoothListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExhibitorBoothList not implemented")
}
func (UnimplementedServiceServer) TicketList(context.Context, *GetTicketListRequest) (*GetTicketListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketList not implemented")
}
func (UnimplementedServiceServer) GetTicketDetail(context.Context, *GetTicketDetailRequest) (*TicketDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicketDetail not implemented")
}
func (UnimplementedServiceServer) FaceSearch(context.Context, *FaceSearchRequest) (*FaceSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceSearch not implemented")
}
func (UnimplementedServiceServer) GetLiveImages(context.Context, *GetLiveImagesRequest) (*GetLiveImagesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLiveImages not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOpenExpo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOpenExpo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOpenExpo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOpenExpo(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserExpoRight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserExpoRightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserExpoRight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserExpoRight_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserExpoRight(ctx, req.(*UserExpoRightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoTab_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoTab(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoList(ctx, req.(*ExpoListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetExpoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetExpoDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetExpoDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetExpoDetail(ctx, req.(*ExpoDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoUserRegistration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoUserSignUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoUserRegistration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoUserRegistration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoUserRegistration(ctx, req.(*ExpoUserSignUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExhibitorRegistration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExhibitorSignUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExhibitorRegistration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExhibitorRegistration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExhibitorRegistration(ctx, req.(*ExhibitorSignUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoInteraction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoInteractionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoInteraction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoInteraction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoInteraction(ctx, req.(*ExpoInteractionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoInteractionPreview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoInteractionPreview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoInteractionPreview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoInteractionPreview(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_PostExpoInteraction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostExpoInteractionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).PostExpoInteraction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_PostExpoInteraction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).PostExpoInteraction(ctx, req.(*PostExpoInteractionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoSchedule(ctx, req.(*ExpoScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoGuideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoGuide(ctx, req.(*ExpoGuideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoPartnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoPartner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoPartner(ctx, req.(*ExpoPartnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpoTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpoTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpoTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpoTopic(ctx, req.(*ExpoTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetSpeakerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpeakerListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetSpeakerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetSpeakerList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetSpeakerList(ctx, req.(*GetSpeakerListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetSpeakerDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpeakerDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetSpeakerDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetSpeakerDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetSpeakerDetail(ctx, req.(*GetSpeakerDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAudienceTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAudienceTabRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAudienceTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAudienceTab_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAudienceTab(ctx, req.(*GetAudienceTabRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAudienceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAudienceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAudienceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAudienceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAudienceList(ctx, req.(*GetAudienceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExhibitorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExhibitorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExhibitorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExhibitorList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExhibitorList(ctx, req.(*GetExhibitorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExhibitorEmployeeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExhibitorEmployeeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExhibitorEmployeeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExhibitorEmployeeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExhibitorEmployeeList(ctx, req.(*GetExhibitorEmployeeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetExhibitorBoothList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExhibitorBoothListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetExhibitorBoothList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetExhibitorBoothList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetExhibitorBoothList(ctx, req.(*GetExhibitorBoothListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TicketList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TicketList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TicketList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TicketList(ctx, req.(*GetTicketListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetTicketDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetTicketDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetTicketDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetTicketDetail(ctx, req.(*GetTicketDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FaceSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FaceSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FaceSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FaceSearch(ctx, req.(*FaceSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLiveImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLiveImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLiveImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLiveImages(ctx, req.(*GetLiveImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.expo.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "GetOpenExpo",
			Handler:    _Service_GetOpenExpo_Handler,
		},
		{
			MethodName: "UserExpoRight",
			Handler:    _Service_UserExpoRight_Handler,
		},
		{
			MethodName: "ExpoTab",
			Handler:    _Service_ExpoTab_Handler,
		},
		{
			MethodName: "ExpoList",
			Handler:    _Service_ExpoList_Handler,
		},
		{
			MethodName: "GetExpoDetail",
			Handler:    _Service_GetExpoDetail_Handler,
		},
		{
			MethodName: "ExpoUserRegistration",
			Handler:    _Service_ExpoUserRegistration_Handler,
		},
		{
			MethodName: "ExhibitorRegistration",
			Handler:    _Service_ExhibitorRegistration_Handler,
		},
		{
			MethodName: "ExpoInteraction",
			Handler:    _Service_ExpoInteraction_Handler,
		},
		{
			MethodName: "ExpoInteractionPreview",
			Handler:    _Service_ExpoInteractionPreview_Handler,
		},
		{
			MethodName: "PostExpoInteraction",
			Handler:    _Service_PostExpoInteraction_Handler,
		},
		{
			MethodName: "ExpoSchedule",
			Handler:    _Service_ExpoSchedule_Handler,
		},
		{
			MethodName: "ExpoGuide",
			Handler:    _Service_ExpoGuide_Handler,
		},
		{
			MethodName: "ExpoPartner",
			Handler:    _Service_ExpoPartner_Handler,
		},
		{
			MethodName: "ExpoTopic",
			Handler:    _Service_ExpoTopic_Handler,
		},
		{
			MethodName: "GetSpeakerList",
			Handler:    _Service_GetSpeakerList_Handler,
		},
		{
			MethodName: "GetSpeakerDetail",
			Handler:    _Service_GetSpeakerDetail_Handler,
		},
		{
			MethodName: "GetAudienceTab",
			Handler:    _Service_GetAudienceTab_Handler,
		},
		{
			MethodName: "GetAudienceList",
			Handler:    _Service_GetAudienceList_Handler,
		},
		{
			MethodName: "ExhibitorList",
			Handler:    _Service_ExhibitorList_Handler,
		},
		{
			MethodName: "ExhibitorEmployeeList",
			Handler:    _Service_ExhibitorEmployeeList_Handler,
		},
		{
			MethodName: "GetExhibitorBoothList",
			Handler:    _Service_GetExhibitorBoothList_Handler,
		},
		{
			MethodName: "TicketList",
			Handler:    _Service_TicketList_Handler,
		},
		{
			MethodName: "GetTicketDetail",
			Handler:    _Service_GetTicketDetail_Handler,
		},
		{
			MethodName: "FaceSearch",
			Handler:    _Service_FaceSearch_Handler,
		},
		{
			MethodName: "GetLiveImages",
			Handler:    _Service_GetLiveImages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "expo/v1/service.proto",
}
