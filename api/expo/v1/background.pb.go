// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: expo/v1/background.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "wiki_user_center/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ===========================================================
// =========================== 展会会场 =======================
// ===========================================================
type ExpoHallType int32

const (
	ExpoHallType_ExpoHallType_MAIN ExpoHallType = 0 // 主会场
	ExpoHallType_ExpoHallType_SUB  ExpoHallType = 1 // 分会场
)

// Enum value maps for ExpoHallType.
var (
	ExpoHallType_name = map[int32]string{
		0: "ExpoHallType_MAIN",
		1: "ExpoHallType_SUB",
	}
	ExpoHallType_value = map[string]int32{
		"ExpoHallType_MAIN": 0,
		"ExpoHallType_SUB":  1,
	}
)

func (x ExpoHallType) Enum() *ExpoHallType {
	p := new(ExpoHallType)
	*p = x
	return p
}

func (x ExpoHallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpoHallType) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_background_proto_enumTypes[0].Descriptor()
}

func (ExpoHallType) Type() protoreflect.EnumType {
	return &file_expo_v1_background_proto_enumTypes[0]
}

func (x ExpoHallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpoHallType.Descriptor instead.
func (ExpoHallType) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{0}
}

type ScheduleType int32

const (
	ScheduleType_ScheduleType_PERSONAL ScheduleType = 0 // 个人演讲
)

// Enum value maps for ScheduleType.
var (
	ScheduleType_name = map[int32]string{
		0: "ScheduleType_PERSONAL",
	}
	ScheduleType_value = map[string]int32{
		"ScheduleType_PERSONAL": 0,
	}
)

func (x ScheduleType) Enum() *ScheduleType {
	p := new(ScheduleType)
	*p = x
	return p
}

func (x ScheduleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScheduleType) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_background_proto_enumTypes[1].Descriptor()
}

func (ScheduleType) Type() protoreflect.EnumType {
	return &file_expo_v1_background_proto_enumTypes[1]
}

func (x ScheduleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScheduleType.Descriptor instead.
func (ScheduleType) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{1}
}

type ExpoReviewType int32

const (
	ExpoReviewType_REVIEW_TYPE_VIDEO   ExpoReviewType = 0 // 视频
	ExpoReviewType_REVIEW_TYPE_PICTURE ExpoReviewType = 1 // 图片
)

// Enum value maps for ExpoReviewType.
var (
	ExpoReviewType_name = map[int32]string{
		0: "REVIEW_TYPE_VIDEO",
		1: "REVIEW_TYPE_PICTURE",
	}
	ExpoReviewType_value = map[string]int32{
		"REVIEW_TYPE_VIDEO":   0,
		"REVIEW_TYPE_PICTURE": 1,
	}
)

func (x ExpoReviewType) Enum() *ExpoReviewType {
	p := new(ExpoReviewType)
	*p = x
	return p
}

func (x ExpoReviewType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpoReviewType) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_background_proto_enumTypes[2].Descriptor()
}

func (ExpoReviewType) Type() protoreflect.EnumType {
	return &file_expo_v1_background_proto_enumTypes[2]
}

func (x ExpoReviewType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpoReviewType.Descriptor instead.
func (ExpoReviewType) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{2}
}

// 同步状态枚举
type SyncStatus int32

const (
	SyncStatus_SYNC_STATUS_UNKNOWN     SyncStatus = 0 // 未知状态
	SyncStatus_SYNC_STATUS_NOT_STARTED SyncStatus = 1 // 未开始
	SyncStatus_SYNC_STATUS_RUNNING     SyncStatus = 2 // 运行中
	SyncStatus_SYNC_STATUS_COMPLETED   SyncStatus = 3 // 已完成
	SyncStatus_SYNC_STATUS_FAILED      SyncStatus = 4 // 失败
	SyncStatus_SYNC_STATUS_CANCELLED   SyncStatus = 5 // 已取消
)

// Enum value maps for SyncStatus.
var (
	SyncStatus_name = map[int32]string{
		0: "SYNC_STATUS_UNKNOWN",
		1: "SYNC_STATUS_NOT_STARTED",
		2: "SYNC_STATUS_RUNNING",
		3: "SYNC_STATUS_COMPLETED",
		4: "SYNC_STATUS_FAILED",
		5: "SYNC_STATUS_CANCELLED",
	}
	SyncStatus_value = map[string]int32{
		"SYNC_STATUS_UNKNOWN":     0,
		"SYNC_STATUS_NOT_STARTED": 1,
		"SYNC_STATUS_RUNNING":     2,
		"SYNC_STATUS_COMPLETED":   3,
		"SYNC_STATUS_FAILED":      4,
		"SYNC_STATUS_CANCELLED":   5,
	}
)

func (x SyncStatus) Enum() *SyncStatus {
	p := new(SyncStatus)
	*p = x
	return p
}

func (x SyncStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SyncStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_background_proto_enumTypes[3].Descriptor()
}

func (SyncStatus) Type() protoreflect.EnumType {
	return &file_expo_v1_background_proto_enumTypes[3]
}

func (x SyncStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SyncStatus.Descriptor instead.
func (SyncStatus) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{3}
}

// 图片同步项状态
type ImageSyncStatus int32

const (
	ImageSyncStatus_IMAGE_SYNC_STATUS_UNKNOWN    ImageSyncStatus = 0 // 未知状态
	ImageSyncStatus_IMAGE_SYNC_STATUS_PENDING    ImageSyncStatus = 1 // 待处理
	ImageSyncStatus_IMAGE_SYNC_STATUS_PROCESSING ImageSyncStatus = 2 // 处理中
	ImageSyncStatus_IMAGE_SYNC_STATUS_SUCCESS    ImageSyncStatus = 3 // 成功
	ImageSyncStatus_IMAGE_SYNC_STATUS_FAILED     ImageSyncStatus = 4 // 失败
	ImageSyncStatus_IMAGE_SYNC_STATUS_SKIPPED    ImageSyncStatus = 5 // 跳过
	ImageSyncStatus_IMAGE_SYNC_STATUS_DELETED    ImageSyncStatus = 6 // 已删除
)

// Enum value maps for ImageSyncStatus.
var (
	ImageSyncStatus_name = map[int32]string{
		0: "IMAGE_SYNC_STATUS_UNKNOWN",
		1: "IMAGE_SYNC_STATUS_PENDING",
		2: "IMAGE_SYNC_STATUS_PROCESSING",
		3: "IMAGE_SYNC_STATUS_SUCCESS",
		4: "IMAGE_SYNC_STATUS_FAILED",
		5: "IMAGE_SYNC_STATUS_SKIPPED",
		6: "IMAGE_SYNC_STATUS_DELETED",
	}
	ImageSyncStatus_value = map[string]int32{
		"IMAGE_SYNC_STATUS_UNKNOWN":    0,
		"IMAGE_SYNC_STATUS_PENDING":    1,
		"IMAGE_SYNC_STATUS_PROCESSING": 2,
		"IMAGE_SYNC_STATUS_SUCCESS":    3,
		"IMAGE_SYNC_STATUS_FAILED":     4,
		"IMAGE_SYNC_STATUS_SKIPPED":    5,
		"IMAGE_SYNC_STATUS_DELETED":    6,
	}
)

func (x ImageSyncStatus) Enum() *ImageSyncStatus {
	p := new(ImageSyncStatus)
	*p = x
	return p
}

func (x ImageSyncStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImageSyncStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_background_proto_enumTypes[4].Descriptor()
}

func (ImageSyncStatus) Type() protoreflect.EnumType {
	return &file_expo_v1_background_proto_enumTypes[4]
}

func (x ImageSyncStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImageSyncStatus.Descriptor instead.
func (ImageSyncStatus) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{4}
}

// 同步操作类型
type SyncAction int32

const (
	SyncAction_SYNC_ACTION_UNKNOWN SyncAction = 0 // 未知操作
	SyncAction_SYNC_ACTION_ADD     SyncAction = 1 // 新增
	SyncAction_SYNC_ACTION_DELETE  SyncAction = 2 // 删除
	SyncAction_SYNC_ACTION_SKIP    SyncAction = 3 // 跳过
)

// Enum value maps for SyncAction.
var (
	SyncAction_name = map[int32]string{
		0: "SYNC_ACTION_UNKNOWN",
		1: "SYNC_ACTION_ADD",
		2: "SYNC_ACTION_DELETE",
		3: "SYNC_ACTION_SKIP",
	}
	SyncAction_value = map[string]int32{
		"SYNC_ACTION_UNKNOWN": 0,
		"SYNC_ACTION_ADD":     1,
		"SYNC_ACTION_DELETE":  2,
		"SYNC_ACTION_SKIP":    3,
	}
)

func (x SyncAction) Enum() *SyncAction {
	p := new(SyncAction)
	*p = x
	return p
}

func (x SyncAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SyncAction) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_background_proto_enumTypes[5].Descriptor()
}

func (SyncAction) Type() protoreflect.EnumType {
	return &file_expo_v1_background_proto_enumTypes[5]
}

func (x SyncAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SyncAction.Descriptor instead.
func (SyncAction) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{5}
}

// ===========================================================
// =========================== 嘉宾 ===========================
// ===========================================================
type GuestLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,json=description,proto3" json:"description"`
	Label       string `protobuf:"bytes,2,opt,name=label,json=label,proto3" json:"label"`
}

func (x *GuestLanguage) Reset() {
	*x = GuestLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GuestLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuestLanguage) ProtoMessage() {}

func (x *GuestLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuestLanguage.ProtoReflect.Descriptor instead.
func (*GuestLanguage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{0}
}

func (x *GuestLanguage) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GuestLanguage) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

type Guest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64                     `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Avatar        string                    `protobuf:"bytes,2,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Name          string                    `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	WikiNumber    string                    `protobuf:"bytes,4,opt,name=wiki_number,json=wikiNumber,proto3" json:"wiki_number"`
	ExpoIds       []int64                   `protobuf:"varint,5,rep,packed,name=expo_ids,json=expoIds,proto3" json:"expo_ids"`
	PhoneAreaCode string                    `protobuf:"bytes,6,opt,name=phone_area_code,json=phoneAreaCode,proto3" json:"phone_area_code"`
	Phone         string                    `protobuf:"bytes,7,opt,name=phone,json=phone,proto3" json:"phone"`
	Email         string                    `protobuf:"bytes,8,opt,name=email,json=email,proto3" json:"email"`
	WhatsApp      string                    `protobuf:"bytes,9,opt,name=whats_app,json=whatsApp,proto3" json:"whats_app"`
	Wechat        string                    `protobuf:"bytes,10,opt,name=wechat,json=wechat,proto3" json:"wechat"`
	Facebook      string                    `protobuf:"bytes,11,opt,name=facebook,json=facebook,proto3" json:"facebook"`
	Twitter       string                    `protobuf:"bytes,12,opt,name=twitter,json=twitter,proto3" json:"twitter"`
	Linkedin      string                    `protobuf:"bytes,13,opt,name=linkedin,json=linkedin,proto3" json:"linkedin"`
	Instagram     string                    `protobuf:"bytes,14,opt,name=instagram,json=instagram,proto3" json:"instagram"`
	Telegram      string                    `protobuf:"bytes,15,opt,name=telegram,json=telegram,proto3" json:"telegram"`
	Youtube       string                    `protobuf:"bytes,16,opt,name=youtube,json=youtube,proto3" json:"youtube"`
	Reddit        string                    `protobuf:"bytes,17,opt,name=reddit,json=reddit,proto3" json:"reddit"`
	Tiktop        string                    `protobuf:"bytes,18,opt,name=tiktop,json=tiktop,proto3" json:"tiktop"`
	Enable        bool                      `protobuf:"varint,19,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt     int64                     `protobuf:"varint,20,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	UpdatedAt     int64                     `protobuf:"varint,21,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	Creator       string                    `protobuf:"bytes,22,opt,name=creator,json=creator,proto3" json:"creator"`
	Languages     map[string]*GuestLanguage `protobuf:"bytes,23,rep,name=languages,json=languages,proto3" json:"languages" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *Guest) Reset() {
	*x = Guest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Guest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Guest) ProtoMessage() {}

func (x *Guest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Guest.ProtoReflect.Descriptor instead.
func (*Guest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{1}
}

func (x *Guest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Guest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Guest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Guest) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

func (x *Guest) GetExpoIds() []int64 {
	if x != nil {
		return x.ExpoIds
	}
	return nil
}

func (x *Guest) GetPhoneAreaCode() string {
	if x != nil {
		return x.PhoneAreaCode
	}
	return ""
}

func (x *Guest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *Guest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Guest) GetWhatsApp() string {
	if x != nil {
		return x.WhatsApp
	}
	return ""
}

func (x *Guest) GetWechat() string {
	if x != nil {
		return x.Wechat
	}
	return ""
}

func (x *Guest) GetFacebook() string {
	if x != nil {
		return x.Facebook
	}
	return ""
}

func (x *Guest) GetTwitter() string {
	if x != nil {
		return x.Twitter
	}
	return ""
}

func (x *Guest) GetLinkedin() string {
	if x != nil {
		return x.Linkedin
	}
	return ""
}

func (x *Guest) GetInstagram() string {
	if x != nil {
		return x.Instagram
	}
	return ""
}

func (x *Guest) GetTelegram() string {
	if x != nil {
		return x.Telegram
	}
	return ""
}

func (x *Guest) GetYoutube() string {
	if x != nil {
		return x.Youtube
	}
	return ""
}

func (x *Guest) GetReddit() string {
	if x != nil {
		return x.Reddit
	}
	return ""
}

func (x *Guest) GetTiktop() string {
	if x != nil {
		return x.Tiktop
	}
	return ""
}

func (x *Guest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Guest) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Guest) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Guest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Guest) GetLanguages() map[string]*GuestLanguage {
	if x != nil {
		return x.Languages
	}
	return nil
}

type AddGuestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddGuestReply) Reset() {
	*x = AddGuestReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddGuestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddGuestReply) ProtoMessage() {}

func (x *AddGuestReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddGuestReply.ProtoReflect.Descriptor instead.
func (*AddGuestReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{2}
}

func (x *AddGuestReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SetGuestEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Enable bool  `protobuf:"varint,2,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetGuestEnableRequest) Reset() {
	*x = SetGuestEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetGuestEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetGuestEnableRequest) ProtoMessage() {}

func (x *SetGuestEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetGuestEnableRequest.ProtoReflect.Descriptor instead.
func (*SetGuestEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{3}
}

func (x *SetGuestEnableRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SetGuestEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetGuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetGuestRequest) Reset() {
	*x = GetGuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuestRequest) ProtoMessage() {}

func (x *GetGuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuestRequest.ProtoReflect.Descriptor instead.
func (*GetGuestRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{4}
}

func (x *GetGuestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListGuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32 `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	Size int32 `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListGuestRequest) Reset() {
	*x = ListGuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGuestRequest) ProtoMessage() {}

func (x *ListGuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGuestRequest.ProtoReflect.Descriptor instead.
func (*ListGuestRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{5}
}

func (x *ListGuestRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGuestRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListGuestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guests []*Guest `protobuf:"bytes,1,rep,name=guests,json=guests,proto3" json:"guests"`
}

func (x *ListGuestReply) Reset() {
	*x = ListGuestReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGuestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGuestReply) ProtoMessage() {}

func (x *ListGuestReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGuestReply.ProtoReflect.Descriptor instead.
func (*ListGuestReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{6}
}

func (x *ListGuestReply) GetGuests() []*Guest {
	if x != nil {
		return x.Guests
	}
	return nil
}

type DeleteGuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteGuestRequest) Reset() {
	*x = DeleteGuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGuestRequest) ProtoMessage() {}

func (x *DeleteGuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGuestRequest.ProtoReflect.Descriptor instead.
func (*DeleteGuestRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteGuestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ExpoCommunityLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,json=description,proto3" json:"description"`
	Logo        string `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
}

func (x *ExpoCommunityLanguage) Reset() {
	*x = ExpoCommunityLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoCommunityLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoCommunityLanguage) ProtoMessage() {}

func (x *ExpoCommunityLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoCommunityLanguage.ProtoReflect.Descriptor instead.
func (*ExpoCommunityLanguage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{8}
}

func (x *ExpoCommunityLanguage) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExpoCommunityLanguage) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

type ExpoCommunity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId    int64                             `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Languages map[string]*ExpoCommunityLanguage `protobuf:"bytes,2,rep,name=languages,json=languages,proto3" json:"languages" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
	Enable    bool                              `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64                             `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	UpdatedAt int64                             `protobuf:"varint,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	Creator   string                            `protobuf:"bytes,6,opt,name=creator,json=creator,proto3" json:"creator"`
}

func (x *ExpoCommunity) Reset() {
	*x = ExpoCommunity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoCommunity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoCommunity) ProtoMessage() {}

func (x *ExpoCommunity) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoCommunity.ProtoReflect.Descriptor instead.
func (*ExpoCommunity) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{9}
}

func (x *ExpoCommunity) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoCommunity) GetLanguages() map[string]*ExpoCommunityLanguage {
	if x != nil {
		return x.Languages
	}
	return nil
}

func (x *ExpoCommunity) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoCommunity) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoCommunity) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ExpoCommunity) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type GetExpoCommunityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoCommunityRequest) Reset() {
	*x = GetExpoCommunityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoCommunityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoCommunityRequest) ProtoMessage() {}

func (x *GetExpoCommunityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoCommunityRequest.ProtoReflect.Descriptor instead.
func (*GetExpoCommunityRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{10}
}

func (x *GetExpoCommunityRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SetExpoCommunityEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Enable bool  `protobuf:"varint,2,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetExpoCommunityEnableRequest) Reset() {
	*x = SetExpoCommunityEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetExpoCommunityEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetExpoCommunityEnableRequest) ProtoMessage() {}

func (x *SetExpoCommunityEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetExpoCommunityEnableRequest.ProtoReflect.Descriptor instead.
func (*SetExpoCommunityEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{11}
}

func (x *SetExpoCommunityEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetExpoCommunityEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type DeleteExpoCommunityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoCommunityRequest) Reset() {
	*x = DeleteExpoCommunityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoCommunityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoCommunityRequest) ProtoMessage() {}

func (x *DeleteExpoCommunityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoCommunityRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoCommunityRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteExpoCommunityRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoHallLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
}

func (x *ExpoHallLanguage) Reset() {
	*x = ExpoHallLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoHallLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoHallLanguage) ProtoMessage() {}

func (x *ExpoHallLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoHallLanguage.ProtoReflect.Descriptor instead.
func (*ExpoHallLanguage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{13}
}

func (x *ExpoHallLanguage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ExpoHall struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId    int64                        `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Id        int64                        `protobuf:"varint,2,opt,name=id,json=id,proto3" json:"id"`
	Type      ExpoHallType                 `protobuf:"varint,3,opt,name=type,json=type,proto3,enum=api.expo.v1.ExpoHallType" json:"type"`
	Name      string                       `protobuf:"bytes,4,opt,name=name,json=name,proto3" json:"name"`
	Enable    bool                         `protobuf:"varint,5,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64                        `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator   string                       `protobuf:"bytes,7,opt,name=creator,json=creator,proto3" json:"creator"`
	Languages map[string]*ExpoHallLanguage `protobuf:"bytes,8,rep,name=languages,json=languages,proto3" json:"languages" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *ExpoHall) Reset() {
	*x = ExpoHall{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoHall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoHall) ProtoMessage() {}

func (x *ExpoHall) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoHall.ProtoReflect.Descriptor instead.
func (*ExpoHall) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{14}
}

func (x *ExpoHall) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoHall) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoHall) GetType() ExpoHallType {
	if x != nil {
		return x.Type
	}
	return ExpoHallType_ExpoHallType_MAIN
}

func (x *ExpoHall) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoHall) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoHall) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoHall) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ExpoHall) GetLanguages() map[string]*ExpoHallLanguage {
	if x != nil {
		return x.Languages
	}
	return nil
}

type GetHallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Id     int64 `protobuf:"varint,2,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetHallRequest) Reset() {
	*x = GetHallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHallRequest) ProtoMessage() {}

func (x *GetHallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHallRequest.ProtoReflect.Descriptor instead.
func (*GetHallRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{15}
}

func (x *GetHallRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetHallRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SetHallEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Id     int64 `protobuf:"varint,2,opt,name=id,json=id,proto3" json:"id"`
	Enable bool  `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetHallEnableRequest) Reset() {
	*x = SetHallEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetHallEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetHallEnableRequest) ProtoMessage() {}

func (x *SetHallEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetHallEnableRequest.ProtoReflect.Descriptor instead.
func (*SetHallEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{16}
}

func (x *SetHallEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetHallEnableRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SetHallEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListHallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListHallRequest) Reset() {
	*x = ListHallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHallRequest) ProtoMessage() {}

func (x *ListHallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHallRequest.ProtoReflect.Descriptor instead.
func (*ListHallRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{17}
}

func (x *ListHallRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListHallRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListHallRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListHallReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ExpoHall `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *ListHallReply) Reset() {
	*x = ListHallReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListHallReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHallReply) ProtoMessage() {}

func (x *ListHallReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHallReply.ProtoReflect.Descriptor instead.
func (*ListHallReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{18}
}

func (x *ListHallReply) GetItems() []*ExpoHall {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteHallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Id     int64 `protobuf:"varint,2,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteHallRequest) Reset() {
	*x = DeleteHallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHallRequest) ProtoMessage() {}

func (x *DeleteHallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHallRequest.ProtoReflect.Descriptor instead.
func (*DeleteHallRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteHallRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *DeleteHallRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ExpoGuest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId     int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	GuestId    int64  `protobuf:"varint,3,opt,name=guest_id,json=guestId,proto3" json:"guest_id"`
	Avatar     string `protobuf:"bytes,4,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Name       string `protobuf:"bytes,5,opt,name=name,json=name,proto3" json:"name"`
	WikiNumber string `protobuf:"bytes,6,opt,name=wiki_number,json=wikiNumber,proto3" json:"wiki_number"`
	Phone      string `protobuf:"bytes,7,opt,name=phone,json=phone,proto3" json:"phone"`
	Email      string `protobuf:"bytes,8,opt,name=email,json=email,proto3" json:"email"`
	Enable     bool   `protobuf:"varint,9,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt  int64  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator    string `protobuf:"bytes,11,opt,name=creator,json=creator,proto3" json:"creator"`
}

func (x *ExpoGuest) Reset() {
	*x = ExpoGuest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoGuest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoGuest) ProtoMessage() {}

func (x *ExpoGuest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoGuest.ProtoReflect.Descriptor instead.
func (*ExpoGuest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{20}
}

func (x *ExpoGuest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoGuest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoGuest) GetGuestId() int64 {
	if x != nil {
		return x.GuestId
	}
	return 0
}

func (x *ExpoGuest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ExpoGuest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoGuest) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

func (x *ExpoGuest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ExpoGuest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ExpoGuest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoGuest) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoGuest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type AddExpoGuestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoGuestReply) Reset() {
	*x = AddExpoGuestReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoGuestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoGuestReply) ProtoMessage() {}

func (x *AddExpoGuestReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoGuestReply.ProtoReflect.Descriptor instead.
func (*AddExpoGuestReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{21}
}

func (x *AddExpoGuestReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetExpoGuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Id     int64 `protobuf:"varint,2,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetExpoGuestRequest) Reset() {
	*x = GetExpoGuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoGuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoGuestRequest) ProtoMessage() {}

func (x *GetExpoGuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoGuestRequest.ProtoReflect.Descriptor instead.
func (*GetExpoGuestRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{22}
}

func (x *GetExpoGuestRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetExpoGuestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetExpoGuestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guest *Guest `protobuf:"bytes,1,opt,name=guest,json=guest,proto3" json:"guest"`
}

func (x *GetExpoGuestReply) Reset() {
	*x = GetExpoGuestReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoGuestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoGuestReply) ProtoMessage() {}

func (x *GetExpoGuestReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoGuestReply.ProtoReflect.Descriptor instead.
func (*GetExpoGuestReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{23}
}

func (x *GetExpoGuestReply) GetGuest() *Guest {
	if x != nil {
		return x.Guest
	}
	return nil
}

type ListExpoGuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListExpoGuestRequest) Reset() {
	*x = ListExpoGuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoGuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoGuestRequest) ProtoMessage() {}

func (x *ListExpoGuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoGuestRequest.ProtoReflect.Descriptor instead.
func (*ListExpoGuestRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{24}
}

func (x *ListExpoGuestRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListExpoGuestRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExpoGuestRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListExpoGuestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guests []*ExpoGuest `protobuf:"bytes,1,rep,name=guests,json=guests,proto3" json:"guests"`
}

func (x *ListExpoGuestReply) Reset() {
	*x = ListExpoGuestReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoGuestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoGuestReply) ProtoMessage() {}

func (x *ListExpoGuestReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoGuestReply.ProtoReflect.Descriptor instead.
func (*ListExpoGuestReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{25}
}

func (x *ListExpoGuestReply) GetGuests() []*ExpoGuest {
	if x != nil {
		return x.Guests
	}
	return nil
}

type DeleteExpoGuestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Id     int64 `protobuf:"varint,2,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteExpoGuestRequest) Reset() {
	*x = DeleteExpoGuestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoGuestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoGuestRequest) ProtoMessage() {}

func (x *DeleteExpoGuestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoGuestRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoGuestRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{26}
}

func (x *DeleteExpoGuestRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *DeleteExpoGuestRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ExpoScheduleLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Theme string `protobuf:"bytes,1,opt,name=theme,json=theme,proto3" json:"theme"`
}

func (x *ExpoScheduleLanguage) Reset() {
	*x = ExpoScheduleLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleLanguage) ProtoMessage() {}

func (x *ExpoScheduleLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleLanguage.ProtoReflect.Descriptor instead.
func (*ExpoScheduleLanguage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{27}
}

func (x *ExpoScheduleLanguage) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

type ExpoScheduleSpeaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Name       string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	WikiNumber string `protobuf:"bytes,6,opt,name=wiki_number,json=wikiNumber,proto3" json:"wiki_number"`
	Phone      string `protobuf:"bytes,7,opt,name=phone,json=phone,proto3" json:"phone"`
	Email      string `protobuf:"bytes,8,opt,name=email,json=email,proto3" json:"email"`
}

func (x *ExpoScheduleSpeaker) Reset() {
	*x = ExpoScheduleSpeaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleSpeaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleSpeaker) ProtoMessage() {}

func (x *ExpoScheduleSpeaker) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleSpeaker.ProtoReflect.Descriptor instead.
func (*ExpoScheduleSpeaker) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{28}
}

func (x *ExpoScheduleSpeaker) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoScheduleSpeaker) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoScheduleSpeaker) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

func (x *ExpoScheduleSpeaker) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ExpoScheduleSpeaker) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type ExpoScheduleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64                            `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId    int64                            `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	HallId    int64                            `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id"`
	Type      ScheduleType                     `protobuf:"varint,4,opt,name=type,json=type,proto3,enum=api.expo.v1.ScheduleType" json:"type"`
	Theme     string                           `protobuf:"bytes,5,opt,name=theme,json=theme,proto3" json:"theme"`
	Host      *ExpoScheduleSpeaker             `protobuf:"bytes,6,opt,name=host,json=host,proto3" json:"host"`
	Speakers  []*ExpoScheduleSpeaker           `protobuf:"bytes,7,rep,name=speakers,json=speakers,proto3" json:"speakers"`
	Start     int64                            `protobuf:"varint,8,opt,name=start,json=start,proto3" json:"start"`
	End       int64                            `protobuf:"varint,9,opt,name=end,json=end,proto3" json:"end"`
	Enable    bool                             `protobuf:"varint,10,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64                            `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator   string                           `protobuf:"bytes,12,opt,name=creator,json=creator,proto3" json:"creator"`
	Languages map[string]*ExpoScheduleLanguage `protobuf:"bytes,13,rep,name=languages,json=languages,proto3" json:"languages" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
}

func (x *ExpoScheduleInfo) Reset() {
	*x = ExpoScheduleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleInfo) ProtoMessage() {}

func (x *ExpoScheduleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleInfo.ProtoReflect.Descriptor instead.
func (*ExpoScheduleInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{29}
}

func (x *ExpoScheduleInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoScheduleInfo) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoScheduleInfo) GetHallId() int64 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *ExpoScheduleInfo) GetType() ScheduleType {
	if x != nil {
		return x.Type
	}
	return ScheduleType_ScheduleType_PERSONAL
}

func (x *ExpoScheduleInfo) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

func (x *ExpoScheduleInfo) GetHost() *ExpoScheduleSpeaker {
	if x != nil {
		return x.Host
	}
	return nil
}

func (x *ExpoScheduleInfo) GetSpeakers() []*ExpoScheduleSpeaker {
	if x != nil {
		return x.Speakers
	}
	return nil
}

func (x *ExpoScheduleInfo) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *ExpoScheduleInfo) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *ExpoScheduleInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoScheduleInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoScheduleInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ExpoScheduleInfo) GetLanguages() map[string]*ExpoScheduleLanguage {
	if x != nil {
		return x.Languages
	}
	return nil
}

type AddExpoScheduleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoScheduleReply) Reset() {
	*x = AddExpoScheduleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoScheduleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoScheduleReply) ProtoMessage() {}

func (x *AddExpoScheduleReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoScheduleReply.ProtoReflect.Descriptor instead.
func (*AddExpoScheduleReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{30}
}

func (x *AddExpoScheduleReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetExpoScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoScheduleRequest) Reset() {
	*x = GetExpoScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoScheduleRequest) ProtoMessage() {}

func (x *GetExpoScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetExpoScheduleRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{31}
}

func (x *GetExpoScheduleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetExpoScheduleRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ListExpoScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListExpoScheduleRequest) Reset() {
	*x = ListExpoScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoScheduleRequest) ProtoMessage() {}

func (x *ListExpoScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoScheduleRequest.ProtoReflect.Descriptor instead.
func (*ListExpoScheduleRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{32}
}

func (x *ListExpoScheduleRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListExpoScheduleRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExpoScheduleRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListExpoScheduleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schedules []*ExpoScheduleInfo `protobuf:"bytes,1,rep,name=schedules,json=schedules,proto3" json:"schedules"`
}

func (x *ListExpoScheduleReply) Reset() {
	*x = ListExpoScheduleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoScheduleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoScheduleReply) ProtoMessage() {}

func (x *ListExpoScheduleReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoScheduleReply.ProtoReflect.Descriptor instead.
func (*ListExpoScheduleReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{33}
}

func (x *ListExpoScheduleReply) GetSchedules() []*ExpoScheduleInfo {
	if x != nil {
		return x.Schedules
	}
	return nil
}

type DeleteExpoScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoScheduleRequest) Reset() {
	*x = DeleteExpoScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoScheduleRequest) ProtoMessage() {}

func (x *DeleteExpoScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoScheduleRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoScheduleRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{34}
}

func (x *DeleteExpoScheduleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteExpoScheduleRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExhibitorEmployeeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatar     string `protobuf:"bytes,1,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Name       string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	WikiNumber string `protobuf:"bytes,3,opt,name=wiki_number,json=wikiNumber,proto3" json:"wiki_number"`
}

func (x *ExhibitorEmployeeInfo) Reset() {
	*x = ExhibitorEmployeeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitorEmployeeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitorEmployeeInfo) ProtoMessage() {}

func (x *ExhibitorEmployeeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitorEmployeeInfo.ProtoReflect.Descriptor instead.
func (*ExhibitorEmployeeInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{35}
}

func (x *ExhibitorEmployeeInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ExhibitorEmployeeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExhibitorEmployeeInfo) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

type ExpoExhibitorLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExpoExhibitorLanguage) Reset() {
	*x = ExpoExhibitorLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoExhibitorLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoExhibitorLanguage) ProtoMessage() {}

func (x *ExpoExhibitorLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoExhibitorLanguage.ProtoReflect.Descriptor instead.
func (*ExpoExhibitorLanguage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{36}
}

type ExpoExhibitorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64                    `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId        int64                    `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	HallId        int64                    `protobuf:"varint,3,opt,name=hall_id,json=hallId,proto3" json:"hall_id"`
	TraderCode    string                   `protobuf:"bytes,4,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	TraderName    string                   `protobuf:"bytes,5,opt,name=trader_name,json=traderName,proto3" json:"trader_name"`
	TraderLogo    string                   `protobuf:"bytes,6,opt,name=trader_logo,json=traderLogo,proto3" json:"trader_logo"`
	TraderMinLogo string                   `protobuf:"bytes,7,opt,name=trader_min_logo,json=traderMinLogo,proto3" json:"trader_min_logo"`
	SponsorLevel  SponsorLevel             `protobuf:"varint,8,opt,name=sponsor_level,json=sponsorLevel,proto3,enum=api.expo.v1.SponsorLevel" json:"sponsor_level"`
	BoothLength   string                   `protobuf:"bytes,9,opt,name=booth_length,json=boothLength,proto3" json:"booth_length"`
	BoothWidth    string                   `protobuf:"bytes,10,opt,name=booth_width,json=boothWidth,proto3" json:"booth_width"`
	BoothHeight   string                   `protobuf:"bytes,11,opt,name=booth_height,json=boothHeight,proto3" json:"booth_height"`
	Booth         string                   `protobuf:"bytes,12,opt,name=booth,json=booth,proto3" json:"booth"`
	Contact       string                   `protobuf:"bytes,13,opt,name=contact,json=contact,proto3" json:"contact"`
	PhoneAreaCode string                   `protobuf:"bytes,14,opt,name=phone_area_code,json=phoneAreaCode,proto3" json:"phone_area_code"`
	Phone         string                   `protobuf:"bytes,15,opt,name=phone,json=phone,proto3" json:"phone"`
	Email         string                   `protobuf:"bytes,16,opt,name=email,json=email,proto3" json:"email"`
	Enable        bool                     `protobuf:"varint,17,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt     int64                    `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator       string                   `protobuf:"bytes,19,opt,name=creator,json=creator,proto3" json:"creator"`
	Employees     []*ExhibitorEmployeeInfo `protobuf:"bytes,20,rep,name=employees,json=employees,proto3" json:"employees"`
	Rank          int64                    `protobuf:"varint,21,opt,name=rank,json=rank,proto3" json:"rank"`
}

func (x *ExpoExhibitorInfo) Reset() {
	*x = ExpoExhibitorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoExhibitorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoExhibitorInfo) ProtoMessage() {}

func (x *ExpoExhibitorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoExhibitorInfo.ProtoReflect.Descriptor instead.
func (*ExpoExhibitorInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{37}
}

func (x *ExpoExhibitorInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoExhibitorInfo) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoExhibitorInfo) GetHallId() int64 {
	if x != nil {
		return x.HallId
	}
	return 0
}

func (x *ExpoExhibitorInfo) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetTraderName() string {
	if x != nil {
		return x.TraderName
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetTraderLogo() string {
	if x != nil {
		return x.TraderLogo
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetTraderMinLogo() string {
	if x != nil {
		return x.TraderMinLogo
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetSponsorLevel() SponsorLevel {
	if x != nil {
		return x.SponsorLevel
	}
	return SponsorLevel_SponsorLevel_NONE
}

func (x *ExpoExhibitorInfo) GetBoothLength() string {
	if x != nil {
		return x.BoothLength
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetBoothWidth() string {
	if x != nil {
		return x.BoothWidth
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetBoothHeight() string {
	if x != nil {
		return x.BoothHeight
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetBooth() string {
	if x != nil {
		return x.Booth
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetPhoneAreaCode() string {
	if x != nil {
		return x.PhoneAreaCode
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoExhibitorInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoExhibitorInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ExpoExhibitorInfo) GetEmployees() []*ExhibitorEmployeeInfo {
	if x != nil {
		return x.Employees
	}
	return nil
}

func (x *ExpoExhibitorInfo) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

type AddExpoExhibitorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoExhibitorReply) Reset() {
	*x = AddExpoExhibitorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoExhibitorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoExhibitorReply) ProtoMessage() {}

func (x *AddExpoExhibitorReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoExhibitorReply.ProtoReflect.Descriptor instead.
func (*AddExpoExhibitorReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{38}
}

func (x *AddExpoExhibitorReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetExpoExhibitorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoExhibitorRequest) Reset() {
	*x = GetExpoExhibitorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoExhibitorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoExhibitorRequest) ProtoMessage() {}

func (x *GetExpoExhibitorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoExhibitorRequest.ProtoReflect.Descriptor instead.
func (*GetExpoExhibitorRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{39}
}

func (x *GetExpoExhibitorRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetExpoExhibitorRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SetExpoExhibitorEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Enable bool  `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetExpoExhibitorEnableRequest) Reset() {
	*x = SetExpoExhibitorEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetExpoExhibitorEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetExpoExhibitorEnableRequest) ProtoMessage() {}

func (x *SetExpoExhibitorEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetExpoExhibitorEnableRequest.ProtoReflect.Descriptor instead.
func (*SetExpoExhibitorEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{40}
}

func (x *SetExpoExhibitorEnableRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SetExpoExhibitorEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetExpoExhibitorEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListExpoExhibitorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListExpoExhibitorRequest) Reset() {
	*x = ListExpoExhibitorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoExhibitorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoExhibitorRequest) ProtoMessage() {}

func (x *ListExpoExhibitorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoExhibitorRequest.ProtoReflect.Descriptor instead.
func (*ListExpoExhibitorRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{41}
}

func (x *ListExpoExhibitorRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListExpoExhibitorRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExpoExhibitorRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListExpoExhibitorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exhibitors []*ExpoExhibitorInfo `protobuf:"bytes,1,rep,name=exhibitors,json=exhibitors,proto3" json:"exhibitors"`
}

func (x *ListExpoExhibitorReply) Reset() {
	*x = ListExpoExhibitorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoExhibitorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoExhibitorReply) ProtoMessage() {}

func (x *ListExpoExhibitorReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoExhibitorReply.ProtoReflect.Descriptor instead.
func (*ListExpoExhibitorReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{42}
}

func (x *ListExpoExhibitorReply) GetExhibitors() []*ExpoExhibitorInfo {
	if x != nil {
		return x.Exhibitors
	}
	return nil
}

type DeleteExpoExhibitorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoExhibitorRequest) Reset() {
	*x = DeleteExpoExhibitorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoExhibitorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoExhibitorRequest) ProtoMessage() {}

func (x *DeleteExpoExhibitorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoExhibitorRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoExhibitorRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{43}
}

func (x *DeleteExpoExhibitorRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteExpoExhibitorRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoGuideInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId    int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	MapUrl    string `protobuf:"bytes,3,opt,name=map_url,json=mapUrl,proto3" json:"map_url"`
	Enable    bool   `protobuf:"varint,17,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64  `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator   string `protobuf:"bytes,19,opt,name=creator,json=creator,proto3" json:"creator"`
}

func (x *ExpoGuideInfo) Reset() {
	*x = ExpoGuideInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoGuideInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoGuideInfo) ProtoMessage() {}

func (x *ExpoGuideInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoGuideInfo.ProtoReflect.Descriptor instead.
func (*ExpoGuideInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{44}
}

func (x *ExpoGuideInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoGuideInfo) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoGuideInfo) GetMapUrl() string {
	if x != nil {
		return x.MapUrl
	}
	return ""
}

func (x *ExpoGuideInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoGuideInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoGuideInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type AddExpoGuideReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoGuideReply) Reset() {
	*x = AddExpoGuideReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoGuideReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoGuideReply) ProtoMessage() {}

func (x *AddExpoGuideReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoGuideReply.ProtoReflect.Descriptor instead.
func (*AddExpoGuideReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{45}
}

func (x *AddExpoGuideReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetExpoGuideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoGuideRequest) Reset() {
	*x = GetExpoGuideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoGuideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoGuideRequest) ProtoMessage() {}

func (x *GetExpoGuideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoGuideRequest.ProtoReflect.Descriptor instead.
func (*GetExpoGuideRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{46}
}

func (x *GetExpoGuideRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetExpoGuideRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SetExpoGuideEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Enable bool   `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetExpoGuideEnableRequest) Reset() {
	*x = SetExpoGuideEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetExpoGuideEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetExpoGuideEnableRequest) ProtoMessage() {}

func (x *SetExpoGuideEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetExpoGuideEnableRequest.ProtoReflect.Descriptor instead.
func (*SetExpoGuideEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{47}
}

func (x *SetExpoGuideEnableRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetExpoGuideEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetExpoGuideEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListExpoGuideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *ListExpoGuideRequest) Reset() {
	*x = ListExpoGuideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoGuideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoGuideRequest) ProtoMessage() {}

func (x *ListExpoGuideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoGuideRequest.ProtoReflect.Descriptor instead.
func (*ListExpoGuideRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{48}
}

func (x *ListExpoGuideRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ListExpoGuideReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ExpoGuideInfo `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *ListExpoGuideReply) Reset() {
	*x = ListExpoGuideReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoGuideReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoGuideReply) ProtoMessage() {}

func (x *ListExpoGuideReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoGuideReply.ProtoReflect.Descriptor instead.
func (*ListExpoGuideReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{49}
}

func (x *ListExpoGuideReply) GetItems() []*ExpoGuideInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteExpoGuideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoGuideRequest) Reset() {
	*x = DeleteExpoGuideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoGuideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoGuideRequest) ProtoMessage() {}

func (x *DeleteExpoGuideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoGuideRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoGuideRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{50}
}

func (x *DeleteExpoGuideRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeleteExpoGuideRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type GetExpoPartnerTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetExpoPartnerTypeRequest) Reset() {
	*x = GetExpoPartnerTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoPartnerTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoPartnerTypeRequest) ProtoMessage() {}

func (x *GetExpoPartnerTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoPartnerTypeRequest.ProtoReflect.Descriptor instead.
func (*GetExpoPartnerTypeRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{51}
}

type ExpoPartnerTypeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
}

func (x *ExpoPartnerTypeItem) Reset() {
	*x = ExpoPartnerTypeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoPartnerTypeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoPartnerTypeItem) ProtoMessage() {}

func (x *ExpoPartnerTypeItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoPartnerTypeItem.ProtoReflect.Descriptor instead.
func (*ExpoPartnerTypeItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{52}
}

func (x *ExpoPartnerTypeItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoPartnerTypeItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetExpoPartnerTypeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ExpoPartnerTypeItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetExpoPartnerTypeReply) Reset() {
	*x = GetExpoPartnerTypeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoPartnerTypeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoPartnerTypeReply) ProtoMessage() {}

func (x *GetExpoPartnerTypeReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoPartnerTypeReply.ProtoReflect.Descriptor instead.
func (*GetExpoPartnerTypeReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{53}
}

func (x *GetExpoPartnerTypeReply) GetItems() []*ExpoPartnerTypeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type ExpoPartnerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId    int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Name      string `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Logo      string `protobuf:"bytes,4,opt,name=logo,json=logo,proto3" json:"logo"`
	Website   string `protobuf:"bytes,5,opt,name=website,json=website,proto3" json:"website"`
	Rank      int32  `protobuf:"varint,6,opt,name=rank,json=rank,proto3" json:"rank"`
	Type      string `protobuf:"bytes,7,opt,name=type,json=type,proto3" json:"type"`
	Enable    bool   `protobuf:"varint,17,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64  `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator   string `protobuf:"bytes,19,opt,name=creator,json=creator,proto3" json:"creator"`
}

func (x *ExpoPartnerInfo) Reset() {
	*x = ExpoPartnerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoPartnerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoPartnerInfo) ProtoMessage() {}

func (x *ExpoPartnerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoPartnerInfo.ProtoReflect.Descriptor instead.
func (*ExpoPartnerInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{54}
}

func (x *ExpoPartnerInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoPartnerInfo) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoPartnerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoPartnerInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExpoPartnerInfo) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *ExpoPartnerInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *ExpoPartnerInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ExpoPartnerInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoPartnerInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoPartnerInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type AddExpoPartnerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoPartnerReply) Reset() {
	*x = AddExpoPartnerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoPartnerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoPartnerReply) ProtoMessage() {}

func (x *AddExpoPartnerReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoPartnerReply.ProtoReflect.Descriptor instead.
func (*AddExpoPartnerReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{55}
}

func (x *AddExpoPartnerReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetExpoPartnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoPartnerRequest) Reset() {
	*x = GetExpoPartnerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoPartnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoPartnerRequest) ProtoMessage() {}

func (x *GetExpoPartnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoPartnerRequest.ProtoReflect.Descriptor instead.
func (*GetExpoPartnerRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{56}
}

func (x *GetExpoPartnerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetExpoPartnerRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SetExpoPartnerEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Enable bool   `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetExpoPartnerEnableRequest) Reset() {
	*x = SetExpoPartnerEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetExpoPartnerEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetExpoPartnerEnableRequest) ProtoMessage() {}

func (x *SetExpoPartnerEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetExpoPartnerEnableRequest.ProtoReflect.Descriptor instead.
func (*SetExpoPartnerEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{57}
}

func (x *SetExpoPartnerEnableRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetExpoPartnerEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetExpoPartnerEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListExpoPartnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId  int64  `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page    int64  `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size    int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Type    int64  `protobuf:"varint,4,opt,name=type,json=type,proto3" json:"type"`
	Keyword string `protobuf:"bytes,5,opt,name=keyword,json=keyword,proto3" json:"keyword"`
}

func (x *ListExpoPartnerRequest) Reset() {
	*x = ListExpoPartnerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoPartnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoPartnerRequest) ProtoMessage() {}

func (x *ListExpoPartnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoPartnerRequest.ProtoReflect.Descriptor instead.
func (*ListExpoPartnerRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{58}
}

func (x *ListExpoPartnerRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListExpoPartnerRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExpoPartnerRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListExpoPartnerRequest) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ListExpoPartnerRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type ListExpoPartnerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Partners []*ExpoPartnerInfo `protobuf:"bytes,1,rep,name=partners,json=partners,proto3" json:"partners"`
}

func (x *ListExpoPartnerReply) Reset() {
	*x = ListExpoPartnerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoPartnerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoPartnerReply) ProtoMessage() {}

func (x *ListExpoPartnerReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoPartnerReply.ProtoReflect.Descriptor instead.
func (*ListExpoPartnerReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{59}
}

func (x *ListExpoPartnerReply) GetPartners() []*ExpoPartnerInfo {
	if x != nil {
		return x.Partners
	}
	return nil
}

type DeleteExpoPartnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoPartnerRequest) Reset() {
	*x = DeleteExpoPartnerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoPartnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoPartnerRequest) ProtoMessage() {}

func (x *DeleteExpoPartnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoPartnerRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoPartnerRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{60}
}

func (x *DeleteExpoPartnerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteExpoPartnerRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoReviewLanguage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,json=description,proto3" json:"description"`
}

func (x *ExpoReviewLanguage) Reset() {
	*x = ExpoReviewLanguage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoReviewLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoReviewLanguage) ProtoMessage() {}

func (x *ExpoReviewLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoReviewLanguage.ProtoReflect.Descriptor instead.
func (*ExpoReviewLanguage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{61}
}

func (x *ExpoReviewLanguage) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ExpoReviewInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64                          `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId    int64                          `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Type      ExpoReviewType                 `protobuf:"varint,3,opt,name=type,json=type,proto3,enum=api.expo.v1.ExpoReviewType" json:"type"`
	VideoUrl  string                         `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url"`
	ImageUrl  []string                       `protobuf:"bytes,5,rep,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Languages map[string]*ExpoReviewLanguage `protobuf:"bytes,6,rep,name=languages,json=languages,proto3" json:"languages" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
	FileCount int64                          `protobuf:"varint,7,opt,name=file_count,json=fileCount,proto3" json:"file_count"`
	Enable    bool                           `protobuf:"varint,17,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64                          `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator   string                         `protobuf:"bytes,19,opt,name=creator,json=creator,proto3" json:"creator"`
}

func (x *ExpoReviewInfo) Reset() {
	*x = ExpoReviewInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoReviewInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoReviewInfo) ProtoMessage() {}

func (x *ExpoReviewInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoReviewInfo.ProtoReflect.Descriptor instead.
func (*ExpoReviewInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{62}
}

func (x *ExpoReviewInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoReviewInfo) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoReviewInfo) GetType() ExpoReviewType {
	if x != nil {
		return x.Type
	}
	return ExpoReviewType_REVIEW_TYPE_VIDEO
}

func (x *ExpoReviewInfo) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *ExpoReviewInfo) GetImageUrl() []string {
	if x != nil {
		return x.ImageUrl
	}
	return nil
}

func (x *ExpoReviewInfo) GetLanguages() map[string]*ExpoReviewLanguage {
	if x != nil {
		return x.Languages
	}
	return nil
}

func (x *ExpoReviewInfo) GetFileCount() int64 {
	if x != nil {
		return x.FileCount
	}
	return 0
}

func (x *ExpoReviewInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoReviewInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoReviewInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type AddExpoReviewReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoReviewReply) Reset() {
	*x = AddExpoReviewReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoReviewReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoReviewReply) ProtoMessage() {}

func (x *AddExpoReviewReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoReviewReply.ProtoReflect.Descriptor instead.
func (*AddExpoReviewReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{63}
}

func (x *AddExpoReviewReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetExpoReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoReviewRequest) Reset() {
	*x = GetExpoReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoReviewRequest) ProtoMessage() {}

func (x *GetExpoReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoReviewRequest.ProtoReflect.Descriptor instead.
func (*GetExpoReviewRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{64}
}

func (x *GetExpoReviewRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetExpoReviewRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SetExpoReviewEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Enable bool   `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetExpoReviewEnableRequest) Reset() {
	*x = SetExpoReviewEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetExpoReviewEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetExpoReviewEnableRequest) ProtoMessage() {}

func (x *SetExpoReviewEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetExpoReviewEnableRequest.ProtoReflect.Descriptor instead.
func (*SetExpoReviewEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{65}
}

func (x *SetExpoReviewEnableRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SetExpoReviewEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetExpoReviewEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListExpoReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int64 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int64 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListExpoReviewRequest) Reset() {
	*x = ListExpoReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoReviewRequest) ProtoMessage() {}

func (x *ListExpoReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoReviewRequest.ProtoReflect.Descriptor instead.
func (*ListExpoReviewRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{66}
}

func (x *ListExpoReviewRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListExpoReviewRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExpoReviewRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListExpoReviewReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ExpoReviewInfo `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *ListExpoReviewReply) Reset() {
	*x = ListExpoReviewReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoReviewReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoReviewReply) ProtoMessage() {}

func (x *ListExpoReviewReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoReviewReply.ProtoReflect.Descriptor instead.
func (*ListExpoReviewReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{67}
}

func (x *ListExpoReviewReply) GetItems() []*ExpoReviewInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteExpoReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoReviewRequest) Reset() {
	*x = DeleteExpoReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoReviewRequest) ProtoMessage() {}

func (x *DeleteExpoReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoReviewRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoReviewRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{68}
}

func (x *DeleteExpoReviewRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteExpoReviewRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoLive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId    int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Level     int32  `protobuf:"varint,3,opt,name=level,json=level,proto3" json:"level"`
	Cover     string `protobuf:"bytes,4,opt,name=cover,json=cover,proto3" json:"cover"`
	Url       string `protobuf:"bytes,5,opt,name=url,json=url,proto3" json:"url"`
	Enable    bool   `protobuf:"varint,17,opt,name=enable,json=enable,proto3" json:"enable"`
	CreatedAt int64  `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Creator   string `protobuf:"bytes,19,opt,name=creator,json=creator,proto3" json:"creator"`
}

func (x *ExpoLive) Reset() {
	*x = ExpoLive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoLive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoLive) ProtoMessage() {}

func (x *ExpoLive) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoLive.ProtoReflect.Descriptor instead.
func (*ExpoLive) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{69}
}

func (x *ExpoLive) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoLive) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoLive) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ExpoLive) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *ExpoLive) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ExpoLive) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ExpoLive) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ExpoLive) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type AddExpoLiveReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AddExpoLiveReply) Reset() {
	*x = AddExpoLiveReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExpoLiveReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExpoLiveReply) ProtoMessage() {}

func (x *AddExpoLiveReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExpoLiveReply.ProtoReflect.Descriptor instead.
func (*AddExpoLiveReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{70}
}

func (x *AddExpoLiveReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetExpoLiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetExpoLiveRequest) Reset() {
	*x = GetExpoLiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpoLiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpoLiveRequest) ProtoMessage() {}

func (x *GetExpoLiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpoLiveRequest.ProtoReflect.Descriptor instead.
func (*GetExpoLiveRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{71}
}

func (x *GetExpoLiveRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetExpoLiveRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SetExpoLiveEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Enable bool  `protobuf:"varint,3,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *SetExpoLiveEnableRequest) Reset() {
	*x = SetExpoLiveEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetExpoLiveEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetExpoLiveEnableRequest) ProtoMessage() {}

func (x *SetExpoLiveEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetExpoLiveEnableRequest.ProtoReflect.Descriptor instead.
func (*SetExpoLiveEnableRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{72}
}

func (x *SetExpoLiveEnableRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SetExpoLiveEnableRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SetExpoLiveEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListExpoLiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int64 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int64 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ListExpoLiveRequest) Reset() {
	*x = ListExpoLiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoLiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoLiveRequest) ProtoMessage() {}

func (x *ListExpoLiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoLiveRequest.ProtoReflect.Descriptor instead.
func (*ListExpoLiveRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{73}
}

func (x *ListExpoLiveRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ListExpoLiveRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListExpoLiveRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ListExpoLiveReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lives []*ExpoLive `protobuf:"bytes,1,rep,name=lives,json=lives,proto3" json:"lives"`
}

func (x *ListExpoLiveReply) Reset() {
	*x = ListExpoLiveReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExpoLiveReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExpoLiveReply) ProtoMessage() {}

func (x *ListExpoLiveReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExpoLiveReply.ProtoReflect.Descriptor instead.
func (*ListExpoLiveReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{74}
}

func (x *ListExpoLiveReply) GetLives() []*ExpoLive {
	if x != nil {
		return x.Lives
	}
	return nil
}

type DeleteExpoLiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *DeleteExpoLiveRequest) Reset() {
	*x = DeleteExpoLiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExpoLiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExpoLiveRequest) ProtoMessage() {}

func (x *DeleteExpoLiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExpoLiveRequest.ProtoReflect.Descriptor instead.
func (*DeleteExpoLiveRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{75}
}

func (x *DeleteExpoLiveRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteExpoLiveRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type AdminLiveImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64    `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Urls   []string `protobuf:"bytes,2,rep,name=urls,json=urls,proto3" json:"urls"`
}

func (x *AdminLiveImage) Reset() {
	*x = AdminLiveImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminLiveImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminLiveImage) ProtoMessage() {}

func (x *AdminLiveImage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminLiveImage.ProtoReflect.Descriptor instead.
func (*AdminLiveImage) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{76}
}

func (x *AdminLiveImage) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *AdminLiveImage) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type ListLiveImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *ListLiveImageRequest) Reset() {
	*x = ListLiveImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLiveImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLiveImageRequest) ProtoMessage() {}

func (x *ListLiveImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLiveImageRequest.ProtoReflect.Descriptor instead.
func (*ListLiveImageRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{77}
}

func (x *ListLiveImageRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ListLiveImageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AdminLiveImage `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
	Page int32             `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"` // 页码，从1开始
	Size int32             `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"` // 每页数量
}

func (x *ListLiveImageReply) Reset() {
	*x = ListLiveImageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLiveImageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLiveImageReply) ProtoMessage() {}

func (x *ListLiveImageReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLiveImageReply.ProtoReflect.Descriptor instead.
func (*ListLiveImageReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{78}
}

func (x *ListLiveImageReply) GetList() []*AdminLiveImage {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListLiveImageReply) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListLiveImageReply) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type AddLiveImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId       int64    `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`                    // 展会ID
	ImageUrlList []string `protobuf:"bytes,2,rep,name=image_url_list,json=imageUrlList,proto3" json:"image_url_list"` // 图片地址
}

func (x *AddLiveImageRequest) Reset() {
	*x = AddLiveImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLiveImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLiveImageRequest) ProtoMessage() {}

func (x *AddLiveImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLiveImageRequest.ProtoReflect.Descriptor instead.
func (*AddLiveImageRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{79}
}

func (x *AddLiveImageRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *AddLiveImageRequest) GetImageUrlList() []string {
	if x != nil {
		return x.ImageUrlList
	}
	return nil
}

type AddLiveImageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId    int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`          // 展会ID
	SubmitNum int64 `protobuf:"varint,2,opt,name=submit_num,json=submitNum,proto3" json:"submit_num"` //添加数量
	FailNum   int64 `protobuf:"varint,3,opt,name=fail_num,json=failNum,proto3" json:"fail_num"`       //失败数量
}

func (x *AddLiveImageReply) Reset() {
	*x = AddLiveImageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLiveImageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLiveImageReply) ProtoMessage() {}

func (x *AddLiveImageReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLiveImageReply.ProtoReflect.Descriptor instead.
func (*AddLiveImageReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{80}
}

func (x *AddLiveImageReply) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *AddLiveImageReply) GetSubmitNum() int64 {
	if x != nil {
		return x.SubmitNum
	}
	return 0
}

func (x *AddLiveImageReply) GetFailNum() int64 {
	if x != nil {
		return x.FailNum
	}
	return 0
}

type DeleteLiveImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageId uint64 `protobuf:"varint,1,opt,name=image_id,json=imageId,proto3" json:"image_id"` // 图片ID
}

func (x *DeleteLiveImageRequest) Reset() {
	*x = DeleteLiveImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLiveImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLiveImageRequest) ProtoMessage() {}

func (x *DeleteLiveImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLiveImageRequest.ProtoReflect.Descriptor instead.
func (*DeleteLiveImageRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{81}
}

func (x *DeleteLiveImageRequest) GetImageId() uint64 {
	if x != nil {
		return x.ImageId
	}
	return 0
}

type DeleteLiveImageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,json=message,proto3" json:"message"` // 提示信息
}

func (x *DeleteLiveImageReply) Reset() {
	*x = DeleteLiveImageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLiveImageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLiveImageReply) ProtoMessage() {}

func (x *DeleteLiveImageReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLiveImageReply.ProtoReflect.Descriptor instead.
func (*DeleteLiveImageReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{82}
}

func (x *DeleteLiveImageReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SyncLiveImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"` // 展会ID
}

func (x *SyncLiveImagesRequest) Reset() {
	*x = SyncLiveImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncLiveImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncLiveImagesRequest) ProtoMessage() {}

func (x *SyncLiveImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncLiveImagesRequest.ProtoReflect.Descriptor instead.
func (*SyncLiveImagesRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{83}
}

func (x *SyncLiveImagesRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SyncLiveImagesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Started bool   `protobuf:"varint,1,opt,name=started,json=started,proto3" json:"started"`  // 是否成功启动
	Message string `protobuf:"bytes,2,opt,name=message,json=message,proto3" json:"message"`   // 消息
	TaskId  string `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`    // 任务ID
	ExpoId  int64  `protobuf:"varint,4,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`   // 展会ID
	GroupId string `protobuf:"bytes,5,opt,name=group_id,json=groupId,proto3" json:"group_id"` // 人员库ID
}

func (x *SyncLiveImagesReply) Reset() {
	*x = SyncLiveImagesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncLiveImagesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncLiveImagesReply) ProtoMessage() {}

func (x *SyncLiveImagesReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncLiveImagesReply.ProtoReflect.Descriptor instead.
func (*SyncLiveImagesReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{84}
}

func (x *SyncLiveImagesReply) GetStarted() bool {
	if x != nil {
		return x.Started
	}
	return false
}

func (x *SyncLiveImagesReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SyncLiveImagesReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SyncLiveImagesReply) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *SyncLiveImagesReply) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

type GetSyncStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"` // 展会ID
}

func (x *GetSyncStatusRequest) Reset() {
	*x = GetSyncStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSyncStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSyncStatusRequest) ProtoMessage() {}

func (x *GetSyncStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSyncStatusRequest.ProtoReflect.Descriptor instead.
func (*GetSyncStatusRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{85}
}

func (x *GetSyncStatusRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

// 图片同步详情
type ImageSyncItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl     string          `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Action       SyncAction      `protobuf:"varint,2,opt,name=action,json=action,proto3,enum=api.expo.v1.SyncAction" json:"action"`
	Status       ImageSyncStatus `protobuf:"varint,3,opt,name=status,json=status,proto3,enum=api.expo.v1.ImageSyncStatus" json:"status"`
	ErrorMessage string          `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message"`
	FaceCount    int32           `protobuf:"varint,5,opt,name=face_count,json=faceCount,proto3" json:"face_count"`
	ProcessedAt  int64           `protobuf:"varint,6,opt,name=processed_at,json=processedAt,proto3" json:"processed_at"`
	RetryCount   int32           `protobuf:"varint,7,opt,name=retry_count,json=retryCount,proto3" json:"retry_count"`
}

func (x *ImageSyncItem) Reset() {
	*x = ImageSyncItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageSyncItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageSyncItem) ProtoMessage() {}

func (x *ImageSyncItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageSyncItem.ProtoReflect.Descriptor instead.
func (*ImageSyncItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{86}
}

func (x *ImageSyncItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *ImageSyncItem) GetAction() SyncAction {
	if x != nil {
		return x.Action
	}
	return SyncAction_SYNC_ACTION_UNKNOWN
}

func (x *ImageSyncItem) GetStatus() ImageSyncStatus {
	if x != nil {
		return x.Status
	}
	return ImageSyncStatus_IMAGE_SYNC_STATUS_UNKNOWN
}

func (x *ImageSyncItem) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ImageSyncItem) GetFaceCount() int32 {
	if x != nil {
		return x.FaceCount
	}
	return 0
}

func (x *ImageSyncItem) GetProcessedAt() int64 {
	if x != nil {
		return x.ProcessedAt
	}
	return 0
}

func (x *ImageSyncItem) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

// 查询同步状态响应
type GetSyncStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId  int64      `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	GroupId string     `protobuf:"bytes,2,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	Status  SyncStatus `protobuf:"varint,3,opt,name=status,json=status,proto3,enum=api.expo.v1.SyncStatus" json:"status"`
	TaskId  string     `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	// 统计信息
	TotalImages     int32 `protobuf:"varint,5,opt,name=total_images,json=totalImages,proto3" json:"total_images"`
	ProcessedImages int32 `protobuf:"varint,6,opt,name=processed_images,json=processedImages,proto3" json:"processed_images"`
	SuccessCount    int32 `protobuf:"varint,7,opt,name=success_count,json=successCount,proto3" json:"success_count"`
	FailedCount     int32 `protobuf:"varint,8,opt,name=failed_count,json=failedCount,proto3" json:"failed_count"`
	SkippedCount    int32 `protobuf:"varint,9,opt,name=skipped_count,json=skippedCount,proto3" json:"skipped_count"`
	DeletedCount    int32 `protobuf:"varint,10,opt,name=deleted_count,json=deletedCount,proto3" json:"deleted_count"`
	// 时间信息
	StartedAt   int64 `protobuf:"varint,11,opt,name=started_at,json=startedAt,proto3" json:"started_at"`
	CompletedAt int64 `protobuf:"varint,12,opt,name=completed_at,json=completedAt,proto3" json:"completed_at"`
	DurationMs  int64 `protobuf:"varint,13,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms"`
	// 详细信息
	Message string           `protobuf:"bytes,14,opt,name=message,json=message,proto3" json:"message"`
	Items   []*ImageSyncItem `protobuf:"bytes,15,rep,name=items,json=items,proto3" json:"items"`
	// 进度信息
	ProgressPercent float32 `protobuf:"fixed32,16,opt,name=progress_percent,json=progressPercent,proto3" json:"progress_percent"`
	CurrentImage    string  `protobuf:"bytes,17,opt,name=current_image,json=currentImage,proto3" json:"current_image"`
}

func (x *GetSyncStatusReply) Reset() {
	*x = GetSyncStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSyncStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSyncStatusReply) ProtoMessage() {}

func (x *GetSyncStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSyncStatusReply.ProtoReflect.Descriptor instead.
func (*GetSyncStatusReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{87}
}

func (x *GetSyncStatusReply) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetSyncStatusReply) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *GetSyncStatusReply) GetStatus() SyncStatus {
	if x != nil {
		return x.Status
	}
	return SyncStatus_SYNC_STATUS_UNKNOWN
}

func (x *GetSyncStatusReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *GetSyncStatusReply) GetTotalImages() int32 {
	if x != nil {
		return x.TotalImages
	}
	return 0
}

func (x *GetSyncStatusReply) GetProcessedImages() int32 {
	if x != nil {
		return x.ProcessedImages
	}
	return 0
}

func (x *GetSyncStatusReply) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *GetSyncStatusReply) GetFailedCount() int32 {
	if x != nil {
		return x.FailedCount
	}
	return 0
}

func (x *GetSyncStatusReply) GetSkippedCount() int32 {
	if x != nil {
		return x.SkippedCount
	}
	return 0
}

func (x *GetSyncStatusReply) GetDeletedCount() int32 {
	if x != nil {
		return x.DeletedCount
	}
	return 0
}

func (x *GetSyncStatusReply) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *GetSyncStatusReply) GetCompletedAt() int64 {
	if x != nil {
		return x.CompletedAt
	}
	return 0
}

func (x *GetSyncStatusReply) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *GetSyncStatusReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetSyncStatusReply) GetItems() []*ImageSyncItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GetSyncStatusReply) GetProgressPercent() float32 {
	if x != nil {
		return x.ProgressPercent
	}
	return 0
}

func (x *GetSyncStatusReply) GetCurrentImage() string {
	if x != nil {
		return x.CurrentImage
	}
	return ""
}

type FacePhotoUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhotoUrl string `protobuf:"bytes,1,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url"` // 展会照片URL
	ExpoId   int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`      // 展会场景ID
	Operator string `protobuf:"bytes,3,opt,name=operator,json=operator,proto3" json:"operator"`   // 操作员ID
}

func (x *FacePhotoUploadRequest) Reset() {
	*x = FacePhotoUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FacePhotoUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FacePhotoUploadRequest) ProtoMessage() {}

func (x *FacePhotoUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FacePhotoUploadRequest.ProtoReflect.Descriptor instead.
func (*FacePhotoUploadRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{88}
}

func (x *FacePhotoUploadRequest) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *FacePhotoUploadRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *FacePhotoUploadRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

// 人脸照片上传响应
type FacePhotoUploadReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadId         string            `protobuf:"bytes,1,opt,name=upload_id,json=uploadId,proto3" json:"upload_id"`                           // 上传批次ID
	OriginalPhotoUrl string            `protobuf:"bytes,2,opt,name=original_photo_url,json=originalPhotoUrl,proto3" json:"original_photo_url"` // 原始照片URL
	TotalFaces       int32             `protobuf:"varint,3,opt,name=total_faces,json=totalFaces,proto3" json:"total_faces"`                    // 检测到的总人脸数
	SuccessFaces     int32             `protobuf:"varint,4,opt,name=success_faces,json=successFaces,proto3" json:"success_faces"`              // 成功入库的人脸数
	FailedFaces      int32             `protobuf:"varint,5,opt,name=failed_faces,json=failedFaces,proto3" json:"failed_faces"`                 // 失败的人脸数
	Faces            []*FaceUploadItem `protobuf:"bytes,6,rep,name=faces,json=faces,proto3" json:"faces"`                                      // 人脸处理结果列表
}

func (x *FacePhotoUploadReply) Reset() {
	*x = FacePhotoUploadReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FacePhotoUploadReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FacePhotoUploadReply) ProtoMessage() {}

func (x *FacePhotoUploadReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FacePhotoUploadReply.ProtoReflect.Descriptor instead.
func (*FacePhotoUploadReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{89}
}

func (x *FacePhotoUploadReply) GetUploadId() string {
	if x != nil {
		return x.UploadId
	}
	return ""
}

func (x *FacePhotoUploadReply) GetOriginalPhotoUrl() string {
	if x != nil {
		return x.OriginalPhotoUrl
	}
	return ""
}

func (x *FacePhotoUploadReply) GetTotalFaces() int32 {
	if x != nil {
		return x.TotalFaces
	}
	return 0
}

func (x *FacePhotoUploadReply) GetSuccessFaces() int32 {
	if x != nil {
		return x.SuccessFaces
	}
	return 0
}

func (x *FacePhotoUploadReply) GetFailedFaces() int32 {
	if x != nil {
		return x.FailedFaces
	}
	return 0
}

func (x *FacePhotoUploadReply) GetFaces() []*FaceUploadItem {
	if x != nil {
		return x.Faces
	}
	return nil
}

// 人脸上传结果项
type FaceUploadItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FaceId       string    `protobuf:"bytes,1,opt,name=face_id,json=faceId,proto3" json:"face_id"`                     // 业务人脸ID
	FaceUrl      string    `protobuf:"bytes,2,opt,name=face_url,json=faceUrl,proto3" json:"face_url"`                  // 人脸截图URL
	QualityScore float32   `protobuf:"fixed32,3,opt,name=quality_score,json=qualityScore,proto3" json:"quality_score"` // 质量分数
	Rect         *FaceRect `protobuf:"bytes,4,opt,name=rect,json=rect,proto3" json:"rect"`                             // 人脸在原图中的坐标
	Status       string    `protobuf:"bytes,5,opt,name=status,json=status,proto3" json:"status"`                       // 入库状态
	ErrorMessage string    `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message"`   // 错误信息
}

func (x *FaceUploadItem) Reset() {
	*x = FaceUploadItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceUploadItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceUploadItem) ProtoMessage() {}

func (x *FaceUploadItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceUploadItem.ProtoReflect.Descriptor instead.
func (*FaceUploadItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{90}
}

func (x *FaceUploadItem) GetFaceId() string {
	if x != nil {
		return x.FaceId
	}
	return ""
}

func (x *FaceUploadItem) GetFaceUrl() string {
	if x != nil {
		return x.FaceUrl
	}
	return ""
}

func (x *FaceUploadItem) GetQualityScore() float32 {
	if x != nil {
		return x.QualityScore
	}
	return 0
}

func (x *FaceUploadItem) GetRect() *FaceRect {
	if x != nil {
		return x.Rect
	}
	return nil
}

func (x *FaceUploadItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FaceUploadItem) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// 展会信息
type ExhibitionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExhibitionId   string `protobuf:"bytes,1,opt,name=exhibition_id,json=exhibitionId,proto3" json:"exhibition_id"`       // 展会ID
	ExhibitionName string `protobuf:"bytes,2,opt,name=exhibition_name,json=exhibitionName,proto3" json:"exhibition_name"` // 展会名称
	ExhibitionDate string `protobuf:"bytes,3,opt,name=exhibition_date,json=exhibitionDate,proto3" json:"exhibition_date"` // 展会日期
}

func (x *ExhibitionInfo) Reset() {
	*x = ExhibitionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitionInfo) ProtoMessage() {}

func (x *ExhibitionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitionInfo.ProtoReflect.Descriptor instead.
func (*ExhibitionInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{91}
}

func (x *ExhibitionInfo) GetExhibitionId() string {
	if x != nil {
		return x.ExhibitionId
	}
	return ""
}

func (x *ExhibitionInfo) GetExhibitionName() string {
	if x != nil {
		return x.ExhibitionName
	}
	return ""
}

func (x *ExhibitionInfo) GetExhibitionDate() string {
	if x != nil {
		return x.ExhibitionDate
	}
	return ""
}

// 创建人员库请求
type FaceGroupCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`                        // 人员库名称
	Description string `protobuf:"bytes,2,opt,name=description,json=description,proto3" json:"description"`   // 描述
	ExpoId      int64  `protobuf:"varint,4,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`               // 场景ID
	MaxFaceNum  int32  `protobuf:"varint,5,opt,name=max_face_num,json=maxFaceNum,proto3" json:"max_face_num"` // 最大人脸数
}

func (x *FaceGroupCreateRequest) Reset() {
	*x = FaceGroupCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupCreateRequest) ProtoMessage() {}

func (x *FaceGroupCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupCreateRequest.ProtoReflect.Descriptor instead.
func (*FaceGroupCreateRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{92}
}

func (x *FaceGroupCreateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FaceGroupCreateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FaceGroupCreateRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *FaceGroupCreateRequest) GetMaxFaceNum() int32 {
	if x != nil {
		return x.MaxFaceNum
	}
	return 0
}

// 创建人员库响应
type FaceGroupCreateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId            string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`                                     // 生成的人员库ID
	Name               string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`                                                // 人员库名称
	Description        string `protobuf:"bytes,3,opt,name=description,json=description,proto3" json:"description"`                           // 描述
	FaceModelVersion   string `protobuf:"bytes,4,opt,name=face_model_version,json=faceModelVersion,proto3" json:"face_model_version"`        // 算法模型版本
	MaxFaces           int32  `protobuf:"varint,5,opt,name=max_faces,json=maxFaces,proto3" json:"max_faces"`                                 // 最大人脸数量
	EstimatedFaceCount int32  `protobuf:"varint,6,opt,name=estimated_face_count,json=estimatedFaceCount,proto3" json:"estimated_face_count"` // 预估当前人脸数量
	Status             int32  `protobuf:"varint,7,opt,name=status,json=status,proto3" json:"status"`                                         // 状态
	CreatedAt          string `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at"`                               // 创建时间
}

func (x *FaceGroupCreateReply) Reset() {
	*x = FaceGroupCreateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupCreateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupCreateReply) ProtoMessage() {}

func (x *FaceGroupCreateReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupCreateReply.ProtoReflect.Descriptor instead.
func (*FaceGroupCreateReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{93}
}

func (x *FaceGroupCreateReply) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *FaceGroupCreateReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FaceGroupCreateReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FaceGroupCreateReply) GetFaceModelVersion() string {
	if x != nil {
		return x.FaceModelVersion
	}
	return ""
}

func (x *FaceGroupCreateReply) GetMaxFaces() int32 {
	if x != nil {
		return x.MaxFaces
	}
	return 0
}

func (x *FaceGroupCreateReply) GetEstimatedFaceCount() int32 {
	if x != nil {
		return x.EstimatedFaceCount
	}
	return 0
}

func (x *FaceGroupCreateReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FaceGroupCreateReply) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

// 获取人员库信息请求
type FaceGroupInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"` // 人员库ID
}

func (x *FaceGroupInfoRequest) Reset() {
	*x = FaceGroupInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupInfoRequest) ProtoMessage() {}

func (x *FaceGroupInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupInfoRequest.ProtoReflect.Descriptor instead.
func (*FaceGroupInfoRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{94}
}

func (x *FaceGroupInfoRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

// 根据场景获取人员库请求
type FaceGroupsByExpoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"` // 场景ID
}

func (x *FaceGroupsByExpoRequest) Reset() {
	*x = FaceGroupsByExpoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupsByExpoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupsByExpoRequest) ProtoMessage() {}

func (x *FaceGroupsByExpoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupsByExpoRequest.ProtoReflect.Descriptor instead.
func (*FaceGroupsByExpoRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{95}
}

func (x *FaceGroupsByExpoRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

// 获取人员库列表请求
type FaceGroupListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"` // 页码
	Size int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"` // 每页数量
}

func (x *FaceGroupListRequest) Reset() {
	*x = FaceGroupListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupListRequest) ProtoMessage() {}

func (x *FaceGroupListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupListRequest.ProtoReflect.Descriptor instead.
func (*FaceGroupListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{96}
}

func (x *FaceGroupListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FaceGroupListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

// 人员库列表响应
type FaceGroupListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*FaceGroupInfo `protobuf:"bytes,1,rep,name=groups,json=groups,proto3" json:"groups"` // 人员库列表
	Total  int32            `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`   // 总数
	Page   int32            `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`      // 当前页
	Size   int32            `protobuf:"varint,4,opt,name=size,json=size,proto3" json:"size"`      // 每页数量
}

func (x *FaceGroupListReply) Reset() {
	*x = FaceGroupListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupListReply) ProtoMessage() {}

func (x *FaceGroupListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupListReply.ProtoReflect.Descriptor instead.
func (*FaceGroupListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{97}
}

func (x *FaceGroupListReply) GetGroups() []*FaceGroupInfo {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *FaceGroupListReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *FaceGroupListReply) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FaceGroupListReply) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

// 人员库信息
type FaceGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId            string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`                                     // 人员库ID
	Name               string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`                                                // 人员库名称
	Description        string `protobuf:"bytes,3,opt,name=description,json=description,proto3" json:"description"`                           // 描述
	FaceModelVersion   string `protobuf:"bytes,4,opt,name=face_model_version,json=faceModelVersion,proto3" json:"face_model_version"`        // 算法模型版本
	MaxFaces           int32  `protobuf:"varint,5,opt,name=max_faces,json=maxFaces,proto3" json:"max_faces"`                                 // 最大人脸数量
	EstimatedFaceCount int32  `protobuf:"varint,6,opt,name=estimated_face_count,json=estimatedFaceCount,proto3" json:"estimated_face_count"` // 预估当前人脸数量
	Status             int32  `protobuf:"varint,7,opt,name=status,json=status,proto3" json:"status"`                                         // 状态
	CreatedAt          string `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at"`                               // 创建时间
}

func (x *FaceGroupInfo) Reset() {
	*x = FaceGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceGroupInfo) ProtoMessage() {}

func (x *FaceGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceGroupInfo.ProtoReflect.Descriptor instead.
func (*FaceGroupInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{98}
}

func (x *FaceGroupInfo) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *FaceGroupInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FaceGroupInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FaceGroupInfo) GetFaceModelVersion() string {
	if x != nil {
		return x.FaceModelVersion
	}
	return ""
}

func (x *FaceGroupInfo) GetMaxFaces() int32 {
	if x != nil {
		return x.MaxFaces
	}
	return 0
}

func (x *FaceGroupInfo) GetEstimatedFaceCount() int32 {
	if x != nil {
		return x.EstimatedFaceCount
	}
	return 0
}

func (x *FaceGroupInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FaceGroupInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

// 获取照片人脸列表请求
type PhotoFacesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhotoUrl string `protobuf:"bytes,1,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url"` // 原始照片URL
}

func (x *PhotoFacesRequest) Reset() {
	*x = PhotoFacesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhotoFacesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhotoFacesRequest) ProtoMessage() {}

func (x *PhotoFacesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhotoFacesRequest.ProtoReflect.Descriptor instead.
func (*PhotoFacesRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{99}
}

func (x *PhotoFacesRequest) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

// 获取照片人脸列表响应
type PhotoFacesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhotoUrl     string            `protobuf:"bytes,1,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url"`              // 原始照片URL
	TotalFaces   int32             `protobuf:"varint,2,opt,name=total_faces,json=totalFaces,proto3" json:"total_faces"`       // 总人脸数
	SuccessFaces int32             `protobuf:"varint,3,opt,name=success_faces,json=successFaces,proto3" json:"success_faces"` // 成功人脸数
	FailedFaces  int32             `protobuf:"varint,4,opt,name=failed_faces,json=failedFaces,proto3" json:"failed_faces"`    // 失败人脸数
	Faces        []*FaceUploadItem `protobuf:"bytes,5,rep,name=faces,json=faces,proto3" json:"faces"`                         // 人脸列表
}

func (x *PhotoFacesReply) Reset() {
	*x = PhotoFacesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_background_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhotoFacesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhotoFacesReply) ProtoMessage() {}

func (x *PhotoFacesReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_background_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhotoFacesReply.ProtoReflect.Descriptor instead.
func (*PhotoFacesReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_background_proto_rawDescGZIP(), []int{100}
}

func (x *PhotoFacesReply) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *PhotoFacesReply) GetTotalFaces() int32 {
	if x != nil {
		return x.TotalFaces
	}
	return 0
}

func (x *PhotoFacesReply) GetSuccessFaces() int32 {
	if x != nil {
		return x.SuccessFaces
	}
	return 0
}

func (x *PhotoFacesReply) GetFailedFaces() int32 {
	if x != nil {
		return x.FailedFaces
	}
	return 0
}

func (x *PhotoFacesReply) GetFaces() []*FaceUploadItem {
	if x != nil {
		return x.Faces
	}
	return nil
}

var File_expo_v1_background_proto protoreflect.FileDescriptor

var file_expo_v1_background_proto_rawDesc = []byte{
	0x0a, 0x18, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65,
	0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x65, 0x78, 0x70, 0x6f,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x61, 0x0a, 0x0d, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe4, 0xbb, 0x8b,
	0xe7, 0xbb, 0x8d, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x22, 0xd3, 0x08, 0x0a, 0x05, 0x47, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x0b, 0x77, 0x69, 0x6b, 0x69,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x92,
	0x41, 0x09, 0x2a, 0x07, 0x77, 0x69, 0x6b, 0x69, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x77, 0x69, 0x6b,
	0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e,
	0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7,
	0xe5, 0x8c, 0xba, 0xe5, 0x9f, 0x9f, 0xe7, 0xa0, 0x81, 0x52, 0x0d, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x89,
	0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x21,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x2a, 0x0a, 0x09, 0x77, 0x68, 0x61, 0x74, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0x57, 0x68, 0x61, 0x74, 0x73,
	0x41, 0x70, 0x70, 0x52, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x41, 0x70, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe5, 0xbe, 0xae, 0xe4, 0xbf, 0xa1, 0x52, 0x06, 0x77, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x12, 0x29, 0x0a, 0x08, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0x46, 0x61, 0x63, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x52, 0x08, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x12, 0x26, 0x0a,
	0x07, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c,
	0x92, 0x41, 0x09, 0x2a, 0x07, 0x54, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x52, 0x07, 0x74, 0x77,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x69,
	0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0x4c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x69, 0x6e,
	0x12, 0x2c, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x29,
	0x0a, 0x08, 0x74, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0x54, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x08, 0x74, 0x65, 0x6c, 0x65, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x26, 0x0a, 0x07, 0x79, 0x6f, 0x75,
	0x74, 0x75, 0x62, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0x92, 0x41, 0x09, 0x2a,
	0x07, 0x59, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x52, 0x07, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x64, 0x64, 0x69, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x52, 0x65, 0x64, 0x64, 0x69, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x64, 0x64, 0x69, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x70,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x54, 0x69, 0x6b,
	0x74, 0x6f, 0x70, 0x52, 0x06, 0x74, 0x69, 0x6b, 0x74, 0x6f, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4,
	0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5,
	0xbb, 0xba, 0xe4, 0xba, 0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x4f,
	0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf,
	0xad, 0xe8, 0xa8, 0x80, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x1a,
	0x58, 0x0a, 0x0e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1f, 0x0a, 0x0d, 0x41, 0x64, 0x64,
	0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x15, 0x53, 0x65,
	0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x21, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3a,
	0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x3c, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x06,
	0x67, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x06, 0x67, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0x24, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x65,
	0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe4, 0xbb, 0x8b, 0xe7, 0xbb, 0x8d, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x52,
	0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x22, 0xb1, 0x03, 0x0a, 0x0d, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f,
	0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x69, 0x64, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12,
	0x5d, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad,
	0xe8, 0xa8, 0x80, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x29,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94,
	0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe8, 0x80, 0x85, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x60, 0x0a, 0x0e, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x32, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x50, 0x0a,
	0x1d, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22,
	0x35, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x39, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61,
	0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x8f, 0x04, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x26,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x60, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x48, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x31, 0x92, 0x41, 0x2e, 0x2a, 0x2c, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9b, 0x30, 0xef,
	0xbc, 0x9a, 0xe4, 0xb8, 0xbb, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0xef, 0xbc, 0x9b, 0x31, 0xef,
	0xbc, 0x9a, 0xe5, 0x88, 0x86, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88,
	0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4,
	0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x52, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x2e, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x09, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x5b, 0x0a, 0x0e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c, 0x6c,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x51, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02,
	0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x48, 0x61,
	0x6c, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf,
	0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x7e, 0x0a, 0x0f, 0x4c,
	0x69, 0x73, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0,
	0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0xa1, 0xb5, 0xe5,
	0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x3c, 0x0a, 0x0d, 0x4c,
	0x69, 0x73, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61,
	0x6c, 0x6c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x54, 0x0a, 0x11, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x81, 0x05, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18,
	0x49, 0x44, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7,
	0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x98, 0x89,
	0xe5, 0xae, 0xbe, 0x49, 0x44, 0x52, 0x07, 0x67, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x39,
	0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21,
	0x92, 0x41, 0x1e, 0x2a, 0x1c, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x2c, 0xe6, 0x96, 0xb0, 0xe5,
	0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80,
	0x92, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27, 0x92, 0x41, 0x24, 0x2a, 0x22, 0xe5, 0x98,
	0x89, 0xe5, 0xae, 0xbe, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2,
	0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0b, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x22, 0x92, 0x41, 0x1f,
	0x2a, 0x1d, 0x77, 0x69, 0x6b, 0x69, 0xe5, 0x8f, 0xb7, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e,
	0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52,
	0x0a, 0x77, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a,
	0x1f, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2,
	0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92,
	0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a, 0x1c, 0xe9, 0x82, 0xae,
	0xe7, 0xae, 0xb1, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d,
	0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x39, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a, 0x1c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x2c, 0xe6, 0x96,
	0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0,
	0xe9, 0x80, 0x92, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x27, 0x92, 0x41, 0x24, 0x2a, 0x22, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7,
	0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1f, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xe4, 0xba, 0xba, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8,
	0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x22, 0x23, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x68, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe8, 0xae, 0xbf, 0xe5, 0xae, 0xa2, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x3d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x67, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x05, 0x67, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x57, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x44, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x2e, 0x0a, 0x06, 0x67, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x06, 0x67, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x22, 0x41, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70,
	0x6f, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x2c, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x68, 0x65, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68, 0x65, 0x6d,
	0x65, 0x22, 0xb7, 0x02, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1f, 0x92, 0x41, 0x1c, 0x2a, 0x1a, 0xe5, 0x98, 0x89, 0xe5,
	0xae, 0xbe, 0x49, 0x44, 0xef, 0xbc, 0x8c, 0xe4, 0xbf, 0x9d, 0xe5, 0xad, 0x98, 0xe6, 0x97, 0xb6,
	0xe5, 0xbf, 0x85, 0xe4, 0xbc, 0xa0, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a, 0x1c, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4,
	0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x43, 0x0a, 0x0b, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x22, 0x92, 0x41, 0x1f, 0x2a, 0x1d, 0x77, 0x69, 0x6b,
	0x69, 0xe5, 0x8f, 0xb7, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8,
	0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x0a, 0x77, 0x69, 0x6b, 0x69,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1f, 0xe6, 0x89, 0x8b, 0xe6,
	0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x2c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4,
	0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a, 0x1c, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x2c, 0xe6,
	0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc,
	0xa0, 0xe9, 0x80, 0x92, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xbe, 0x06, 0x0a, 0x10,
	0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x35, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x25, 0x92, 0x41,
	0x22, 0x2a, 0x20, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0xef, 0xbc, 0x8c, 0xe5, 0x88,
	0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8, 0xe4, 0xbc, 0xa0,
	0xe9, 0x80, 0x92, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x49, 0x44, 0x52,
	0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x68, 0x65,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe4,
	0xb8, 0xbb, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x58, 0x0a, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x42, 0x22, 0x92, 0x41,
	0x1f, 0x2a, 0x1d, 0xe4, 0xb8, 0xbb, 0xe6, 0x8c, 0x81, 0xe4, 0xba, 0xba, 0xef, 0xbc, 0x8c, 0xe4,
	0xbf, 0x9d, 0xe5, 0xad, 0x98, 0xe6, 0x97, 0xb6, 0xe5, 0x8f, 0xaa, 0xe4, 0xbc, 0xa0, 0x49, 0x44,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x08, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x52, 0x08, 0x73,
	0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80,
	0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x23, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x5a, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x5f, 0x0a, 0x0e, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x26, 0x0a, 0x14,
	0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x5f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65,
	0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49,
	0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1,
	0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9,
	0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x54,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x22, 0x62, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78,
	0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44,
	0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x15, 0x45, 0x78, 0x68, 0x69,
	0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x17,
	0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x90, 0x08, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x6f,
	0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbc, 0x9a, 0xe5,
	0x9c, 0xba, 0x49, 0x44, 0x52, 0x06, 0x68, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x0b,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe7, 0xbc,
	0x96, 0xe7, 0xa0, 0x81, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x32, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe5,
	0x95, 0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a,
	0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x3a, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe5, 0xb0, 0x8f, 0x6c,
	0x6f, 0x67, 0x6f, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x4c, 0x6f,
	0x67, 0x6f, 0x12, 0x51, 0x0a, 0x0d, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb5, 0x9e, 0xe5, 0x8a,
	0xa9, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x52, 0x0c, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x34, 0x0a, 0x0c, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x5f, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbd, 0x8d, 0xe9, 0x95, 0xbf, 0xe5, 0xba, 0xa6, 0x52, 0x0b,
	0x62, 0x6f, 0x6f, 0x74, 0x68, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x32, 0x0a, 0x0b, 0x62,
	0x6f, 0x6f, 0x74, 0x68, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbd, 0x8d, 0xe5, 0xae, 0xbd,
	0xe5, 0xba, 0xa6, 0x52, 0x0a, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x34, 0x0a, 0x0c, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbd, 0x8d, 0xe9, 0xab, 0x98, 0xe5, 0xba, 0xa6, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbd,
	0x8d, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09,
	0xe8, 0x81, 0x94, 0xe7, 0xb3, 0xbb, 0xe4, 0xba, 0xba, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x12, 0x39, 0x0a, 0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe7, 0x94, 0xb5, 0xe8, 0xaf, 0x9d, 0xe5, 0x8c, 0xba, 0xe5, 0x9f, 0x9f, 0x52, 0x0d,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe7, 0x94, 0xb5, 0xe8, 0xaf, 0x9d, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4, 0xba,
	0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x4d, 0x0a, 0x09, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x68, 0x69,
	0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x91, 0x98, 0xe5, 0xb7, 0xa5, 0x52, 0x09,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x8e,
	0x92, 0xe5, 0xba, 0x8f, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0x30, 0x0a, 0x15, 0x41, 0x64,
	0x64, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x60, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b,
	0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x8b,
	0x01, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69,
	0x74, 0x6f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41,
	0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49,
	0x64, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90,
	0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x8a, 0x01, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x58, 0x0a, 0x16, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x0a, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69,
	0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74,
	0x6f, 0x72, 0x73, 0x22, 0x63, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44,
	0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x86, 0x02, 0x0a, 0x0d, 0x45, 0x78, 0x70,
	0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0x8c, 0x87,
	0xe5, 0x8d, 0x97, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x9c, 0xb0, 0xe5, 0x9b, 0xbe, 0x75,
	0x72, 0x6c, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5,
	0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4, 0xba, 0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x22, 0x2c, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x56, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf,
	0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0x3e, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49,
	0x64, 0x22, 0x46, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69,
	0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x59, 0x0a, 0x16, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x4f, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x51, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x50,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x84, 0x03, 0x0a, 0x0f, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04,
	0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x25, 0x0a, 0x07, 0x77, 0x65,
	0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe7, 0xbd, 0x91, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0x52, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x69, 0x64, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb,
	0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba,
	0xe4, 0xba, 0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x2e, 0x0a, 0x13,
	0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x5e, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x89, 0x01, 0x0a,
	0x1b, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49,
	0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x69, 0x64,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x85,
	0xb3, 0xe9, 0x94, 0xae, 0xe5, 0xad, 0x97, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0x50, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x73, 0x22, 0x61, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f,
	0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65,
	0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe1, 0x04,
	0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41,
	0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x2e, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa7, 0x86, 0xe9, 0xa2, 0x91, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12,
	0x2e, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x58, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x09,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f,
	0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09,
	0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4, 0xba, 0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x1a, 0x5d, 0x0a, 0x0e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x2d, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x57, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49,
	0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x1a, 0x53, 0x65,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49,
	0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0,
	0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f, 0xe9,
	0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x48,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x60, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0xbd, 0x02, 0x0a, 0x08, 0x45,
	0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44,
	0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xba,
	0xa7, 0xe5, 0x88, 0xab, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x05, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe5, 0xb0, 0x81, 0xe9, 0x9d, 0xa2, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x28, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4, 0xba,
	0xba, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x2b, 0x0a, 0x10, 0x41, 0x64,
	0x64, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a,
	0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x55, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02,
	0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x86,
	0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0xaf, 0xe7, 0x94, 0xa8, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7,
	0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f,
	0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22,
	0x4d, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x22, 0x5e,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87,
	0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0x69, 0x64, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x5f,
	0x0a, 0x0e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x69, 0x64,
	0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x22,
	0x3e, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x69, 0x64, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22,
	0x80, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x22, 0x54, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x55, 0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x66, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x4c,
	0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x4e, 0x75, 0x6d,
	0x22, 0x33, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x30, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c,
	0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5b, 0x0a, 0x15, 0x53, 0x79, 0x6e, 0x63, 0x4c,
	0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x42, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x29, 0x92, 0x41, 0x26, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44,
	0x32, 0x1a, 0xe8, 0xa6, 0x81, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe5, 0x9b, 0xbe, 0xe7, 0x89,
	0x87, 0xe7, 0x9a, 0x84, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x49, 0x64, 0x22, 0xe9, 0x02, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x4c, 0x69, 0x76,
	0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x51, 0x0a, 0x07,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x37, 0x92,
	0x41, 0x34, 0x2a, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f,
	0xe5, 0x90, 0xaf, 0xe5, 0x8a, 0xa8, 0x32, 0x1e, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f,
	0xe5, 0x90, 0xaf, 0xe5, 0x8a, 0xa8, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x12,
	0x45, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x0c, 0xe5, 0x93, 0x8d, 0xe5, 0xba, 0x94, 0xe6, 0xb6, 0x88,
	0xe6, 0x81, 0xaf, 0x32, 0x18, 0xe5, 0x90, 0xaf, 0xe5, 0x8a, 0xa8, 0xe7, 0xbb, 0x93, 0xe6, 0x9e,
	0x9c, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x2a, 0x08, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x32, 0x1b, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x9a, 0x84, 0xe5, 0x94, 0xaf, 0xe4, 0xb8, 0x80, 0xe6, 0xa0, 0x87,
	0xe8, 0xaf, 0x86, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x65,
	0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x32, 0x08, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x41, 0x0a,
	0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x26, 0x92, 0x41, 0x23, 0x2a, 0x0b, 0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0x49,
	0x44, 0x32, 0x14, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a, 0x84, 0xe4, 0xba, 0xba, 0xe5,
	0x91, 0x98, 0xe5, 0xba, 0x93, 0x49, 0x44, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x22, 0x60, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2f, 0x92, 0x41, 0x2c, 0x2a, 0x08,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x32, 0x20, 0xe8, 0xa6, 0x81, 0xe6, 0x9f, 0xa5,
	0xe8, 0xaf, 0xa2, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe7,
	0x9a, 0x84, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x22, 0xc4, 0x04, 0x0a, 0x0d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x79, 0x6e, 0x63,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x39, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0x92, 0x41, 0x19, 0x2a, 0x09, 0xe5, 0x9b,
	0xbe, 0xe7, 0x89, 0x87, 0x55, 0x52, 0x4c, 0x32, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x65, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x34, 0x92, 0x41, 0x31, 0x2a, 0x0c, 0xe5,
	0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0x32, 0x21, 0xe5, 0xaf, 0xb9,
	0xe8, 0xaf, 0xa5, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0xe7,
	0x9a, 0x84, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x28, 0x92, 0x41, 0x25, 0x2a, 0x0c, 0xe5, 0xa4, 0x84, 0xe7, 0x90,
	0x86, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x32, 0x15, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7,
	0x9a, 0x84, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0x92,
	0x41, 0x2e, 0x2a, 0x0c, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x32, 0x1e, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe5, 0xa4, 0xb1, 0xe8, 0xb4, 0xa5, 0xe6, 0x97,
	0xb6, 0xe7, 0x9a, 0x84, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4a,
	0x0a, 0x0a, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x0c, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe6,
	0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x32, 0x18, 0xe6, 0xa3, 0x80, 0xe6, 0xb5, 0x8b, 0xe5, 0x88, 0xb0,
	0xe7, 0x9a, 0x84, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52,
	0x09, 0x66, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x28, 0x92, 0x41, 0x25, 0x2a, 0x0c, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe6, 0x97, 0xb6,
	0xe9, 0x97, 0xb4, 0x32, 0x15, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe5, 0xae, 0x8c, 0xe6, 0x88,
	0x90, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1f, 0x92, 0x41,
	0x1c, 0x2a, 0x0c, 0xe9, 0x87, 0x8d, 0xe8, 0xaf, 0x95, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x32,
	0x0c, 0xe9, 0x87, 0x8d, 0xe8, 0xaf, 0x95, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0a, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xcf, 0x0a, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x30, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44,
	0x32, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x12, 0x41, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0x41, 0x23, 0x2a, 0x0b, 0xe4, 0xba, 0xba, 0xe5, 0x91,
	0x98, 0xe5, 0xba, 0x93, 0x49, 0x44, 0x32, 0x14, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe7, 0x9a,
	0x84, 0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0x49, 0x44, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x25,
	0x92, 0x41, 0x22, 0x2a, 0x0c, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80,
	0x81, 0x32, 0x12, 0xe6, 0x95, 0xb4, 0xe4, 0xbd, 0x93, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d,
	0x92, 0x41, 0x1a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x32, 0x0e, 0xe5,
	0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2e, 0x92, 0x41, 0x2b,
	0x2a, 0x0c, 0xe6, 0x80, 0xbb, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x95, 0xb0, 0x32, 0x1b,
	0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe7, 0x9a, 0x84, 0xe6,
	0x80, 0xbb, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x95, 0xb0, 0x52, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x28, 0x92, 0x41, 0x25, 0x2a, 0x0c, 0xe5, 0xb7, 0xb2, 0xe5, 0xa4, 0x84, 0xe7,
	0x90, 0x86, 0xe6, 0x95, 0xb0, 0x32, 0x15, 0xe5, 0xb7, 0xb2, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86,
	0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x50, 0x0a,
	0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x0c, 0xe6, 0x88, 0x90, 0xe5, 0x8a,
	0x9f, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x32, 0x18, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0xe5,
	0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x95,
	0xb0, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x4e, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x0c, 0xe5, 0xa4, 0xb1, 0xe8,
	0xb4, 0xa5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x32, 0x18, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86,
	0xe5, 0xa4, 0xb1, 0xe8, 0xb4, 0xa5, 0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6,
	0x95, 0xb0, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x4a, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x0c, 0xe8, 0xb7, 0xb3,
	0xe8, 0xbf, 0x87, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x32, 0x12, 0xe8, 0xb7, 0xb3, 0xe8, 0xbf,
	0x87, 0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x73,
	0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0d, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x0c, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe6,
	0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x32, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe7, 0x9a, 0x84,
	0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x28, 0x92, 0x41, 0x25,
	0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x32, 0x15,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x4b, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x28, 0x92, 0x41, 0x25, 0x2a, 0x0c, 0xe5, 0xae, 0x8c,
	0xe6, 0x88, 0x90, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x32, 0x15, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3,
	0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x52, 0x0a,
	0x0b, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x31, 0x92, 0x41, 0x2e, 0x2a, 0x0c, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0xe6,
	0x97, 0xb6, 0xe9, 0x95, 0xbf, 0x32, 0x1e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x89, 0xa7,
	0xe8, 0xa1, 0x8c, 0xe6, 0x97, 0xb6, 0xe9, 0x95, 0xbf, 0xef, 0xbc, 0x88, 0xe6, 0xaf, 0xab, 0xe7,
	0xa7, 0x92, 0xef, 0xbc, 0x89, 0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x73, 0x12, 0x48, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x2a, 0x0c, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe6,
	0xb6, 0x88, 0xe6, 0x81, 0xaf, 0x32, 0x1b, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0xe7, 0x9a, 0x84, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6,
	0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x60, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x79,
	0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x32, 0x1b, 0xe6, 0xaf, 0x8f, 0xe4, 0xb8,
	0xaa, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7, 0x9a, 0x84, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5,
	0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x5e, 0x0a,
	0x10, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x42, 0x33, 0x92, 0x41, 0x30, 0x2a, 0x0f, 0xe8, 0xbf,
	0x9b, 0xe5, 0xba, 0xa6, 0xe7, 0x99, 0xbe, 0xe5, 0x88, 0x86, 0xe6, 0xaf, 0x94, 0x32, 0x1d, 0xe5,
	0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0xe7, 0x99, 0xbe, 0xe5, 0x88,
	0x86, 0xe6, 0xaf, 0x94, 0x20, 0x28, 0x30, 0x2d, 0x31, 0x30, 0x30, 0x29, 0x52, 0x0f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x5c, 0x0a,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x37, 0x92, 0x41, 0x34, 0x2a, 0x12, 0xe5, 0xbd, 0x93, 0xe5, 0x89,
	0x8d, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x32, 0x1e, 0xe5,
	0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe6, 0xad, 0xa3, 0xe5, 0x9c, 0xa8, 0xe5, 0xa4, 0x84, 0xe7, 0x90,
	0x86, 0xe7, 0x9a, 0x84, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x55, 0x52, 0x4c, 0x52, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x6a, 0x0a, 0x16, 0x46,
	0x61, 0x63, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55,
	0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x22, 0xfd, 0x01, 0x0a, 0x14, 0x46, 0x61, 0x63, 0x65,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x61, 0x63, 0x65,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x66, 0x61, 0x63, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x46,
	0x61, 0x63, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x05, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x66, 0x61, 0x63, 0x65, 0x73, 0x22, 0xd1, 0x01, 0x0a, 0x0e, 0x46, 0x61, 0x63, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x61, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x61, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x23,
	0x0a, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x72, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x61, 0x63, 0x65, 0x52, 0x65, 0x63, 0x74, 0x52, 0x04, 0x72, 0x65, 0x63, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x0e,
	0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78,
	0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x16, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x46, 0x61, 0x63, 0x65, 0x4e, 0x75,
	0x6d, 0x22, 0x9b, 0x02, 0x0a, 0x14, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x61, 0x63, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78,
	0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61,
	0x78, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x61, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x31, 0x0a, 0x14, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x22, 0x32, 0x0a, 0x17, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x42, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x14, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x46, 0x61, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a,
	0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22,
	0x94, 0x02, 0x0a, 0x0d, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x66, 0x61, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12, 0x30, 0x0a,
	0x14, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x46, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x30, 0x0a, 0x11, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x46,
	0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0xca, 0x01, 0x0a, 0x0f, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x46, 0x61, 0x63, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x46, 0x61, 0x63,
	0x65, 0x73, 0x12, 0x31, 0x0a, 0x05, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x61, 0x63, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x66, 0x61, 0x63, 0x65, 0x73, 0x2a, 0x3b, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x55, 0x42,
	0x10, 0x01, 0x2a, 0x29, 0x0a, 0x0c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x00, 0x2a, 0x40, 0x0a,
	0x0e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x15, 0x0a, 0x11, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x43, 0x54, 0x55, 0x52, 0x45, 0x10, 0x01, 0x2a,
	0xa9, 0x01, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17,
	0x0a, 0x13, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x59, 0x4e, 0x43, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x19, 0x0a,
	0x15, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x59, 0x4e, 0x43,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x19, 0x0a, 0x15, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xec, 0x01, 0x0a, 0x0f,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x19, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1d,
	0x0a, 0x19, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x20, 0x0a,
	0x1c, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12,
	0x1d, 0x0a, 0x19, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1c,
	0x0a, 0x18, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19,
	0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x49,
	0x4d, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x68, 0x0a, 0x0a, 0x53, 0x79,
	0x6e, 0x63, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x59, 0x4e, 0x43,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x12, 0x14,
	0x0a, 0x10, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4b,
	0x49, 0x50, 0x10, 0x03, 0x32, 0xe6, 0x55, 0x0a, 0x0a, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x12, 0x47, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x14,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x12, 0xa1, 0x01, 0x0a,
	0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7,
	0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x18, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0xb7, 0x01, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x65, 0x92, 0x41, 0x3f, 0x0a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87,
	0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x1a, 0x1b, 0xe4, 0xb8, 0xba, 0xe6,
	0x8c, 0x87, 0xe5, 0xae, 0x9a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a,
	0xa0, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a,
	0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x12, 0xc3, 0x01, 0x0a, 0x0f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x68, 0x92, 0x41, 0x3f, 0x0a, 0x0c, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99,
	0xa4, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x1a, 0x1b, 0xe5,
	0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe6, 0x8c, 0x87, 0xe5, 0xae, 0x9a, 0xe7, 0x9a, 0x84, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20,
	0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x12, 0x91, 0x02, 0x0a, 0x0e, 0x53, 0x79, 0x6e, 0x63, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xb8, 0x01, 0x92, 0x41, 0x8f, 0x01,
	0x0a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x24,
	0xe6, 0x89, 0x8b, 0xe5, 0x8a, 0xa8, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x88, 0xb0, 0xe4, 0xba, 0xba, 0xe5, 0x91,
	0x98, 0xe5, 0xba, 0x93, 0x1a, 0x59, 0xe6, 0x89, 0x8b, 0xe5, 0x8a, 0xa8, 0xe8, 0xa7, 0xa6, 0xe5,
	0x8f, 0x91, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x90,
	0x8c, 0xe6, 0xad, 0xa5, 0xe5, 0x88, 0xb0, 0xe8, 0x85, 0xbe, 0xe8, 0xae, 0xaf, 0xe4, 0xba, 0x91,
	0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0xef, 0xbc, 0x8c, 0xe5, 0xbc, 0x82, 0xe6,
	0xad, 0xa5, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0xef, 0xbc, 0x8c, 0xe7, 0xab, 0x8b, 0xe5, 0x8d,
	0xb3, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x12, 0xe1, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x79, 0x6e, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x8b, 0x01, 0x92, 0x41, 0x6c,
	0x0a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x1e,
	0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7,
	0x89, 0x87, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x1a, 0x3c,
	0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7,
	0x89, 0x87, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x9a,
	0x84, 0xe8, 0xaf, 0xa6, 0xe7, 0xbb, 0x86, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe5, 0x92, 0x8c,
	0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x79, 0x6e,
	0x63, 0x2d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x73, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0x98, 0x89, 0xe5, 0xae,
	0xbe, 0x12, 0x0c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x75, 0x0a,
	0x08, 0x47, 0x65, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x22, 0x37, 0x92, 0x41, 0x16,
	0x0a, 0x06, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x0c, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2,
	0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x71, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x92, 0x41, 0x16,
	0x0a, 0x06, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0,
	0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x1a,
	0x16, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74,
	0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x47, 0x75, 0x65, 0x73,
	0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x40, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12,
	0x12, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x1a, 0x16, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3b,
	0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x12, 0xe6, 0x9f, 0xa5,
	0xe8, 0xaf, 0xa2, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x7b, 0x0a, 0x0b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x37, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x0c, 0xe5, 0x88,
	0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18,
	0x2a, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x75, 0x65, 0x73,
	0x74, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x64,
	0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92,
	0x41, 0x1c, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0xa4, 0xbe, 0xe5, 0x8c, 0xba,
	0x12, 0x0c, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x9c, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x22, 0x46, 0x92, 0x41,
	0x1c, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0xa4, 0xbe, 0xe5, 0x8c, 0xba, 0x12,
	0x0c, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65,
	0x78, 0x70, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x90, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x43,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x49, 0x92, 0x41,
	0x1c, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0xa4, 0xbe, 0xe5, 0x8c, 0xba, 0x12,
	0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x1a, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa9, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x4f, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7,
	0xa4, 0xbe, 0xe5, 0x8c, 0xba, 0x12, 0x12, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a,
	0x01, 0x2a, 0x1a, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78,
	0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x45, 0x78, 0x70, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x1c, 0x0a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0xa4, 0xbe, 0xe5, 0x8c, 0xba, 0x12, 0x0c, 0xe5, 0x88, 0xa0,
	0xe9, 0x99, 0xa4, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x2a,
	0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x12, 0x79, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x15, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61,
	0x6c, 0x6c, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3b,
	0x92, 0x41, 0x16, 0x0a, 0x06, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x12, 0x0c, 0xe6, 0x96, 0xb0,
	0xe5, 0xa2, 0x9e, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a,
	0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x7a, 0x0a, 0x07, 0x47,
	0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x22, 0x3b, 0x92, 0x41, 0x16, 0x0a,
	0x06, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x12, 0x0c, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c,
	0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x77, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x48, 0x61, 0x6c, 0x6c, 0x1a, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x3e, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x12, 0x0c, 0xe6,
	0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1f, 0x3a, 0x01, 0x2a, 0x1a, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x65, 0x78, 0x70, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x8c, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x44, 0x92, 0x41, 0x1c, 0x0a, 0x06,
	0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x12, 0x12, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe4, 0xbc,
	0x9a, 0xe5, 0x9c, 0xba, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x3a, 0x01, 0x2a, 0x1a, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65,
	0x78, 0x70, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x2f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x85, 0x01, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x61, 0x6c,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3f, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9c, 0xba, 0x12, 0x12, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c,
	0xba, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x68, 0x61,
	0x6c, 0x6c, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x7d, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x48, 0x61, 0x6c, 0x6c, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3b, 0x92, 0x41, 0x16, 0x0a, 0x06,
	0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xba, 0x12, 0x0c, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe4, 0xbc,
	0x9a, 0xe5, 0x9c, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x2a, 0x1a, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x2f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x45, 0x78,
	0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x48, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x98, 0x89, 0xe5,
	0xae, 0xbe, 0x12, 0x12, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22,
	0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x92, 0x01, 0x0a, 0x0c, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x48, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70,
	0x6f, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x9b,
	0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x12, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70,
	0x6f, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x94, 0x01, 0x0a,
	0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x22, 0x0a, 0x0c,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x12, 0x12, 0xe5, 0x88,
	0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x2a, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x67, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4b, 0x92, 0x41, 0x22, 0x0a, 0x0c,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x12, 0x12, 0xe6, 0xb7,
	0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x12, 0xa2, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4b,
	0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8,
	0x8b, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8,
	0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x97, 0x01, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x12, 0x12, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x23, 0x3a, 0x01, 0x2a, 0x1a, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x4f, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x9d, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4b, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x12, 0x12, 0xe5, 0x88, 0xa0,
	0xe9, 0x99, 0xa4, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x20, 0x2a, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70,
	0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68,
	0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f,
	0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x43,
	0x92, 0x41, 0x19, 0x0a, 0x09, 0xe5, 0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x12, 0x0c,
	0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f,
	0x61, 0x64, 0x64, 0x12, 0xa3, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45,
	0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78,
	0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x49,
	0x92, 0x41, 0x1f, 0x0a, 0x09, 0xe5, 0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x12, 0x12,
	0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe8, 0xaf, 0xa6, 0xe6,
	0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74,
	0x6f, 0x72, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x91, 0x01, 0x0a, 0x13, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f,
	0x72, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x19, 0x0a, 0x09, 0xe5, 0x8f, 0x82, 0xe5,
	0xb1, 0x95, 0xe5, 0x95, 0x86, 0x12, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe5, 0xb1, 0x95,
	0xe5, 0x95, 0x86, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x1a, 0x1f, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68,
	0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa6, 0x01,
	0x0a, 0x16, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74,
	0x6f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78,
	0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x1f, 0x0a, 0x09, 0xe5,
	0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x12, 0x12, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae,
	0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x3a, 0x01, 0x2a, 0x1a, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x42, 0x92, 0x41, 0x1f, 0x0a, 0x09, 0xe5,
	0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96,
	0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x97, 0x01, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62,
	0x69, 0x74, 0x6f, 0x72, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68,
	0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x43, 0x92, 0x41, 0x19, 0x0a, 0x09, 0xe5, 0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5, 0x95,
	0x86, 0x12, 0x0c, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x21, 0x2a, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x45, 0x78,
	0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d,
	0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65,
	0x78, 0x70, 0x6f, 0x2f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x96, 0x01,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x48, 0x92, 0x41,
	0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x12,
	0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0xe8, 0xaf, 0xa6,
	0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x2f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x8e, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4b, 0x92, 0x41, 0x22, 0x0a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x12, 0x12, 0xe4,
	0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d,
	0x97, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x1a, 0x1b, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x67, 0x75, 0x69, 0x64, 0x65,
	0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x51, 0x92, 0x41, 0x28, 0x0a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x12, 0x18, 0xe8,
	0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d,
	0x97, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a,
	0x1a, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f,
	0x2f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x2f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0xa1, 0x01,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d, 0x97, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x94, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f,
	0x47, 0x75, 0x69, 0x64, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48,
	0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5, 0x8d,
	0x97, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6,
	0x8c, 0x87, 0xe5, 0x8d, 0x97, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x2a, 0x1b, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x67, 0x75, 0x69, 0x64,
	0x65, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xb2, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92,
	0x41, 0x28, 0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4,
	0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc,
	0x99, 0xe4, 0xbc, 0xb4, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d,
	0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f,
	0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x9c, 0x01,
	0x0a, 0x0e, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x4a, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99,
	0xe4, 0xbc, 0xb4, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0x90, 0x88, 0xe4, 0xbd,
	0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a,
	0x22, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f,
	0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x12, 0xa4, 0x01, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x50, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc,
	0x99, 0xe4, 0xbc, 0xb4, 0x12, 0x18, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x90, 0x88, 0xe4,
	0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x65, 0x78, 0x70, 0x6f, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x94, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4d, 0x92, 0x41, 0x22,
	0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0x12, 0x12,
	0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4,
	0xbc, 0xb4, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x1a, 0x1d, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa9, 0x01, 0x0a, 0x14, 0x53,
	0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x53, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc,
	0x99, 0xe4, 0xbc, 0xb4, 0x12, 0x18, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe5, 0x90, 0x88, 0xe4,
	0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x1a, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0xa9, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4,
	0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x90, 0x88,
	0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x4a, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c,
	0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0x90,
	0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x2a, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f,
	0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x98, 0x01, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x49, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9,
	0xa1, 0xbe, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22,
	0x19, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x61, 0x64, 0x64, 0x12, 0xa0, 0x01, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4f, 0x92, 0x41,
	0x28, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x12,
	0x18, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e,
	0xe9, 0xa1, 0xbe, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12,
	0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x91, 0x01,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x1a,
	0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x12, 0x12, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21,
	0x3a, 0x01, 0x2a, 0x1a, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65,
	0x78, 0x70, 0x6f, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0xa6, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x52, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x12, 0x18, 0xe8, 0xae, 0xbe, 0xe7, 0xbd,
	0xae, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x1a, 0x1c, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x0e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x4d, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x97, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x49, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b,
	0x9e, 0xe9, 0xa1, 0xbe, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe9, 0xa1, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x2a, 0x1c,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x8c, 0x01, 0x0a,
	0x0b, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x4c,
	0x69, 0x76, 0x65, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x47, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7,
	0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a,
	0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x8e, 0x01, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x4c,
	0x69, 0x76, 0x65, 0x22, 0x47, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b,
	0xb4, 0xe6, 0x92, 0xad, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c,
	0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f,
	0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x87, 0x01, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x12,
	0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4a, 0x92, 0x41, 0x22, 0x0a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x12, 0xe4,
	0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92,
	0xad, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x1a, 0x1a, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x50, 0x92, 0x41, 0x28, 0x0a, 0x0c, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x18, 0xe8, 0xae, 0xbe, 0xe7,
	0xbd, 0xae, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe7, 0x8a,
	0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x1a, 0x1a, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x6c, 0x69,
	0x76, 0x65, 0x2f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x0c, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4b, 0x92, 0x41,
	0x28, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12,
	0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4,
	0xe6, 0x92, 0xad, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12,
	0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x6c, 0x69, 0x76, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x0e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x47, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1c, 0x2a, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x70,
	0x6f, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xf4, 0x01,
	0x0a, 0x0f, 0x46, 0x61, 0x63, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x61, 0x63, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x98, 0x01, 0x92, 0x41, 0x75, 0x0a,
	0x0c, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe8, 0xaf, 0x86, 0xe5, 0x88, 0xab, 0x12, 0x21, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x85, 0xa7, 0xe7, 0x89, 0x87, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc,
	0xa0, 0xe4, 0xb8, 0x8e, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe8, 0xaf, 0x86, 0xe5, 0x88, 0xab,
	0x1a, 0x42, 0xe5, 0x90, 0x8e, 0xe5, 0x8f, 0xb0, 0xe7, 0xae, 0xa1, 0xe7, 0x90, 0x86, 0xe6, 0x8e,
	0xa5, 0xe5, 0x8f, 0xa3, 0xef, 0xbc, 0x8c, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0xe7, 0x85, 0xa7, 0xe7, 0x89, 0x87, 0xe5, 0xb9, 0xb6, 0xe8, 0xbf, 0x9b, 0xe8,
	0xa1, 0x8c, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe6, 0xa3, 0x80, 0xe6, 0xb5, 0x8b, 0xe5, 0x85,
	0xa5, 0xe5, 0xba, 0x93, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f,
	0x76, 0x31, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x2f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x7b, 0x0a, 0x0f, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f,
	0x66, 0x61, 0x63, 0x65, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x12, 0xbe, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6b, 0x92, 0x41, 0x4a, 0x0a, 0x0c, 0xe4, 0xba, 0xba, 0xe8,
	0x84, 0xb8, 0xe8, 0xaf, 0x86, 0xe5, 0x88, 0xab, 0x12, 0x15, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96,
	0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x1a,
	0x23, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93,
	0x49, 0x44, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0xaf, 0xa6, 0xe7, 0xbb, 0x86, 0xe4, 0xbf,
	0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f,
	0x76, 0x31, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0xd3, 0x01, 0x0a, 0x0d, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x7e, 0x92, 0x41, 0x5d, 0x0a, 0x0c, 0xe4,
	0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe8, 0xaf, 0x86, 0xe5, 0x88, 0xab, 0x12, 0x15, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x1a, 0x36, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96,
	0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0xef,
	0xbc, 0x8c, 0xe6, 0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18,
	0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x2f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0xdc, 0x01, 0x0a, 0x10, 0x46, 0x61, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x12, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x85, 0x01, 0x92, 0x41, 0x62, 0x0a, 0x0c, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe8, 0xaf, 0x86,
	0xe5, 0x88, 0xab, 0x12, 0x1b, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe5, 0x9c, 0xba, 0xe6, 0x99,
	0xaf, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93,
	0x1a, 0x35, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44,
	0xe7, 0xad, 0x89, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xaf, 0xb9, 0xe5, 0xba, 0x94, 0xe7, 0x9a, 0x84, 0xe4, 0xba,
	0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a,
	0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x42, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x12, 0xd3, 0x01, 0x0a, 0x0e, 0x46, 0x61, 0x63, 0x65,
	0x73, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x46, 0x61,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x46, 0x61,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x82, 0x01, 0x92, 0x41, 0x62, 0x0a, 0x0c,
	0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe8, 0xaf, 0x86, 0xe5, 0x88, 0xab, 0x12, 0x1b, 0xe6, 0xa0,
	0xb9, 0xe6, 0x8d, 0xae, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96,
	0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93, 0x1a, 0x35, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d,
	0xae, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0xe7, 0xad, 0x89, 0xe5, 0x9c, 0xba, 0xe6,
	0x99, 0xaf, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xaf,
	0xb9, 0xe5, 0xba, 0x94, 0xe7, 0x9a, 0x84, 0xe4, 0xba, 0xba, 0xe5, 0x91, 0x98, 0xe5, 0xba, 0x93,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x66,
	0x61, 0x63, 0x65, 0x2f, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x10, 0x5a,
	0x0e, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_expo_v1_background_proto_rawDescOnce sync.Once
	file_expo_v1_background_proto_rawDescData = file_expo_v1_background_proto_rawDesc
)

func file_expo_v1_background_proto_rawDescGZIP() []byte {
	file_expo_v1_background_proto_rawDescOnce.Do(func() {
		file_expo_v1_background_proto_rawDescData = protoimpl.X.CompressGZIP(file_expo_v1_background_proto_rawDescData)
	})
	return file_expo_v1_background_proto_rawDescData
}

var file_expo_v1_background_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_expo_v1_background_proto_msgTypes = make([]protoimpl.MessageInfo, 106)
var file_expo_v1_background_proto_goTypes = []interface{}{
	(ExpoHallType)(0),                     // 0: api.expo.v1.ExpoHallType
	(ScheduleType)(0),                     // 1: api.expo.v1.ScheduleType
	(ExpoReviewType)(0),                   // 2: api.expo.v1.ExpoReviewType
	(SyncStatus)(0),                       // 3: api.expo.v1.SyncStatus
	(ImageSyncStatus)(0),                  // 4: api.expo.v1.ImageSyncStatus
	(SyncAction)(0),                       // 5: api.expo.v1.SyncAction
	(*GuestLanguage)(nil),                 // 6: api.expo.v1.GuestLanguage
	(*Guest)(nil),                         // 7: api.expo.v1.Guest
	(*AddGuestReply)(nil),                 // 8: api.expo.v1.AddGuestReply
	(*SetGuestEnableRequest)(nil),         // 9: api.expo.v1.SetGuestEnableRequest
	(*GetGuestRequest)(nil),               // 10: api.expo.v1.GetGuestRequest
	(*ListGuestRequest)(nil),              // 11: api.expo.v1.ListGuestRequest
	(*ListGuestReply)(nil),                // 12: api.expo.v1.ListGuestReply
	(*DeleteGuestRequest)(nil),            // 13: api.expo.v1.DeleteGuestRequest
	(*ExpoCommunityLanguage)(nil),         // 14: api.expo.v1.ExpoCommunityLanguage
	(*ExpoCommunity)(nil),                 // 15: api.expo.v1.ExpoCommunity
	(*GetExpoCommunityRequest)(nil),       // 16: api.expo.v1.GetExpoCommunityRequest
	(*SetExpoCommunityEnableRequest)(nil), // 17: api.expo.v1.SetExpoCommunityEnableRequest
	(*DeleteExpoCommunityRequest)(nil),    // 18: api.expo.v1.DeleteExpoCommunityRequest
	(*ExpoHallLanguage)(nil),              // 19: api.expo.v1.ExpoHallLanguage
	(*ExpoHall)(nil),                      // 20: api.expo.v1.ExpoHall
	(*GetHallRequest)(nil),                // 21: api.expo.v1.GetHallRequest
	(*SetHallEnableRequest)(nil),          // 22: api.expo.v1.SetHallEnableRequest
	(*ListHallRequest)(nil),               // 23: api.expo.v1.ListHallRequest
	(*ListHallReply)(nil),                 // 24: api.expo.v1.ListHallReply
	(*DeleteHallRequest)(nil),             // 25: api.expo.v1.DeleteHallRequest
	(*ExpoGuest)(nil),                     // 26: api.expo.v1.ExpoGuest
	(*AddExpoGuestReply)(nil),             // 27: api.expo.v1.AddExpoGuestReply
	(*GetExpoGuestRequest)(nil),           // 28: api.expo.v1.GetExpoGuestRequest
	(*GetExpoGuestReply)(nil),             // 29: api.expo.v1.GetExpoGuestReply
	(*ListExpoGuestRequest)(nil),          // 30: api.expo.v1.ListExpoGuestRequest
	(*ListExpoGuestReply)(nil),            // 31: api.expo.v1.ListExpoGuestReply
	(*DeleteExpoGuestRequest)(nil),        // 32: api.expo.v1.DeleteExpoGuestRequest
	(*ExpoScheduleLanguage)(nil),          // 33: api.expo.v1.ExpoScheduleLanguage
	(*ExpoScheduleSpeaker)(nil),           // 34: api.expo.v1.ExpoScheduleSpeaker
	(*ExpoScheduleInfo)(nil),              // 35: api.expo.v1.ExpoScheduleInfo
	(*AddExpoScheduleReply)(nil),          // 36: api.expo.v1.AddExpoScheduleReply
	(*GetExpoScheduleRequest)(nil),        // 37: api.expo.v1.GetExpoScheduleRequest
	(*ListExpoScheduleRequest)(nil),       // 38: api.expo.v1.ListExpoScheduleRequest
	(*ListExpoScheduleReply)(nil),         // 39: api.expo.v1.ListExpoScheduleReply
	(*DeleteExpoScheduleRequest)(nil),     // 40: api.expo.v1.DeleteExpoScheduleRequest
	(*ExhibitorEmployeeInfo)(nil),         // 41: api.expo.v1.ExhibitorEmployeeInfo
	(*ExpoExhibitorLanguage)(nil),         // 42: api.expo.v1.ExpoExhibitorLanguage
	(*ExpoExhibitorInfo)(nil),             // 43: api.expo.v1.ExpoExhibitorInfo
	(*AddExpoExhibitorReply)(nil),         // 44: api.expo.v1.AddExpoExhibitorReply
	(*GetExpoExhibitorRequest)(nil),       // 45: api.expo.v1.GetExpoExhibitorRequest
	(*SetExpoExhibitorEnableRequest)(nil), // 46: api.expo.v1.SetExpoExhibitorEnableRequest
	(*ListExpoExhibitorRequest)(nil),      // 47: api.expo.v1.ListExpoExhibitorRequest
	(*ListExpoExhibitorReply)(nil),        // 48: api.expo.v1.ListExpoExhibitorReply
	(*DeleteExpoExhibitorRequest)(nil),    // 49: api.expo.v1.DeleteExpoExhibitorRequest
	(*ExpoGuideInfo)(nil),                 // 50: api.expo.v1.ExpoGuideInfo
	(*AddExpoGuideReply)(nil),             // 51: api.expo.v1.AddExpoGuideReply
	(*GetExpoGuideRequest)(nil),           // 52: api.expo.v1.GetExpoGuideRequest
	(*SetExpoGuideEnableRequest)(nil),     // 53: api.expo.v1.SetExpoGuideEnableRequest
	(*ListExpoGuideRequest)(nil),          // 54: api.expo.v1.ListExpoGuideRequest
	(*ListExpoGuideReply)(nil),            // 55: api.expo.v1.ListExpoGuideReply
	(*DeleteExpoGuideRequest)(nil),        // 56: api.expo.v1.DeleteExpoGuideRequest
	(*GetExpoPartnerTypeRequest)(nil),     // 57: api.expo.v1.GetExpoPartnerTypeRequest
	(*ExpoPartnerTypeItem)(nil),           // 58: api.expo.v1.ExpoPartnerTypeItem
	(*GetExpoPartnerTypeReply)(nil),       // 59: api.expo.v1.GetExpoPartnerTypeReply
	(*ExpoPartnerInfo)(nil),               // 60: api.expo.v1.ExpoPartnerInfo
	(*AddExpoPartnerReply)(nil),           // 61: api.expo.v1.AddExpoPartnerReply
	(*GetExpoPartnerRequest)(nil),         // 62: api.expo.v1.GetExpoPartnerRequest
	(*SetExpoPartnerEnableRequest)(nil),   // 63: api.expo.v1.SetExpoPartnerEnableRequest
	(*ListExpoPartnerRequest)(nil),        // 64: api.expo.v1.ListExpoPartnerRequest
	(*ListExpoPartnerReply)(nil),          // 65: api.expo.v1.ListExpoPartnerReply
	(*DeleteExpoPartnerRequest)(nil),      // 66: api.expo.v1.DeleteExpoPartnerRequest
	(*ExpoReviewLanguage)(nil),            // 67: api.expo.v1.ExpoReviewLanguage
	(*ExpoReviewInfo)(nil),                // 68: api.expo.v1.ExpoReviewInfo
	(*AddExpoReviewReply)(nil),            // 69: api.expo.v1.AddExpoReviewReply
	(*GetExpoReviewRequest)(nil),          // 70: api.expo.v1.GetExpoReviewRequest
	(*SetExpoReviewEnableRequest)(nil),    // 71: api.expo.v1.SetExpoReviewEnableRequest
	(*ListExpoReviewRequest)(nil),         // 72: api.expo.v1.ListExpoReviewRequest
	(*ListExpoReviewReply)(nil),           // 73: api.expo.v1.ListExpoReviewReply
	(*DeleteExpoReviewRequest)(nil),       // 74: api.expo.v1.DeleteExpoReviewRequest
	(*ExpoLive)(nil),                      // 75: api.expo.v1.ExpoLive
	(*AddExpoLiveReply)(nil),              // 76: api.expo.v1.AddExpoLiveReply
	(*GetExpoLiveRequest)(nil),            // 77: api.expo.v1.GetExpoLiveRequest
	(*SetExpoLiveEnableRequest)(nil),      // 78: api.expo.v1.SetExpoLiveEnableRequest
	(*ListExpoLiveRequest)(nil),           // 79: api.expo.v1.ListExpoLiveRequest
	(*ListExpoLiveReply)(nil),             // 80: api.expo.v1.ListExpoLiveReply
	(*DeleteExpoLiveRequest)(nil),         // 81: api.expo.v1.DeleteExpoLiveRequest
	(*AdminLiveImage)(nil),                // 82: api.expo.v1.adminLiveImage
	(*ListLiveImageRequest)(nil),          // 83: api.expo.v1.ListLiveImageRequest
	(*ListLiveImageReply)(nil),            // 84: api.expo.v1.ListLiveImageReply
	(*AddLiveImageRequest)(nil),           // 85: api.expo.v1.AddLiveImageRequest
	(*AddLiveImageReply)(nil),             // 86: api.expo.v1.AddLiveImageReply
	(*DeleteLiveImageRequest)(nil),        // 87: api.expo.v1.DeleteLiveImageRequest
	(*DeleteLiveImageReply)(nil),          // 88: api.expo.v1.DeleteLiveImageReply
	(*SyncLiveImagesRequest)(nil),         // 89: api.expo.v1.SyncLiveImagesRequest
	(*SyncLiveImagesReply)(nil),           // 90: api.expo.v1.SyncLiveImagesReply
	(*GetSyncStatusRequest)(nil),          // 91: api.expo.v1.GetSyncStatusRequest
	(*ImageSyncItem)(nil),                 // 92: api.expo.v1.ImageSyncItem
	(*GetSyncStatusReply)(nil),            // 93: api.expo.v1.GetSyncStatusReply
	(*FacePhotoUploadRequest)(nil),        // 94: api.expo.v1.FacePhotoUploadRequest
	(*FacePhotoUploadReply)(nil),          // 95: api.expo.v1.FacePhotoUploadReply
	(*FaceUploadItem)(nil),                // 96: api.expo.v1.FaceUploadItem
	(*ExhibitionInfo)(nil),                // 97: api.expo.v1.ExhibitionInfo
	(*FaceGroupCreateRequest)(nil),        // 98: api.expo.v1.FaceGroupCreateRequest
	(*FaceGroupCreateReply)(nil),          // 99: api.expo.v1.FaceGroupCreateReply
	(*FaceGroupInfoRequest)(nil),          // 100: api.expo.v1.FaceGroupInfoRequest
	(*FaceGroupsByExpoRequest)(nil),       // 101: api.expo.v1.FaceGroupsByExpoRequest
	(*FaceGroupListRequest)(nil),          // 102: api.expo.v1.FaceGroupListRequest
	(*FaceGroupListReply)(nil),            // 103: api.expo.v1.FaceGroupListReply
	(*FaceGroupInfo)(nil),                 // 104: api.expo.v1.FaceGroupInfo
	(*PhotoFacesRequest)(nil),             // 105: api.expo.v1.PhotoFacesRequest
	(*PhotoFacesReply)(nil),               // 106: api.expo.v1.PhotoFacesReply
	nil,                                   // 107: api.expo.v1.Guest.LanguagesEntry
	nil,                                   // 108: api.expo.v1.ExpoCommunity.LanguagesEntry
	nil,                                   // 109: api.expo.v1.ExpoHall.LanguagesEntry
	nil,                                   // 110: api.expo.v1.ExpoScheduleInfo.LanguagesEntry
	nil,                                   // 111: api.expo.v1.ExpoReviewInfo.LanguagesEntry
	(SponsorLevel)(0),                     // 112: api.expo.v1.SponsorLevel
	(*FaceRect)(nil),                      // 113: api.expo.v1.FaceRect
	(*common.EmptyRequest)(nil),           // 114: common.EmptyRequest
	(*common.HealthyReply)(nil),           // 115: common.HealthyReply
	(*common.EmptyReply)(nil),             // 116: common.EmptyReply
}
var file_expo_v1_background_proto_depIdxs = []int32{
	107, // 0: api.expo.v1.Guest.languages:type_name -> api.expo.v1.Guest.LanguagesEntry
	7,   // 1: api.expo.v1.ListGuestReply.guests:type_name -> api.expo.v1.Guest
	108, // 2: api.expo.v1.ExpoCommunity.languages:type_name -> api.expo.v1.ExpoCommunity.LanguagesEntry
	0,   // 3: api.expo.v1.ExpoHall.type:type_name -> api.expo.v1.ExpoHallType
	109, // 4: api.expo.v1.ExpoHall.languages:type_name -> api.expo.v1.ExpoHall.LanguagesEntry
	20,  // 5: api.expo.v1.ListHallReply.items:type_name -> api.expo.v1.ExpoHall
	7,   // 6: api.expo.v1.GetExpoGuestReply.guest:type_name -> api.expo.v1.Guest
	26,  // 7: api.expo.v1.ListExpoGuestReply.guests:type_name -> api.expo.v1.ExpoGuest
	1,   // 8: api.expo.v1.ExpoScheduleInfo.type:type_name -> api.expo.v1.ScheduleType
	34,  // 9: api.expo.v1.ExpoScheduleInfo.host:type_name -> api.expo.v1.ExpoScheduleSpeaker
	34,  // 10: api.expo.v1.ExpoScheduleInfo.speakers:type_name -> api.expo.v1.ExpoScheduleSpeaker
	110, // 11: api.expo.v1.ExpoScheduleInfo.languages:type_name -> api.expo.v1.ExpoScheduleInfo.LanguagesEntry
	35,  // 12: api.expo.v1.ListExpoScheduleReply.schedules:type_name -> api.expo.v1.ExpoScheduleInfo
	112, // 13: api.expo.v1.ExpoExhibitorInfo.sponsor_level:type_name -> api.expo.v1.SponsorLevel
	41,  // 14: api.expo.v1.ExpoExhibitorInfo.employees:type_name -> api.expo.v1.ExhibitorEmployeeInfo
	43,  // 15: api.expo.v1.ListExpoExhibitorReply.exhibitors:type_name -> api.expo.v1.ExpoExhibitorInfo
	50,  // 16: api.expo.v1.ListExpoGuideReply.items:type_name -> api.expo.v1.ExpoGuideInfo
	58,  // 17: api.expo.v1.GetExpoPartnerTypeReply.items:type_name -> api.expo.v1.ExpoPartnerTypeItem
	60,  // 18: api.expo.v1.ListExpoPartnerReply.partners:type_name -> api.expo.v1.ExpoPartnerInfo
	2,   // 19: api.expo.v1.ExpoReviewInfo.type:type_name -> api.expo.v1.ExpoReviewType
	111, // 20: api.expo.v1.ExpoReviewInfo.languages:type_name -> api.expo.v1.ExpoReviewInfo.LanguagesEntry
	68,  // 21: api.expo.v1.ListExpoReviewReply.items:type_name -> api.expo.v1.ExpoReviewInfo
	75,  // 22: api.expo.v1.ListExpoLiveReply.lives:type_name -> api.expo.v1.ExpoLive
	82,  // 23: api.expo.v1.ListLiveImageReply.list:type_name -> api.expo.v1.adminLiveImage
	5,   // 24: api.expo.v1.ImageSyncItem.action:type_name -> api.expo.v1.SyncAction
	4,   // 25: api.expo.v1.ImageSyncItem.status:type_name -> api.expo.v1.ImageSyncStatus
	3,   // 26: api.expo.v1.GetSyncStatusReply.status:type_name -> api.expo.v1.SyncStatus
	92,  // 27: api.expo.v1.GetSyncStatusReply.items:type_name -> api.expo.v1.ImageSyncItem
	96,  // 28: api.expo.v1.FacePhotoUploadReply.faces:type_name -> api.expo.v1.FaceUploadItem
	113, // 29: api.expo.v1.FaceUploadItem.rect:type_name -> api.expo.v1.FaceRect
	104, // 30: api.expo.v1.FaceGroupListReply.groups:type_name -> api.expo.v1.FaceGroupInfo
	96,  // 31: api.expo.v1.PhotoFacesReply.faces:type_name -> api.expo.v1.FaceUploadItem
	6,   // 32: api.expo.v1.Guest.LanguagesEntry.value:type_name -> api.expo.v1.GuestLanguage
	14,  // 33: api.expo.v1.ExpoCommunity.LanguagesEntry.value:type_name -> api.expo.v1.ExpoCommunityLanguage
	19,  // 34: api.expo.v1.ExpoHall.LanguagesEntry.value:type_name -> api.expo.v1.ExpoHallLanguage
	33,  // 35: api.expo.v1.ExpoScheduleInfo.LanguagesEntry.value:type_name -> api.expo.v1.ExpoScheduleLanguage
	67,  // 36: api.expo.v1.ExpoReviewInfo.LanguagesEntry.value:type_name -> api.expo.v1.ExpoReviewLanguage
	114, // 37: api.expo.v1.Background.Healthy:input_type -> common.EmptyRequest
	83,  // 38: api.expo.v1.Background.ListLiveImage:input_type -> api.expo.v1.ListLiveImageRequest
	85,  // 39: api.expo.v1.Background.AddLiveImage:input_type -> api.expo.v1.AddLiveImageRequest
	87,  // 40: api.expo.v1.Background.DeleteLiveImage:input_type -> api.expo.v1.DeleteLiveImageRequest
	89,  // 41: api.expo.v1.Background.SyncLiveImages:input_type -> api.expo.v1.SyncLiveImagesRequest
	91,  // 42: api.expo.v1.Background.GetSyncStatus:input_type -> api.expo.v1.GetSyncStatusRequest
	7,   // 43: api.expo.v1.Background.AddGuest:input_type -> api.expo.v1.Guest
	10,  // 44: api.expo.v1.Background.GetGuest:input_type -> api.expo.v1.GetGuestRequest
	7,   // 45: api.expo.v1.Background.UpdateGuest:input_type -> api.expo.v1.Guest
	9,   // 46: api.expo.v1.Background.SetGuestEnable:input_type -> api.expo.v1.SetGuestEnableRequest
	11,  // 47: api.expo.v1.Background.ListGuest:input_type -> api.expo.v1.ListGuestRequest
	13,  // 48: api.expo.v1.Background.DeleteGuest:input_type -> api.expo.v1.DeleteGuestRequest
	15,  // 49: api.expo.v1.Background.AddExpoCommunity:input_type -> api.expo.v1.ExpoCommunity
	16,  // 50: api.expo.v1.Background.GetExpoCommunity:input_type -> api.expo.v1.GetExpoCommunityRequest
	15,  // 51: api.expo.v1.Background.UpdateExpoCommunity:input_type -> api.expo.v1.ExpoCommunity
	17,  // 52: api.expo.v1.Background.SetExpoCommunityEnable:input_type -> api.expo.v1.SetExpoCommunityEnableRequest
	18,  // 53: api.expo.v1.Background.DeleteExpoCommunity:input_type -> api.expo.v1.DeleteExpoCommunityRequest
	20,  // 54: api.expo.v1.Background.AddHall:input_type -> api.expo.v1.ExpoHall
	21,  // 55: api.expo.v1.Background.GetHall:input_type -> api.expo.v1.GetHallRequest
	20,  // 56: api.expo.v1.Background.UpdateHall:input_type -> api.expo.v1.ExpoHall
	22,  // 57: api.expo.v1.Background.SetHallEnable:input_type -> api.expo.v1.SetHallEnableRequest
	23,  // 58: api.expo.v1.Background.ListHall:input_type -> api.expo.v1.ListHallRequest
	25,  // 59: api.expo.v1.Background.DeleteHall:input_type -> api.expo.v1.DeleteHallRequest
	26,  // 60: api.expo.v1.Background.AddExpoGuest:input_type -> api.expo.v1.ExpoGuest
	28,  // 61: api.expo.v1.Background.GetExpoGuest:input_type -> api.expo.v1.GetExpoGuestRequest
	30,  // 62: api.expo.v1.Background.ListExpoGuest:input_type -> api.expo.v1.ListExpoGuestRequest
	32,  // 63: api.expo.v1.Background.DeleteExpoGuest:input_type -> api.expo.v1.DeleteExpoGuestRequest
	35,  // 64: api.expo.v1.Background.AddExpoSchedule:input_type -> api.expo.v1.ExpoScheduleInfo
	37,  // 65: api.expo.v1.Background.GetExpoSchedule:input_type -> api.expo.v1.GetExpoScheduleRequest
	35,  // 66: api.expo.v1.Background.UpdateExpoSchedule:input_type -> api.expo.v1.ExpoScheduleInfo
	38,  // 67: api.expo.v1.Background.ListExpoSchedule:input_type -> api.expo.v1.ListExpoScheduleRequest
	40,  // 68: api.expo.v1.Background.DeleteExpoSchedule:input_type -> api.expo.v1.DeleteExpoScheduleRequest
	43,  // 69: api.expo.v1.Background.AddExpoExhibitor:input_type -> api.expo.v1.ExpoExhibitorInfo
	45,  // 70: api.expo.v1.Background.GetExpoExhibitor:input_type -> api.expo.v1.GetExpoExhibitorRequest
	43,  // 71: api.expo.v1.Background.UpdateExpoExhibitor:input_type -> api.expo.v1.ExpoExhibitorInfo
	46,  // 72: api.expo.v1.Background.SetExpoExhibitorEnable:input_type -> api.expo.v1.SetExpoExhibitorEnableRequest
	47,  // 73: api.expo.v1.Background.ListExpoExhibitor:input_type -> api.expo.v1.ListExpoExhibitorRequest
	49,  // 74: api.expo.v1.Background.DeleteExpoExhibitor:input_type -> api.expo.v1.DeleteExpoExhibitorRequest
	50,  // 75: api.expo.v1.Background.AddExpoGuide:input_type -> api.expo.v1.ExpoGuideInfo
	52,  // 76: api.expo.v1.Background.GetExpoGuide:input_type -> api.expo.v1.GetExpoGuideRequest
	50,  // 77: api.expo.v1.Background.UpdateExpoGuide:input_type -> api.expo.v1.ExpoGuideInfo
	53,  // 78: api.expo.v1.Background.SetExpoGuideEnable:input_type -> api.expo.v1.SetExpoGuideEnableRequest
	54,  // 79: api.expo.v1.Background.ListExpoGuide:input_type -> api.expo.v1.ListExpoGuideRequest
	56,  // 80: api.expo.v1.Background.DeleteExpoGuide:input_type -> api.expo.v1.DeleteExpoGuideRequest
	57,  // 81: api.expo.v1.Background.GetExpoPartnerType:input_type -> api.expo.v1.GetExpoPartnerTypeRequest
	60,  // 82: api.expo.v1.Background.AddExpoPartner:input_type -> api.expo.v1.ExpoPartnerInfo
	62,  // 83: api.expo.v1.Background.GetExpoPartner:input_type -> api.expo.v1.GetExpoPartnerRequest
	60,  // 84: api.expo.v1.Background.UpdateExpoPartner:input_type -> api.expo.v1.ExpoPartnerInfo
	63,  // 85: api.expo.v1.Background.SetExpoPartnerEnable:input_type -> api.expo.v1.SetExpoPartnerEnableRequest
	64,  // 86: api.expo.v1.Background.ListExpoPartner:input_type -> api.expo.v1.ListExpoPartnerRequest
	66,  // 87: api.expo.v1.Background.DeleteExpoPartner:input_type -> api.expo.v1.DeleteExpoPartnerRequest
	68,  // 88: api.expo.v1.Background.AddExpoReview:input_type -> api.expo.v1.ExpoReviewInfo
	70,  // 89: api.expo.v1.Background.GetExpoReview:input_type -> api.expo.v1.GetExpoReviewRequest
	68,  // 90: api.expo.v1.Background.UpdateExpoReview:input_type -> api.expo.v1.ExpoReviewInfo
	71,  // 91: api.expo.v1.Background.SetExpoReviewEnable:input_type -> api.expo.v1.SetExpoReviewEnableRequest
	72,  // 92: api.expo.v1.Background.ListExpoReview:input_type -> api.expo.v1.ListExpoReviewRequest
	74,  // 93: api.expo.v1.Background.DeleteExpoReview:input_type -> api.expo.v1.DeleteExpoReviewRequest
	75,  // 94: api.expo.v1.Background.AddExpoLive:input_type -> api.expo.v1.ExpoLive
	77,  // 95: api.expo.v1.Background.GetExpoLive:input_type -> api.expo.v1.GetExpoLiveRequest
	75,  // 96: api.expo.v1.Background.UpdateExpoLive:input_type -> api.expo.v1.ExpoLive
	78,  // 97: api.expo.v1.Background.SetExpoLiveEnable:input_type -> api.expo.v1.SetExpoLiveEnableRequest
	79,  // 98: api.expo.v1.Background.ListExpoLive:input_type -> api.expo.v1.ListExpoLiveRequest
	81,  // 99: api.expo.v1.Background.DeleteExpoLive:input_type -> api.expo.v1.DeleteExpoLiveRequest
	94,  // 100: api.expo.v1.Background.FacePhotoUpload:input_type -> api.expo.v1.FacePhotoUploadRequest
	98,  // 101: api.expo.v1.Background.FaceGroupCreate:input_type -> api.expo.v1.FaceGroupCreateRequest
	100, // 102: api.expo.v1.Background.GetFaceGroupInfo:input_type -> api.expo.v1.FaceGroupInfoRequest
	102, // 103: api.expo.v1.Background.FaceGroupList:input_type -> api.expo.v1.FaceGroupListRequest
	101, // 104: api.expo.v1.Background.FaceGroupsByExpo:input_type -> api.expo.v1.FaceGroupsByExpoRequest
	105, // 105: api.expo.v1.Background.FacesPhotoList:input_type -> api.expo.v1.PhotoFacesRequest
	115, // 106: api.expo.v1.Background.Healthy:output_type -> common.HealthyReply
	84,  // 107: api.expo.v1.Background.ListLiveImage:output_type -> api.expo.v1.ListLiveImageReply
	86,  // 108: api.expo.v1.Background.AddLiveImage:output_type -> api.expo.v1.AddLiveImageReply
	88,  // 109: api.expo.v1.Background.DeleteLiveImage:output_type -> api.expo.v1.DeleteLiveImageReply
	90,  // 110: api.expo.v1.Background.SyncLiveImages:output_type -> api.expo.v1.SyncLiveImagesReply
	93,  // 111: api.expo.v1.Background.GetSyncStatus:output_type -> api.expo.v1.GetSyncStatusReply
	8,   // 112: api.expo.v1.Background.AddGuest:output_type -> api.expo.v1.AddGuestReply
	7,   // 113: api.expo.v1.Background.GetGuest:output_type -> api.expo.v1.Guest
	116, // 114: api.expo.v1.Background.UpdateGuest:output_type -> common.EmptyReply
	116, // 115: api.expo.v1.Background.SetGuestEnable:output_type -> common.EmptyReply
	12,  // 116: api.expo.v1.Background.ListGuest:output_type -> api.expo.v1.ListGuestReply
	116, // 117: api.expo.v1.Background.DeleteGuest:output_type -> common.EmptyReply
	116, // 118: api.expo.v1.Background.AddExpoCommunity:output_type -> common.EmptyReply
	15,  // 119: api.expo.v1.Background.GetExpoCommunity:output_type -> api.expo.v1.ExpoCommunity
	116, // 120: api.expo.v1.Background.UpdateExpoCommunity:output_type -> common.EmptyReply
	116, // 121: api.expo.v1.Background.SetExpoCommunityEnable:output_type -> common.EmptyReply
	116, // 122: api.expo.v1.Background.DeleteExpoCommunity:output_type -> common.EmptyReply
	8,   // 123: api.expo.v1.Background.AddHall:output_type -> api.expo.v1.AddGuestReply
	20,  // 124: api.expo.v1.Background.GetHall:output_type -> api.expo.v1.ExpoHall
	116, // 125: api.expo.v1.Background.UpdateHall:output_type -> common.EmptyReply
	116, // 126: api.expo.v1.Background.SetHallEnable:output_type -> common.EmptyReply
	24,  // 127: api.expo.v1.Background.ListHall:output_type -> api.expo.v1.ListHallReply
	116, // 128: api.expo.v1.Background.DeleteHall:output_type -> common.EmptyReply
	27,  // 129: api.expo.v1.Background.AddExpoGuest:output_type -> api.expo.v1.AddExpoGuestReply
	26,  // 130: api.expo.v1.Background.GetExpoGuest:output_type -> api.expo.v1.ExpoGuest
	31,  // 131: api.expo.v1.Background.ListExpoGuest:output_type -> api.expo.v1.ListExpoGuestReply
	116, // 132: api.expo.v1.Background.DeleteExpoGuest:output_type -> common.EmptyReply
	36,  // 133: api.expo.v1.Background.AddExpoSchedule:output_type -> api.expo.v1.AddExpoScheduleReply
	35,  // 134: api.expo.v1.Background.GetExpoSchedule:output_type -> api.expo.v1.ExpoScheduleInfo
	116, // 135: api.expo.v1.Background.UpdateExpoSchedule:output_type -> common.EmptyReply
	39,  // 136: api.expo.v1.Background.ListExpoSchedule:output_type -> api.expo.v1.ListExpoScheduleReply
	116, // 137: api.expo.v1.Background.DeleteExpoSchedule:output_type -> common.EmptyReply
	44,  // 138: api.expo.v1.Background.AddExpoExhibitor:output_type -> api.expo.v1.AddExpoExhibitorReply
	43,  // 139: api.expo.v1.Background.GetExpoExhibitor:output_type -> api.expo.v1.ExpoExhibitorInfo
	116, // 140: api.expo.v1.Background.UpdateExpoExhibitor:output_type -> common.EmptyReply
	116, // 141: api.expo.v1.Background.SetExpoExhibitorEnable:output_type -> common.EmptyReply
	48,  // 142: api.expo.v1.Background.ListExpoExhibitor:output_type -> api.expo.v1.ListExpoExhibitorReply
	116, // 143: api.expo.v1.Background.DeleteExpoExhibitor:output_type -> common.EmptyReply
	51,  // 144: api.expo.v1.Background.AddExpoGuide:output_type -> api.expo.v1.AddExpoGuideReply
	50,  // 145: api.expo.v1.Background.GetExpoGuide:output_type -> api.expo.v1.ExpoGuideInfo
	116, // 146: api.expo.v1.Background.UpdateExpoGuide:output_type -> common.EmptyReply
	116, // 147: api.expo.v1.Background.SetExpoGuideEnable:output_type -> common.EmptyReply
	55,  // 148: api.expo.v1.Background.ListExpoGuide:output_type -> api.expo.v1.ListExpoGuideReply
	116, // 149: api.expo.v1.Background.DeleteExpoGuide:output_type -> common.EmptyReply
	59,  // 150: api.expo.v1.Background.GetExpoPartnerType:output_type -> api.expo.v1.GetExpoPartnerTypeReply
	61,  // 151: api.expo.v1.Background.AddExpoPartner:output_type -> api.expo.v1.AddExpoPartnerReply
	60,  // 152: api.expo.v1.Background.GetExpoPartner:output_type -> api.expo.v1.ExpoPartnerInfo
	116, // 153: api.expo.v1.Background.UpdateExpoPartner:output_type -> common.EmptyReply
	116, // 154: api.expo.v1.Background.SetExpoPartnerEnable:output_type -> common.EmptyReply
	65,  // 155: api.expo.v1.Background.ListExpoPartner:output_type -> api.expo.v1.ListExpoPartnerReply
	116, // 156: api.expo.v1.Background.DeleteExpoPartner:output_type -> common.EmptyReply
	69,  // 157: api.expo.v1.Background.AddExpoReview:output_type -> api.expo.v1.AddExpoReviewReply
	68,  // 158: api.expo.v1.Background.GetExpoReview:output_type -> api.expo.v1.ExpoReviewInfo
	116, // 159: api.expo.v1.Background.UpdateExpoReview:output_type -> common.EmptyReply
	116, // 160: api.expo.v1.Background.SetExpoReviewEnable:output_type -> common.EmptyReply
	73,  // 161: api.expo.v1.Background.ListExpoReview:output_type -> api.expo.v1.ListExpoReviewReply
	116, // 162: api.expo.v1.Background.DeleteExpoReview:output_type -> common.EmptyReply
	76,  // 163: api.expo.v1.Background.AddExpoLive:output_type -> api.expo.v1.AddExpoLiveReply
	75,  // 164: api.expo.v1.Background.GetExpoLive:output_type -> api.expo.v1.ExpoLive
	116, // 165: api.expo.v1.Background.UpdateExpoLive:output_type -> common.EmptyReply
	116, // 166: api.expo.v1.Background.SetExpoLiveEnable:output_type -> common.EmptyReply
	80,  // 167: api.expo.v1.Background.ListExpoLive:output_type -> api.expo.v1.ListExpoLiveReply
	116, // 168: api.expo.v1.Background.DeleteExpoLive:output_type -> common.EmptyReply
	95,  // 169: api.expo.v1.Background.FacePhotoUpload:output_type -> api.expo.v1.FacePhotoUploadReply
	99,  // 170: api.expo.v1.Background.FaceGroupCreate:output_type -> api.expo.v1.FaceGroupCreateReply
	104, // 171: api.expo.v1.Background.GetFaceGroupInfo:output_type -> api.expo.v1.FaceGroupInfo
	103, // 172: api.expo.v1.Background.FaceGroupList:output_type -> api.expo.v1.FaceGroupListReply
	104, // 173: api.expo.v1.Background.FaceGroupsByExpo:output_type -> api.expo.v1.FaceGroupInfo
	106, // 174: api.expo.v1.Background.FacesPhotoList:output_type -> api.expo.v1.PhotoFacesReply
	106, // [106:175] is the sub-list for method output_type
	37,  // [37:106] is the sub-list for method input_type
	37,  // [37:37] is the sub-list for extension type_name
	37,  // [37:37] is the sub-list for extension extendee
	0,   // [0:37] is the sub-list for field type_name
}

func init() { file_expo_v1_background_proto_init() }
func file_expo_v1_background_proto_init() {
	if File_expo_v1_background_proto != nil {
		return
	}
	file_expo_v1_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_expo_v1_background_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GuestLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Guest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddGuestReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetGuestEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGuestReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoCommunityLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoCommunity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoCommunityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetExpoCommunityEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoCommunityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoHallLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoHall); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetHallEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListHallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListHallReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteHallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoGuest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoGuestReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoGuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoGuestReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoGuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoGuestReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoGuestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleSpeaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoScheduleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoScheduleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitorEmployeeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoExhibitorLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoExhibitorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoExhibitorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoExhibitorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetExpoExhibitorEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoExhibitorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoExhibitorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoExhibitorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoGuideInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoGuideReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoGuideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetExpoGuideEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoGuideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoGuideReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoGuideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoPartnerTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoPartnerTypeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoPartnerTypeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoPartnerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoPartnerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoPartnerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetExpoPartnerEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoPartnerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoPartnerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoPartnerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoReviewLanguage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoReviewInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoReviewReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetExpoReviewEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoReviewReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoLive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExpoLiveReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpoLiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetExpoLiveEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoLiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExpoLiveReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExpoLiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminLiveImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLiveImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLiveImageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLiveImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLiveImageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLiveImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLiveImageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncLiveImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncLiveImagesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSyncStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageSyncItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSyncStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FacePhotoUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FacePhotoUploadReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceUploadItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupCreateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupsByExpoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhotoFacesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_background_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhotoFacesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_expo_v1_background_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   106,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_expo_v1_background_proto_goTypes,
		DependencyIndexes: file_expo_v1_background_proto_depIdxs,
		EnumInfos:         file_expo_v1_background_proto_enumTypes,
		MessageInfos:      file_expo_v1_background_proto_msgTypes,
	}.Build()
	File_expo_v1_background_proto = out.File
	file_expo_v1_background_proto_rawDesc = nil
	file_expo_v1_background_proto_goTypes = nil
	file_expo_v1_background_proto_depIdxs = nil
}
