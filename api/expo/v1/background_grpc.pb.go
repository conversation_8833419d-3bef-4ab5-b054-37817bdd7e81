// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: expo/v1/background.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "wiki_user_center/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Background_Healthy_FullMethodName                = "/api.expo.v1.Background/Healthy"
	Background_ListLiveImage_FullMethodName          = "/api.expo.v1.Background/ListLiveImage"
	Background_AddLiveImage_FullMethodName           = "/api.expo.v1.Background/AddLiveImage"
	Background_DeleteLiveImage_FullMethodName        = "/api.expo.v1.Background/DeleteLiveImage"
	Background_SyncLiveImages_FullMethodName         = "/api.expo.v1.Background/SyncLiveImages"
	Background_GetSyncStatus_FullMethodName          = "/api.expo.v1.Background/GetSyncStatus"
	Background_AddGuest_FullMethodName               = "/api.expo.v1.Background/AddGuest"
	Background_GetGuest_FullMethodName               = "/api.expo.v1.Background/GetGuest"
	Background_UpdateGuest_FullMethodName            = "/api.expo.v1.Background/UpdateGuest"
	Background_SetGuestEnable_FullMethodName         = "/api.expo.v1.Background/SetGuestEnable"
	Background_ListGuest_FullMethodName              = "/api.expo.v1.Background/ListGuest"
	Background_DeleteGuest_FullMethodName            = "/api.expo.v1.Background/DeleteGuest"
	Background_AddExpoCommunity_FullMethodName       = "/api.expo.v1.Background/AddExpoCommunity"
	Background_GetExpoCommunity_FullMethodName       = "/api.expo.v1.Background/GetExpoCommunity"
	Background_UpdateExpoCommunity_FullMethodName    = "/api.expo.v1.Background/UpdateExpoCommunity"
	Background_SetExpoCommunityEnable_FullMethodName = "/api.expo.v1.Background/SetExpoCommunityEnable"
	Background_DeleteExpoCommunity_FullMethodName    = "/api.expo.v1.Background/DeleteExpoCommunity"
	Background_AddHall_FullMethodName                = "/api.expo.v1.Background/AddHall"
	Background_GetHall_FullMethodName                = "/api.expo.v1.Background/GetHall"
	Background_UpdateHall_FullMethodName             = "/api.expo.v1.Background/UpdateHall"
	Background_SetHallEnable_FullMethodName          = "/api.expo.v1.Background/SetHallEnable"
	Background_ListHall_FullMethodName               = "/api.expo.v1.Background/ListHall"
	Background_DeleteHall_FullMethodName             = "/api.expo.v1.Background/DeleteHall"
	Background_AddExpoGuest_FullMethodName           = "/api.expo.v1.Background/AddExpoGuest"
	Background_GetExpoGuest_FullMethodName           = "/api.expo.v1.Background/GetExpoGuest"
	Background_ListExpoGuest_FullMethodName          = "/api.expo.v1.Background/ListExpoGuest"
	Background_DeleteExpoGuest_FullMethodName        = "/api.expo.v1.Background/DeleteExpoGuest"
	Background_AddExpoSchedule_FullMethodName        = "/api.expo.v1.Background/AddExpoSchedule"
	Background_GetExpoSchedule_FullMethodName        = "/api.expo.v1.Background/GetExpoSchedule"
	Background_UpdateExpoSchedule_FullMethodName     = "/api.expo.v1.Background/UpdateExpoSchedule"
	Background_ListExpoSchedule_FullMethodName       = "/api.expo.v1.Background/ListExpoSchedule"
	Background_DeleteExpoSchedule_FullMethodName     = "/api.expo.v1.Background/DeleteExpoSchedule"
	Background_AddExpoExhibitor_FullMethodName       = "/api.expo.v1.Background/AddExpoExhibitor"
	Background_GetExpoExhibitor_FullMethodName       = "/api.expo.v1.Background/GetExpoExhibitor"
	Background_UpdateExpoExhibitor_FullMethodName    = "/api.expo.v1.Background/UpdateExpoExhibitor"
	Background_SetExpoExhibitorEnable_FullMethodName = "/api.expo.v1.Background/SetExpoExhibitorEnable"
	Background_ListExpoExhibitor_FullMethodName      = "/api.expo.v1.Background/ListExpoExhibitor"
	Background_DeleteExpoExhibitor_FullMethodName    = "/api.expo.v1.Background/DeleteExpoExhibitor"
	Background_AddExpoGuide_FullMethodName           = "/api.expo.v1.Background/AddExpoGuide"
	Background_GetExpoGuide_FullMethodName           = "/api.expo.v1.Background/GetExpoGuide"
	Background_UpdateExpoGuide_FullMethodName        = "/api.expo.v1.Background/UpdateExpoGuide"
	Background_SetExpoGuideEnable_FullMethodName     = "/api.expo.v1.Background/SetExpoGuideEnable"
	Background_ListExpoGuide_FullMethodName          = "/api.expo.v1.Background/ListExpoGuide"
	Background_DeleteExpoGuide_FullMethodName        = "/api.expo.v1.Background/DeleteExpoGuide"
	Background_GetExpoPartnerType_FullMethodName     = "/api.expo.v1.Background/GetExpoPartnerType"
	Background_AddExpoPartner_FullMethodName         = "/api.expo.v1.Background/AddExpoPartner"
	Background_GetExpoPartner_FullMethodName         = "/api.expo.v1.Background/GetExpoPartner"
	Background_UpdateExpoPartner_FullMethodName      = "/api.expo.v1.Background/UpdateExpoPartner"
	Background_SetExpoPartnerEnable_FullMethodName   = "/api.expo.v1.Background/SetExpoPartnerEnable"
	Background_ListExpoPartner_FullMethodName        = "/api.expo.v1.Background/ListExpoPartner"
	Background_DeleteExpoPartner_FullMethodName      = "/api.expo.v1.Background/DeleteExpoPartner"
	Background_AddExpoReview_FullMethodName          = "/api.expo.v1.Background/AddExpoReview"
	Background_GetExpoReview_FullMethodName          = "/api.expo.v1.Background/GetExpoReview"
	Background_UpdateExpoReview_FullMethodName       = "/api.expo.v1.Background/UpdateExpoReview"
	Background_SetExpoReviewEnable_FullMethodName    = "/api.expo.v1.Background/SetExpoReviewEnable"
	Background_ListExpoReview_FullMethodName         = "/api.expo.v1.Background/ListExpoReview"
	Background_DeleteExpoReview_FullMethodName       = "/api.expo.v1.Background/DeleteExpoReview"
	Background_AddExpoLive_FullMethodName            = "/api.expo.v1.Background/AddExpoLive"
	Background_GetExpoLive_FullMethodName            = "/api.expo.v1.Background/GetExpoLive"
	Background_UpdateExpoLive_FullMethodName         = "/api.expo.v1.Background/UpdateExpoLive"
	Background_SetExpoLiveEnable_FullMethodName      = "/api.expo.v1.Background/SetExpoLiveEnable"
	Background_ListExpoLive_FullMethodName           = "/api.expo.v1.Background/ListExpoLive"
	Background_DeleteExpoLive_FullMethodName         = "/api.expo.v1.Background/DeleteExpoLive"
	Background_FacePhotoUpload_FullMethodName        = "/api.expo.v1.Background/FacePhotoUpload"
	Background_FaceGroupCreate_FullMethodName        = "/api.expo.v1.Background/FaceGroupCreate"
	Background_GetFaceGroupInfo_FullMethodName       = "/api.expo.v1.Background/GetFaceGroupInfo"
	Background_FaceGroupList_FullMethodName          = "/api.expo.v1.Background/FaceGroupList"
	Background_FaceGroupsByExpo_FullMethodName       = "/api.expo.v1.Background/FaceGroupsByExpo"
	Background_FacesPhotoList_FullMethodName         = "/api.expo.v1.Background/FacesPhotoList"
)

// BackgroundClient is the client API for Background service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BackgroundClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// ===========================================================
	// =========================== 图片直播 =======================
	// ===========================================================
	ListLiveImage(ctx context.Context, in *ListLiveImageRequest, opts ...grpc.CallOption) (*ListLiveImageReply, error)
	AddLiveImage(ctx context.Context, in *AddLiveImageRequest, opts ...grpc.CallOption) (*AddLiveImageReply, error)
	DeleteLiveImage(ctx context.Context, in *DeleteLiveImageRequest, opts ...grpc.CallOption) (*DeleteLiveImageReply, error)
	SyncLiveImages(ctx context.Context, in *SyncLiveImagesRequest, opts ...grpc.CallOption) (*SyncLiveImagesReply, error)
	// 查询展会图片同步状态
	GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...grpc.CallOption) (*GetSyncStatusReply, error)
	// ===========================================================
	// =========================== 嘉宾 ===========================
	// ===========================================================
	AddGuest(ctx context.Context, in *Guest, opts ...grpc.CallOption) (*AddGuestReply, error)
	GetGuest(ctx context.Context, in *GetGuestRequest, opts ...grpc.CallOption) (*Guest, error)
	UpdateGuest(ctx context.Context, in *Guest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetGuestEnable(ctx context.Context, in *SetGuestEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListGuest(ctx context.Context, in *ListGuestRequest, opts ...grpc.CallOption) (*ListGuestReply, error)
	DeleteGuest(ctx context.Context, in *DeleteGuestRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会社区 =======================
	// ===========================================================
	AddExpoCommunity(ctx context.Context, in *ExpoCommunity, opts ...grpc.CallOption) (*common.EmptyReply, error)
	GetExpoCommunity(ctx context.Context, in *GetExpoCommunityRequest, opts ...grpc.CallOption) (*ExpoCommunity, error)
	UpdateExpoCommunity(ctx context.Context, in *ExpoCommunity, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetExpoCommunityEnable(ctx context.Context, in *SetExpoCommunityEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	DeleteExpoCommunity(ctx context.Context, in *DeleteExpoCommunityRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会会场 =======================
	// ===========================================================
	AddHall(ctx context.Context, in *ExpoHall, opts ...grpc.CallOption) (*AddGuestReply, error)
	GetHall(ctx context.Context, in *GetHallRequest, opts ...grpc.CallOption) (*ExpoHall, error)
	UpdateHall(ctx context.Context, in *ExpoHall, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetHallEnable(ctx context.Context, in *SetHallEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListHall(ctx context.Context, in *ListHallRequest, opts ...grpc.CallOption) (*ListHallReply, error)
	DeleteHall(ctx context.Context, in *DeleteHallRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会嘉宾 =======================
	// ===========================================================
	AddExpoGuest(ctx context.Context, in *ExpoGuest, opts ...grpc.CallOption) (*AddExpoGuestReply, error)
	GetExpoGuest(ctx context.Context, in *GetExpoGuestRequest, opts ...grpc.CallOption) (*ExpoGuest, error)
	ListExpoGuest(ctx context.Context, in *ListExpoGuestRequest, opts ...grpc.CallOption) (*ListExpoGuestReply, error)
	DeleteExpoGuest(ctx context.Context, in *DeleteExpoGuestRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会议程 =======================
	// ===========================================================
	AddExpoSchedule(ctx context.Context, in *ExpoScheduleInfo, opts ...grpc.CallOption) (*AddExpoScheduleReply, error)
	GetExpoSchedule(ctx context.Context, in *GetExpoScheduleRequest, opts ...grpc.CallOption) (*ExpoScheduleInfo, error)
	UpdateExpoSchedule(ctx context.Context, in *ExpoScheduleInfo, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListExpoSchedule(ctx context.Context, in *ListExpoScheduleRequest, opts ...grpc.CallOption) (*ListExpoScheduleReply, error)
	DeleteExpoSchedule(ctx context.Context, in *DeleteExpoScheduleRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会参展商 =========================
	// ===========================================================
	AddExpoExhibitor(ctx context.Context, in *ExpoExhibitorInfo, opts ...grpc.CallOption) (*AddExpoExhibitorReply, error)
	GetExpoExhibitor(ctx context.Context, in *GetExpoExhibitorRequest, opts ...grpc.CallOption) (*ExpoExhibitorInfo, error)
	UpdateExpoExhibitor(ctx context.Context, in *ExpoExhibitorInfo, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetExpoExhibitorEnable(ctx context.Context, in *SetExpoExhibitorEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListExpoExhibitor(ctx context.Context, in *ListExpoExhibitorRequest, opts ...grpc.CallOption) (*ListExpoExhibitorReply, error)
	DeleteExpoExhibitor(ctx context.Context, in *DeleteExpoExhibitorRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会指南 =======================
	// ===========================================================
	AddExpoGuide(ctx context.Context, in *ExpoGuideInfo, opts ...grpc.CallOption) (*AddExpoGuideReply, error)
	GetExpoGuide(ctx context.Context, in *GetExpoGuideRequest, opts ...grpc.CallOption) (*ExpoGuideInfo, error)
	UpdateExpoGuide(ctx context.Context, in *ExpoGuideInfo, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetExpoGuideEnable(ctx context.Context, in *SetExpoGuideEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListExpoGuide(ctx context.Context, in *ListExpoGuideRequest, opts ...grpc.CallOption) (*ListExpoGuideReply, error)
	DeleteExpoGuide(ctx context.Context, in *DeleteExpoGuideRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 合作伙伴 =======================
	// ===========================================================
	GetExpoPartnerType(ctx context.Context, in *GetExpoPartnerTypeRequest, opts ...grpc.CallOption) (*GetExpoPartnerTypeReply, error)
	AddExpoPartner(ctx context.Context, in *ExpoPartnerInfo, opts ...grpc.CallOption) (*AddExpoPartnerReply, error)
	GetExpoPartner(ctx context.Context, in *GetExpoPartnerRequest, opts ...grpc.CallOption) (*ExpoPartnerInfo, error)
	UpdateExpoPartner(ctx context.Context, in *ExpoPartnerInfo, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetExpoPartnerEnable(ctx context.Context, in *SetExpoPartnerEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListExpoPartner(ctx context.Context, in *ListExpoPartnerRequest, opts ...grpc.CallOption) (*ListExpoPartnerReply, error)
	DeleteExpoPartner(ctx context.Context, in *DeleteExpoPartnerRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会回顾 =======================
	// ===========================================================
	AddExpoReview(ctx context.Context, in *ExpoReviewInfo, opts ...grpc.CallOption) (*AddExpoReviewReply, error)
	GetExpoReview(ctx context.Context, in *GetExpoReviewRequest, opts ...grpc.CallOption) (*ExpoReviewInfo, error)
	UpdateExpoReview(ctx context.Context, in *ExpoReviewInfo, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetExpoReviewEnable(ctx context.Context, in *SetExpoReviewEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListExpoReview(ctx context.Context, in *ListExpoReviewRequest, opts ...grpc.CallOption) (*ListExpoReviewReply, error)
	DeleteExpoReview(ctx context.Context, in *DeleteExpoReviewRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会直播 =======================
	// ===========================================================
	AddExpoLive(ctx context.Context, in *ExpoLive, opts ...grpc.CallOption) (*AddExpoLiveReply, error)
	GetExpoLive(ctx context.Context, in *GetExpoLiveRequest, opts ...grpc.CallOption) (*ExpoLive, error)
	UpdateExpoLive(ctx context.Context, in *ExpoLive, opts ...grpc.CallOption) (*common.EmptyReply, error)
	SetExpoLiveEnable(ctx context.Context, in *SetExpoLiveEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	ListExpoLive(ctx context.Context, in *ListExpoLiveRequest, opts ...grpc.CallOption) (*ListExpoLiveReply, error)
	DeleteExpoLive(ctx context.Context, in *DeleteExpoLiveRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 展会照片上传与人脸识别
	FacePhotoUpload(ctx context.Context, in *FacePhotoUploadRequest, opts ...grpc.CallOption) (*FacePhotoUploadReply, error)
	// 创建人员库
	FaceGroupCreate(ctx context.Context, in *FaceGroupCreateRequest, opts ...grpc.CallOption) (*FaceGroupCreateReply, error)
	// 获取人员库信息
	GetFaceGroupInfo(ctx context.Context, in *FaceGroupInfoRequest, opts ...grpc.CallOption) (*FaceGroupInfo, error)
	// 获取人员库列表
	FaceGroupList(ctx context.Context, in *FaceGroupListRequest, opts ...grpc.CallOption) (*FaceGroupListReply, error)
	// 根据展会ID获取人员库
	FaceGroupsByExpo(ctx context.Context, in *FaceGroupsByExpoRequest, opts ...grpc.CallOption) (*FaceGroupInfo, error)
	// 照片列表
	FacesPhotoList(ctx context.Context, in *PhotoFacesRequest, opts ...grpc.CallOption) (*PhotoFacesReply, error)
}

type backgroundClient struct {
	cc grpc.ClientConnInterface
}

func NewBackgroundClient(cc grpc.ClientConnInterface) BackgroundClient {
	return &backgroundClient{cc}
}

func (c *backgroundClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Background_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListLiveImage(ctx context.Context, in *ListLiveImageRequest, opts ...grpc.CallOption) (*ListLiveImageReply, error) {
	out := new(ListLiveImageReply)
	err := c.cc.Invoke(ctx, Background_ListLiveImage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddLiveImage(ctx context.Context, in *AddLiveImageRequest, opts ...grpc.CallOption) (*AddLiveImageReply, error) {
	out := new(AddLiveImageReply)
	err := c.cc.Invoke(ctx, Background_AddLiveImage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteLiveImage(ctx context.Context, in *DeleteLiveImageRequest, opts ...grpc.CallOption) (*DeleteLiveImageReply, error) {
	out := new(DeleteLiveImageReply)
	err := c.cc.Invoke(ctx, Background_DeleteLiveImage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SyncLiveImages(ctx context.Context, in *SyncLiveImagesRequest, opts ...grpc.CallOption) (*SyncLiveImagesReply, error) {
	out := new(SyncLiveImagesReply)
	err := c.cc.Invoke(ctx, Background_SyncLiveImages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...grpc.CallOption) (*GetSyncStatusReply, error) {
	out := new(GetSyncStatusReply)
	err := c.cc.Invoke(ctx, Background_GetSyncStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddGuest(ctx context.Context, in *Guest, opts ...grpc.CallOption) (*AddGuestReply, error) {
	out := new(AddGuestReply)
	err := c.cc.Invoke(ctx, Background_AddGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetGuest(ctx context.Context, in *GetGuestRequest, opts ...grpc.CallOption) (*Guest, error) {
	out := new(Guest)
	err := c.cc.Invoke(ctx, Background_GetGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateGuest(ctx context.Context, in *Guest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetGuestEnable(ctx context.Context, in *SetGuestEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetGuestEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListGuest(ctx context.Context, in *ListGuestRequest, opts ...grpc.CallOption) (*ListGuestReply, error) {
	out := new(ListGuestReply)
	err := c.cc.Invoke(ctx, Background_ListGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteGuest(ctx context.Context, in *DeleteGuestRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoCommunity(ctx context.Context, in *ExpoCommunity, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_AddExpoCommunity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoCommunity(ctx context.Context, in *GetExpoCommunityRequest, opts ...grpc.CallOption) (*ExpoCommunity, error) {
	out := new(ExpoCommunity)
	err := c.cc.Invoke(ctx, Background_GetExpoCommunity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoCommunity(ctx context.Context, in *ExpoCommunity, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoCommunity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetExpoCommunityEnable(ctx context.Context, in *SetExpoCommunityEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetExpoCommunityEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoCommunity(ctx context.Context, in *DeleteExpoCommunityRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoCommunity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddHall(ctx context.Context, in *ExpoHall, opts ...grpc.CallOption) (*AddGuestReply, error) {
	out := new(AddGuestReply)
	err := c.cc.Invoke(ctx, Background_AddHall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetHall(ctx context.Context, in *GetHallRequest, opts ...grpc.CallOption) (*ExpoHall, error) {
	out := new(ExpoHall)
	err := c.cc.Invoke(ctx, Background_GetHall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateHall(ctx context.Context, in *ExpoHall, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateHall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetHallEnable(ctx context.Context, in *SetHallEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetHallEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListHall(ctx context.Context, in *ListHallRequest, opts ...grpc.CallOption) (*ListHallReply, error) {
	out := new(ListHallReply)
	err := c.cc.Invoke(ctx, Background_ListHall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteHall(ctx context.Context, in *DeleteHallRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteHall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoGuest(ctx context.Context, in *ExpoGuest, opts ...grpc.CallOption) (*AddExpoGuestReply, error) {
	out := new(AddExpoGuestReply)
	err := c.cc.Invoke(ctx, Background_AddExpoGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoGuest(ctx context.Context, in *GetExpoGuestRequest, opts ...grpc.CallOption) (*ExpoGuest, error) {
	out := new(ExpoGuest)
	err := c.cc.Invoke(ctx, Background_GetExpoGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoGuest(ctx context.Context, in *ListExpoGuestRequest, opts ...grpc.CallOption) (*ListExpoGuestReply, error) {
	out := new(ListExpoGuestReply)
	err := c.cc.Invoke(ctx, Background_ListExpoGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoGuest(ctx context.Context, in *DeleteExpoGuestRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoGuest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoSchedule(ctx context.Context, in *ExpoScheduleInfo, opts ...grpc.CallOption) (*AddExpoScheduleReply, error) {
	out := new(AddExpoScheduleReply)
	err := c.cc.Invoke(ctx, Background_AddExpoSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoSchedule(ctx context.Context, in *GetExpoScheduleRequest, opts ...grpc.CallOption) (*ExpoScheduleInfo, error) {
	out := new(ExpoScheduleInfo)
	err := c.cc.Invoke(ctx, Background_GetExpoSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoSchedule(ctx context.Context, in *ExpoScheduleInfo, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoSchedule(ctx context.Context, in *ListExpoScheduleRequest, opts ...grpc.CallOption) (*ListExpoScheduleReply, error) {
	out := new(ListExpoScheduleReply)
	err := c.cc.Invoke(ctx, Background_ListExpoSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoSchedule(ctx context.Context, in *DeleteExpoScheduleRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoExhibitor(ctx context.Context, in *ExpoExhibitorInfo, opts ...grpc.CallOption) (*AddExpoExhibitorReply, error) {
	out := new(AddExpoExhibitorReply)
	err := c.cc.Invoke(ctx, Background_AddExpoExhibitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoExhibitor(ctx context.Context, in *GetExpoExhibitorRequest, opts ...grpc.CallOption) (*ExpoExhibitorInfo, error) {
	out := new(ExpoExhibitorInfo)
	err := c.cc.Invoke(ctx, Background_GetExpoExhibitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoExhibitor(ctx context.Context, in *ExpoExhibitorInfo, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoExhibitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetExpoExhibitorEnable(ctx context.Context, in *SetExpoExhibitorEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetExpoExhibitorEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoExhibitor(ctx context.Context, in *ListExpoExhibitorRequest, opts ...grpc.CallOption) (*ListExpoExhibitorReply, error) {
	out := new(ListExpoExhibitorReply)
	err := c.cc.Invoke(ctx, Background_ListExpoExhibitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoExhibitor(ctx context.Context, in *DeleteExpoExhibitorRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoExhibitor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoGuide(ctx context.Context, in *ExpoGuideInfo, opts ...grpc.CallOption) (*AddExpoGuideReply, error) {
	out := new(AddExpoGuideReply)
	err := c.cc.Invoke(ctx, Background_AddExpoGuide_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoGuide(ctx context.Context, in *GetExpoGuideRequest, opts ...grpc.CallOption) (*ExpoGuideInfo, error) {
	out := new(ExpoGuideInfo)
	err := c.cc.Invoke(ctx, Background_GetExpoGuide_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoGuide(ctx context.Context, in *ExpoGuideInfo, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoGuide_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetExpoGuideEnable(ctx context.Context, in *SetExpoGuideEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetExpoGuideEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoGuide(ctx context.Context, in *ListExpoGuideRequest, opts ...grpc.CallOption) (*ListExpoGuideReply, error) {
	out := new(ListExpoGuideReply)
	err := c.cc.Invoke(ctx, Background_ListExpoGuide_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoGuide(ctx context.Context, in *DeleteExpoGuideRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoGuide_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoPartnerType(ctx context.Context, in *GetExpoPartnerTypeRequest, opts ...grpc.CallOption) (*GetExpoPartnerTypeReply, error) {
	out := new(GetExpoPartnerTypeReply)
	err := c.cc.Invoke(ctx, Background_GetExpoPartnerType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoPartner(ctx context.Context, in *ExpoPartnerInfo, opts ...grpc.CallOption) (*AddExpoPartnerReply, error) {
	out := new(AddExpoPartnerReply)
	err := c.cc.Invoke(ctx, Background_AddExpoPartner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoPartner(ctx context.Context, in *GetExpoPartnerRequest, opts ...grpc.CallOption) (*ExpoPartnerInfo, error) {
	out := new(ExpoPartnerInfo)
	err := c.cc.Invoke(ctx, Background_GetExpoPartner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoPartner(ctx context.Context, in *ExpoPartnerInfo, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoPartner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetExpoPartnerEnable(ctx context.Context, in *SetExpoPartnerEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetExpoPartnerEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoPartner(ctx context.Context, in *ListExpoPartnerRequest, opts ...grpc.CallOption) (*ListExpoPartnerReply, error) {
	out := new(ListExpoPartnerReply)
	err := c.cc.Invoke(ctx, Background_ListExpoPartner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoPartner(ctx context.Context, in *DeleteExpoPartnerRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoPartner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoReview(ctx context.Context, in *ExpoReviewInfo, opts ...grpc.CallOption) (*AddExpoReviewReply, error) {
	out := new(AddExpoReviewReply)
	err := c.cc.Invoke(ctx, Background_AddExpoReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoReview(ctx context.Context, in *GetExpoReviewRequest, opts ...grpc.CallOption) (*ExpoReviewInfo, error) {
	out := new(ExpoReviewInfo)
	err := c.cc.Invoke(ctx, Background_GetExpoReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoReview(ctx context.Context, in *ExpoReviewInfo, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetExpoReviewEnable(ctx context.Context, in *SetExpoReviewEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetExpoReviewEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoReview(ctx context.Context, in *ListExpoReviewRequest, opts ...grpc.CallOption) (*ListExpoReviewReply, error) {
	out := new(ListExpoReviewReply)
	err := c.cc.Invoke(ctx, Background_ListExpoReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoReview(ctx context.Context, in *DeleteExpoReviewRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddExpoLive(ctx context.Context, in *ExpoLive, opts ...grpc.CallOption) (*AddExpoLiveReply, error) {
	out := new(AddExpoLiveReply)
	err := c.cc.Invoke(ctx, Background_AddExpoLive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetExpoLive(ctx context.Context, in *GetExpoLiveRequest, opts ...grpc.CallOption) (*ExpoLive, error) {
	out := new(ExpoLive)
	err := c.cc.Invoke(ctx, Background_GetExpoLive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateExpoLive(ctx context.Context, in *ExpoLive, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateExpoLive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetExpoLiveEnable(ctx context.Context, in *SetExpoLiveEnableRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetExpoLiveEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListExpoLive(ctx context.Context, in *ListExpoLiveRequest, opts ...grpc.CallOption) (*ListExpoLiveReply, error) {
	out := new(ListExpoLiveReply)
	err := c.cc.Invoke(ctx, Background_ListExpoLive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteExpoLive(ctx context.Context, in *DeleteExpoLiveRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteExpoLive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) FacePhotoUpload(ctx context.Context, in *FacePhotoUploadRequest, opts ...grpc.CallOption) (*FacePhotoUploadReply, error) {
	out := new(FacePhotoUploadReply)
	err := c.cc.Invoke(ctx, Background_FacePhotoUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) FaceGroupCreate(ctx context.Context, in *FaceGroupCreateRequest, opts ...grpc.CallOption) (*FaceGroupCreateReply, error) {
	out := new(FaceGroupCreateReply)
	err := c.cc.Invoke(ctx, Background_FaceGroupCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetFaceGroupInfo(ctx context.Context, in *FaceGroupInfoRequest, opts ...grpc.CallOption) (*FaceGroupInfo, error) {
	out := new(FaceGroupInfo)
	err := c.cc.Invoke(ctx, Background_GetFaceGroupInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) FaceGroupList(ctx context.Context, in *FaceGroupListRequest, opts ...grpc.CallOption) (*FaceGroupListReply, error) {
	out := new(FaceGroupListReply)
	err := c.cc.Invoke(ctx, Background_FaceGroupList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) FaceGroupsByExpo(ctx context.Context, in *FaceGroupsByExpoRequest, opts ...grpc.CallOption) (*FaceGroupInfo, error) {
	out := new(FaceGroupInfo)
	err := c.cc.Invoke(ctx, Background_FaceGroupsByExpo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) FacesPhotoList(ctx context.Context, in *PhotoFacesRequest, opts ...grpc.CallOption) (*PhotoFacesReply, error) {
	out := new(PhotoFacesReply)
	err := c.cc.Invoke(ctx, Background_FacesPhotoList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackgroundServer is the server API for Background service.
// All implementations must embed UnimplementedBackgroundServer
// for forward compatibility
type BackgroundServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// ===========================================================
	// =========================== 图片直播 =======================
	// ===========================================================
	ListLiveImage(context.Context, *ListLiveImageRequest) (*ListLiveImageReply, error)
	AddLiveImage(context.Context, *AddLiveImageRequest) (*AddLiveImageReply, error)
	DeleteLiveImage(context.Context, *DeleteLiveImageRequest) (*DeleteLiveImageReply, error)
	SyncLiveImages(context.Context, *SyncLiveImagesRequest) (*SyncLiveImagesReply, error)
	// 查询展会图片同步状态
	GetSyncStatus(context.Context, *GetSyncStatusRequest) (*GetSyncStatusReply, error)
	// ===========================================================
	// =========================== 嘉宾 ===========================
	// ===========================================================
	AddGuest(context.Context, *Guest) (*AddGuestReply, error)
	GetGuest(context.Context, *GetGuestRequest) (*Guest, error)
	UpdateGuest(context.Context, *Guest) (*common.EmptyReply, error)
	SetGuestEnable(context.Context, *SetGuestEnableRequest) (*common.EmptyReply, error)
	ListGuest(context.Context, *ListGuestRequest) (*ListGuestReply, error)
	DeleteGuest(context.Context, *DeleteGuestRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会社区 =======================
	// ===========================================================
	AddExpoCommunity(context.Context, *ExpoCommunity) (*common.EmptyReply, error)
	GetExpoCommunity(context.Context, *GetExpoCommunityRequest) (*ExpoCommunity, error)
	UpdateExpoCommunity(context.Context, *ExpoCommunity) (*common.EmptyReply, error)
	SetExpoCommunityEnable(context.Context, *SetExpoCommunityEnableRequest) (*common.EmptyReply, error)
	DeleteExpoCommunity(context.Context, *DeleteExpoCommunityRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会会场 =======================
	// ===========================================================
	AddHall(context.Context, *ExpoHall) (*AddGuestReply, error)
	GetHall(context.Context, *GetHallRequest) (*ExpoHall, error)
	UpdateHall(context.Context, *ExpoHall) (*common.EmptyReply, error)
	SetHallEnable(context.Context, *SetHallEnableRequest) (*common.EmptyReply, error)
	ListHall(context.Context, *ListHallRequest) (*ListHallReply, error)
	DeleteHall(context.Context, *DeleteHallRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会嘉宾 =======================
	// ===========================================================
	AddExpoGuest(context.Context, *ExpoGuest) (*AddExpoGuestReply, error)
	GetExpoGuest(context.Context, *GetExpoGuestRequest) (*ExpoGuest, error)
	ListExpoGuest(context.Context, *ListExpoGuestRequest) (*ListExpoGuestReply, error)
	DeleteExpoGuest(context.Context, *DeleteExpoGuestRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会议程 =======================
	// ===========================================================
	AddExpoSchedule(context.Context, *ExpoScheduleInfo) (*AddExpoScheduleReply, error)
	GetExpoSchedule(context.Context, *GetExpoScheduleRequest) (*ExpoScheduleInfo, error)
	UpdateExpoSchedule(context.Context, *ExpoScheduleInfo) (*common.EmptyReply, error)
	ListExpoSchedule(context.Context, *ListExpoScheduleRequest) (*ListExpoScheduleReply, error)
	DeleteExpoSchedule(context.Context, *DeleteExpoScheduleRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会参展商 =========================
	// ===========================================================
	AddExpoExhibitor(context.Context, *ExpoExhibitorInfo) (*AddExpoExhibitorReply, error)
	GetExpoExhibitor(context.Context, *GetExpoExhibitorRequest) (*ExpoExhibitorInfo, error)
	UpdateExpoExhibitor(context.Context, *ExpoExhibitorInfo) (*common.EmptyReply, error)
	SetExpoExhibitorEnable(context.Context, *SetExpoExhibitorEnableRequest) (*common.EmptyReply, error)
	ListExpoExhibitor(context.Context, *ListExpoExhibitorRequest) (*ListExpoExhibitorReply, error)
	DeleteExpoExhibitor(context.Context, *DeleteExpoExhibitorRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会指南 =======================
	// ===========================================================
	AddExpoGuide(context.Context, *ExpoGuideInfo) (*AddExpoGuideReply, error)
	GetExpoGuide(context.Context, *GetExpoGuideRequest) (*ExpoGuideInfo, error)
	UpdateExpoGuide(context.Context, *ExpoGuideInfo) (*common.EmptyReply, error)
	SetExpoGuideEnable(context.Context, *SetExpoGuideEnableRequest) (*common.EmptyReply, error)
	ListExpoGuide(context.Context, *ListExpoGuideRequest) (*ListExpoGuideReply, error)
	DeleteExpoGuide(context.Context, *DeleteExpoGuideRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 合作伙伴 =======================
	// ===========================================================
	GetExpoPartnerType(context.Context, *GetExpoPartnerTypeRequest) (*GetExpoPartnerTypeReply, error)
	AddExpoPartner(context.Context, *ExpoPartnerInfo) (*AddExpoPartnerReply, error)
	GetExpoPartner(context.Context, *GetExpoPartnerRequest) (*ExpoPartnerInfo, error)
	UpdateExpoPartner(context.Context, *ExpoPartnerInfo) (*common.EmptyReply, error)
	SetExpoPartnerEnable(context.Context, *SetExpoPartnerEnableRequest) (*common.EmptyReply, error)
	ListExpoPartner(context.Context, *ListExpoPartnerRequest) (*ListExpoPartnerReply, error)
	DeleteExpoPartner(context.Context, *DeleteExpoPartnerRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会回顾 =======================
	// ===========================================================
	AddExpoReview(context.Context, *ExpoReviewInfo) (*AddExpoReviewReply, error)
	GetExpoReview(context.Context, *GetExpoReviewRequest) (*ExpoReviewInfo, error)
	UpdateExpoReview(context.Context, *ExpoReviewInfo) (*common.EmptyReply, error)
	SetExpoReviewEnable(context.Context, *SetExpoReviewEnableRequest) (*common.EmptyReply, error)
	ListExpoReview(context.Context, *ListExpoReviewRequest) (*ListExpoReviewReply, error)
	DeleteExpoReview(context.Context, *DeleteExpoReviewRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 展会直播 =======================
	// ===========================================================
	AddExpoLive(context.Context, *ExpoLive) (*AddExpoLiveReply, error)
	GetExpoLive(context.Context, *GetExpoLiveRequest) (*ExpoLive, error)
	UpdateExpoLive(context.Context, *ExpoLive) (*common.EmptyReply, error)
	SetExpoLiveEnable(context.Context, *SetExpoLiveEnableRequest) (*common.EmptyReply, error)
	ListExpoLive(context.Context, *ListExpoLiveRequest) (*ListExpoLiveReply, error)
	DeleteExpoLive(context.Context, *DeleteExpoLiveRequest) (*common.EmptyReply, error)
	// 展会照片上传与人脸识别
	FacePhotoUpload(context.Context, *FacePhotoUploadRequest) (*FacePhotoUploadReply, error)
	// 创建人员库
	FaceGroupCreate(context.Context, *FaceGroupCreateRequest) (*FaceGroupCreateReply, error)
	// 获取人员库信息
	GetFaceGroupInfo(context.Context, *FaceGroupInfoRequest) (*FaceGroupInfo, error)
	// 获取人员库列表
	FaceGroupList(context.Context, *FaceGroupListRequest) (*FaceGroupListReply, error)
	// 根据展会ID获取人员库
	FaceGroupsByExpo(context.Context, *FaceGroupsByExpoRequest) (*FaceGroupInfo, error)
	// 照片列表
	FacesPhotoList(context.Context, *PhotoFacesRequest) (*PhotoFacesReply, error)
	mustEmbedUnimplementedBackgroundServer()
}

// UnimplementedBackgroundServer must be embedded to have forward compatible implementations.
type UnimplementedBackgroundServer struct {
}

func (UnimplementedBackgroundServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedBackgroundServer) ListLiveImage(context.Context, *ListLiveImageRequest) (*ListLiveImageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLiveImage not implemented")
}
func (UnimplementedBackgroundServer) AddLiveImage(context.Context, *AddLiveImageRequest) (*AddLiveImageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLiveImage not implemented")
}
func (UnimplementedBackgroundServer) DeleteLiveImage(context.Context, *DeleteLiveImageRequest) (*DeleteLiveImageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLiveImage not implemented")
}
func (UnimplementedBackgroundServer) SyncLiveImages(context.Context, *SyncLiveImagesRequest) (*SyncLiveImagesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncLiveImages not implemented")
}
func (UnimplementedBackgroundServer) GetSyncStatus(context.Context, *GetSyncStatusRequest) (*GetSyncStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSyncStatus not implemented")
}
func (UnimplementedBackgroundServer) AddGuest(context.Context, *Guest) (*AddGuestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGuest not implemented")
}
func (UnimplementedBackgroundServer) GetGuest(context.Context, *GetGuestRequest) (*Guest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGuest not implemented")
}
func (UnimplementedBackgroundServer) UpdateGuest(context.Context, *Guest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGuest not implemented")
}
func (UnimplementedBackgroundServer) SetGuestEnable(context.Context, *SetGuestEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGuestEnable not implemented")
}
func (UnimplementedBackgroundServer) ListGuest(context.Context, *ListGuestRequest) (*ListGuestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGuest not implemented")
}
func (UnimplementedBackgroundServer) DeleteGuest(context.Context, *DeleteGuestRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGuest not implemented")
}
func (UnimplementedBackgroundServer) AddExpoCommunity(context.Context, *ExpoCommunity) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoCommunity not implemented")
}
func (UnimplementedBackgroundServer) GetExpoCommunity(context.Context, *GetExpoCommunityRequest) (*ExpoCommunity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoCommunity not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoCommunity(context.Context, *ExpoCommunity) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoCommunity not implemented")
}
func (UnimplementedBackgroundServer) SetExpoCommunityEnable(context.Context, *SetExpoCommunityEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetExpoCommunityEnable not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoCommunity(context.Context, *DeleteExpoCommunityRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoCommunity not implemented")
}
func (UnimplementedBackgroundServer) AddHall(context.Context, *ExpoHall) (*AddGuestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHall not implemented")
}
func (UnimplementedBackgroundServer) GetHall(context.Context, *GetHallRequest) (*ExpoHall, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHall not implemented")
}
func (UnimplementedBackgroundServer) UpdateHall(context.Context, *ExpoHall) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHall not implemented")
}
func (UnimplementedBackgroundServer) SetHallEnable(context.Context, *SetHallEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetHallEnable not implemented")
}
func (UnimplementedBackgroundServer) ListHall(context.Context, *ListHallRequest) (*ListHallReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHall not implemented")
}
func (UnimplementedBackgroundServer) DeleteHall(context.Context, *DeleteHallRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHall not implemented")
}
func (UnimplementedBackgroundServer) AddExpoGuest(context.Context, *ExpoGuest) (*AddExpoGuestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoGuest not implemented")
}
func (UnimplementedBackgroundServer) GetExpoGuest(context.Context, *GetExpoGuestRequest) (*ExpoGuest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoGuest not implemented")
}
func (UnimplementedBackgroundServer) ListExpoGuest(context.Context, *ListExpoGuestRequest) (*ListExpoGuestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoGuest not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoGuest(context.Context, *DeleteExpoGuestRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoGuest not implemented")
}
func (UnimplementedBackgroundServer) AddExpoSchedule(context.Context, *ExpoScheduleInfo) (*AddExpoScheduleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoSchedule not implemented")
}
func (UnimplementedBackgroundServer) GetExpoSchedule(context.Context, *GetExpoScheduleRequest) (*ExpoScheduleInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoSchedule not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoSchedule(context.Context, *ExpoScheduleInfo) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoSchedule not implemented")
}
func (UnimplementedBackgroundServer) ListExpoSchedule(context.Context, *ListExpoScheduleRequest) (*ListExpoScheduleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoSchedule not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoSchedule(context.Context, *DeleteExpoScheduleRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoSchedule not implemented")
}
func (UnimplementedBackgroundServer) AddExpoExhibitor(context.Context, *ExpoExhibitorInfo) (*AddExpoExhibitorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoExhibitor not implemented")
}
func (UnimplementedBackgroundServer) GetExpoExhibitor(context.Context, *GetExpoExhibitorRequest) (*ExpoExhibitorInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoExhibitor not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoExhibitor(context.Context, *ExpoExhibitorInfo) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoExhibitor not implemented")
}
func (UnimplementedBackgroundServer) SetExpoExhibitorEnable(context.Context, *SetExpoExhibitorEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetExpoExhibitorEnable not implemented")
}
func (UnimplementedBackgroundServer) ListExpoExhibitor(context.Context, *ListExpoExhibitorRequest) (*ListExpoExhibitorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoExhibitor not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoExhibitor(context.Context, *DeleteExpoExhibitorRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoExhibitor not implemented")
}
func (UnimplementedBackgroundServer) AddExpoGuide(context.Context, *ExpoGuideInfo) (*AddExpoGuideReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoGuide not implemented")
}
func (UnimplementedBackgroundServer) GetExpoGuide(context.Context, *GetExpoGuideRequest) (*ExpoGuideInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoGuide not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoGuide(context.Context, *ExpoGuideInfo) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoGuide not implemented")
}
func (UnimplementedBackgroundServer) SetExpoGuideEnable(context.Context, *SetExpoGuideEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetExpoGuideEnable not implemented")
}
func (UnimplementedBackgroundServer) ListExpoGuide(context.Context, *ListExpoGuideRequest) (*ListExpoGuideReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoGuide not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoGuide(context.Context, *DeleteExpoGuideRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoGuide not implemented")
}
func (UnimplementedBackgroundServer) GetExpoPartnerType(context.Context, *GetExpoPartnerTypeRequest) (*GetExpoPartnerTypeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoPartnerType not implemented")
}
func (UnimplementedBackgroundServer) AddExpoPartner(context.Context, *ExpoPartnerInfo) (*AddExpoPartnerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoPartner not implemented")
}
func (UnimplementedBackgroundServer) GetExpoPartner(context.Context, *GetExpoPartnerRequest) (*ExpoPartnerInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoPartner not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoPartner(context.Context, *ExpoPartnerInfo) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoPartner not implemented")
}
func (UnimplementedBackgroundServer) SetExpoPartnerEnable(context.Context, *SetExpoPartnerEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetExpoPartnerEnable not implemented")
}
func (UnimplementedBackgroundServer) ListExpoPartner(context.Context, *ListExpoPartnerRequest) (*ListExpoPartnerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoPartner not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoPartner(context.Context, *DeleteExpoPartnerRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoPartner not implemented")
}
func (UnimplementedBackgroundServer) AddExpoReview(context.Context, *ExpoReviewInfo) (*AddExpoReviewReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoReview not implemented")
}
func (UnimplementedBackgroundServer) GetExpoReview(context.Context, *GetExpoReviewRequest) (*ExpoReviewInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoReview not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoReview(context.Context, *ExpoReviewInfo) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoReview not implemented")
}
func (UnimplementedBackgroundServer) SetExpoReviewEnable(context.Context, *SetExpoReviewEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetExpoReviewEnable not implemented")
}
func (UnimplementedBackgroundServer) ListExpoReview(context.Context, *ListExpoReviewRequest) (*ListExpoReviewReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoReview not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoReview(context.Context, *DeleteExpoReviewRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoReview not implemented")
}
func (UnimplementedBackgroundServer) AddExpoLive(context.Context, *ExpoLive) (*AddExpoLiveReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExpoLive not implemented")
}
func (UnimplementedBackgroundServer) GetExpoLive(context.Context, *GetExpoLiveRequest) (*ExpoLive, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpoLive not implemented")
}
func (UnimplementedBackgroundServer) UpdateExpoLive(context.Context, *ExpoLive) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExpoLive not implemented")
}
func (UnimplementedBackgroundServer) SetExpoLiveEnable(context.Context, *SetExpoLiveEnableRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetExpoLiveEnable not implemented")
}
func (UnimplementedBackgroundServer) ListExpoLive(context.Context, *ListExpoLiveRequest) (*ListExpoLiveReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExpoLive not implemented")
}
func (UnimplementedBackgroundServer) DeleteExpoLive(context.Context, *DeleteExpoLiveRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExpoLive not implemented")
}
func (UnimplementedBackgroundServer) FacePhotoUpload(context.Context, *FacePhotoUploadRequest) (*FacePhotoUploadReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FacePhotoUpload not implemented")
}
func (UnimplementedBackgroundServer) FaceGroupCreate(context.Context, *FaceGroupCreateRequest) (*FaceGroupCreateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceGroupCreate not implemented")
}
func (UnimplementedBackgroundServer) GetFaceGroupInfo(context.Context, *FaceGroupInfoRequest) (*FaceGroupInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFaceGroupInfo not implemented")
}
func (UnimplementedBackgroundServer) FaceGroupList(context.Context, *FaceGroupListRequest) (*FaceGroupListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceGroupList not implemented")
}
func (UnimplementedBackgroundServer) FaceGroupsByExpo(context.Context, *FaceGroupsByExpoRequest) (*FaceGroupInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FaceGroupsByExpo not implemented")
}
func (UnimplementedBackgroundServer) FacesPhotoList(context.Context, *PhotoFacesRequest) (*PhotoFacesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FacesPhotoList not implemented")
}
func (UnimplementedBackgroundServer) mustEmbedUnimplementedBackgroundServer() {}

// UnsafeBackgroundServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BackgroundServer will
// result in compilation errors.
type UnsafeBackgroundServer interface {
	mustEmbedUnimplementedBackgroundServer()
}

func RegisterBackgroundServer(s grpc.ServiceRegistrar, srv BackgroundServer) {
	s.RegisterService(&Background_ServiceDesc, srv)
}

func _Background_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListLiveImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLiveImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListLiveImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListLiveImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListLiveImage(ctx, req.(*ListLiveImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddLiveImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLiveImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddLiveImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddLiveImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddLiveImage(ctx, req.(*AddLiveImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteLiveImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLiveImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteLiveImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteLiveImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteLiveImage(ctx, req.(*DeleteLiveImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SyncLiveImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncLiveImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SyncLiveImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SyncLiveImages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SyncLiveImages(ctx, req.(*SyncLiveImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetSyncStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetSyncStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetSyncStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetSyncStatus(ctx, req.(*GetSyncStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Guest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddGuest(ctx, req.(*Guest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetGuest(ctx, req.(*GetGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Guest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateGuest(ctx, req.(*Guest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetGuestEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGuestEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetGuestEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetGuestEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetGuestEnable(ctx, req.(*SetGuestEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListGuest(ctx, req.(*ListGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteGuest(ctx, req.(*DeleteGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoCommunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoCommunity)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoCommunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoCommunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoCommunity(ctx, req.(*ExpoCommunity))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoCommunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoCommunityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoCommunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoCommunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoCommunity(ctx, req.(*GetExpoCommunityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoCommunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoCommunity)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoCommunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoCommunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoCommunity(ctx, req.(*ExpoCommunity))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetExpoCommunityEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExpoCommunityEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetExpoCommunityEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetExpoCommunityEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetExpoCommunityEnable(ctx, req.(*SetExpoCommunityEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoCommunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoCommunityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoCommunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoCommunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoCommunity(ctx, req.(*DeleteExpoCommunityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoHall)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddHall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddHall(ctx, req.(*ExpoHall))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetHall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetHall(ctx, req.(*GetHallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoHall)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateHall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateHall(ctx, req.(*ExpoHall))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetHallEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHallEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetHallEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetHallEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetHallEnable(ctx, req.(*SetHallEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListHallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListHall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListHall(ctx, req.(*ListHallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteHall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteHall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteHall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteHall(ctx, req.(*DeleteHallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoGuest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoGuest(ctx, req.(*ExpoGuest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoGuest(ctx, req.(*GetExpoGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoGuest(ctx, req.(*ListExpoGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoGuest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoGuest(ctx, req.(*DeleteExpoGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoScheduleInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoSchedule(ctx, req.(*ExpoScheduleInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoSchedule(ctx, req.(*GetExpoScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoScheduleInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoSchedule(ctx, req.(*ExpoScheduleInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoSchedule(ctx, req.(*ListExpoScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoSchedule(ctx, req.(*DeleteExpoScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoExhibitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoExhibitorInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoExhibitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoExhibitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoExhibitor(ctx, req.(*ExpoExhibitorInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoExhibitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoExhibitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoExhibitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoExhibitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoExhibitor(ctx, req.(*GetExpoExhibitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoExhibitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoExhibitorInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoExhibitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoExhibitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoExhibitor(ctx, req.(*ExpoExhibitorInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetExpoExhibitorEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExpoExhibitorEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetExpoExhibitorEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetExpoExhibitorEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetExpoExhibitorEnable(ctx, req.(*SetExpoExhibitorEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoExhibitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoExhibitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoExhibitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoExhibitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoExhibitor(ctx, req.(*ListExpoExhibitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoExhibitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoExhibitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoExhibitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoExhibitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoExhibitor(ctx, req.(*DeleteExpoExhibitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoGuideInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoGuide(ctx, req.(*ExpoGuideInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoGuideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoGuide(ctx, req.(*GetExpoGuideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoGuideInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoGuide(ctx, req.(*ExpoGuideInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetExpoGuideEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExpoGuideEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetExpoGuideEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetExpoGuideEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetExpoGuideEnable(ctx, req.(*SetExpoGuideEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoGuideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoGuide(ctx, req.(*ListExpoGuideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoGuideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoGuide(ctx, req.(*DeleteExpoGuideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoPartnerType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoPartnerTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoPartnerType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoPartnerType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoPartnerType(ctx, req.(*GetExpoPartnerTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoPartnerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoPartner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoPartner(ctx, req.(*ExpoPartnerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoPartnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoPartner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoPartner(ctx, req.(*GetExpoPartnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoPartnerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoPartner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoPartner(ctx, req.(*ExpoPartnerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetExpoPartnerEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExpoPartnerEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetExpoPartnerEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetExpoPartnerEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetExpoPartnerEnable(ctx, req.(*SetExpoPartnerEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoPartnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoPartner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoPartner(ctx, req.(*ListExpoPartnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoPartner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoPartnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoPartner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoPartner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoPartner(ctx, req.(*DeleteExpoPartnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoReviewInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoReview(ctx, req.(*ExpoReviewInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoReview(ctx, req.(*GetExpoReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoReviewInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoReview(ctx, req.(*ExpoReviewInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetExpoReviewEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExpoReviewEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetExpoReviewEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetExpoReviewEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetExpoReviewEnable(ctx, req.(*SetExpoReviewEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoReview(ctx, req.(*ListExpoReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoReview(ctx, req.(*DeleteExpoReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddExpoLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoLive)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddExpoLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddExpoLive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddExpoLive(ctx, req.(*ExpoLive))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetExpoLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpoLiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetExpoLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetExpoLive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetExpoLive(ctx, req.(*GetExpoLiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateExpoLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpoLive)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateExpoLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateExpoLive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateExpoLive(ctx, req.(*ExpoLive))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetExpoLiveEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExpoLiveEnableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetExpoLiveEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetExpoLiveEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetExpoLiveEnable(ctx, req.(*SetExpoLiveEnableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListExpoLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExpoLiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListExpoLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListExpoLive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListExpoLive(ctx, req.(*ListExpoLiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteExpoLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExpoLiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteExpoLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteExpoLive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteExpoLive(ctx, req.(*DeleteExpoLiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_FacePhotoUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FacePhotoUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).FacePhotoUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_FacePhotoUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).FacePhotoUpload(ctx, req.(*FacePhotoUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_FaceGroupCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceGroupCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).FaceGroupCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_FaceGroupCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).FaceGroupCreate(ctx, req.(*FaceGroupCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetFaceGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceGroupInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetFaceGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetFaceGroupInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetFaceGroupInfo(ctx, req.(*FaceGroupInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_FaceGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).FaceGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_FaceGroupList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).FaceGroupList(ctx, req.(*FaceGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_FaceGroupsByExpo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceGroupsByExpoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).FaceGroupsByExpo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_FaceGroupsByExpo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).FaceGroupsByExpo(ctx, req.(*FaceGroupsByExpoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_FacesPhotoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PhotoFacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).FacesPhotoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_FacesPhotoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).FacesPhotoList(ctx, req.(*PhotoFacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Background_ServiceDesc is the grpc.ServiceDesc for Background service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Background_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.expo.v1.Background",
	HandlerType: (*BackgroundServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Background_Healthy_Handler,
		},
		{
			MethodName: "ListLiveImage",
			Handler:    _Background_ListLiveImage_Handler,
		},
		{
			MethodName: "AddLiveImage",
			Handler:    _Background_AddLiveImage_Handler,
		},
		{
			MethodName: "DeleteLiveImage",
			Handler:    _Background_DeleteLiveImage_Handler,
		},
		{
			MethodName: "SyncLiveImages",
			Handler:    _Background_SyncLiveImages_Handler,
		},
		{
			MethodName: "GetSyncStatus",
			Handler:    _Background_GetSyncStatus_Handler,
		},
		{
			MethodName: "AddGuest",
			Handler:    _Background_AddGuest_Handler,
		},
		{
			MethodName: "GetGuest",
			Handler:    _Background_GetGuest_Handler,
		},
		{
			MethodName: "UpdateGuest",
			Handler:    _Background_UpdateGuest_Handler,
		},
		{
			MethodName: "SetGuestEnable",
			Handler:    _Background_SetGuestEnable_Handler,
		},
		{
			MethodName: "ListGuest",
			Handler:    _Background_ListGuest_Handler,
		},
		{
			MethodName: "DeleteGuest",
			Handler:    _Background_DeleteGuest_Handler,
		},
		{
			MethodName: "AddExpoCommunity",
			Handler:    _Background_AddExpoCommunity_Handler,
		},
		{
			MethodName: "GetExpoCommunity",
			Handler:    _Background_GetExpoCommunity_Handler,
		},
		{
			MethodName: "UpdateExpoCommunity",
			Handler:    _Background_UpdateExpoCommunity_Handler,
		},
		{
			MethodName: "SetExpoCommunityEnable",
			Handler:    _Background_SetExpoCommunityEnable_Handler,
		},
		{
			MethodName: "DeleteExpoCommunity",
			Handler:    _Background_DeleteExpoCommunity_Handler,
		},
		{
			MethodName: "AddHall",
			Handler:    _Background_AddHall_Handler,
		},
		{
			MethodName: "GetHall",
			Handler:    _Background_GetHall_Handler,
		},
		{
			MethodName: "UpdateHall",
			Handler:    _Background_UpdateHall_Handler,
		},
		{
			MethodName: "SetHallEnable",
			Handler:    _Background_SetHallEnable_Handler,
		},
		{
			MethodName: "ListHall",
			Handler:    _Background_ListHall_Handler,
		},
		{
			MethodName: "DeleteHall",
			Handler:    _Background_DeleteHall_Handler,
		},
		{
			MethodName: "AddExpoGuest",
			Handler:    _Background_AddExpoGuest_Handler,
		},
		{
			MethodName: "GetExpoGuest",
			Handler:    _Background_GetExpoGuest_Handler,
		},
		{
			MethodName: "ListExpoGuest",
			Handler:    _Background_ListExpoGuest_Handler,
		},
		{
			MethodName: "DeleteExpoGuest",
			Handler:    _Background_DeleteExpoGuest_Handler,
		},
		{
			MethodName: "AddExpoSchedule",
			Handler:    _Background_AddExpoSchedule_Handler,
		},
		{
			MethodName: "GetExpoSchedule",
			Handler:    _Background_GetExpoSchedule_Handler,
		},
		{
			MethodName: "UpdateExpoSchedule",
			Handler:    _Background_UpdateExpoSchedule_Handler,
		},
		{
			MethodName: "ListExpoSchedule",
			Handler:    _Background_ListExpoSchedule_Handler,
		},
		{
			MethodName: "DeleteExpoSchedule",
			Handler:    _Background_DeleteExpoSchedule_Handler,
		},
		{
			MethodName: "AddExpoExhibitor",
			Handler:    _Background_AddExpoExhibitor_Handler,
		},
		{
			MethodName: "GetExpoExhibitor",
			Handler:    _Background_GetExpoExhibitor_Handler,
		},
		{
			MethodName: "UpdateExpoExhibitor",
			Handler:    _Background_UpdateExpoExhibitor_Handler,
		},
		{
			MethodName: "SetExpoExhibitorEnable",
			Handler:    _Background_SetExpoExhibitorEnable_Handler,
		},
		{
			MethodName: "ListExpoExhibitor",
			Handler:    _Background_ListExpoExhibitor_Handler,
		},
		{
			MethodName: "DeleteExpoExhibitor",
			Handler:    _Background_DeleteExpoExhibitor_Handler,
		},
		{
			MethodName: "AddExpoGuide",
			Handler:    _Background_AddExpoGuide_Handler,
		},
		{
			MethodName: "GetExpoGuide",
			Handler:    _Background_GetExpoGuide_Handler,
		},
		{
			MethodName: "UpdateExpoGuide",
			Handler:    _Background_UpdateExpoGuide_Handler,
		},
		{
			MethodName: "SetExpoGuideEnable",
			Handler:    _Background_SetExpoGuideEnable_Handler,
		},
		{
			MethodName: "ListExpoGuide",
			Handler:    _Background_ListExpoGuide_Handler,
		},
		{
			MethodName: "DeleteExpoGuide",
			Handler:    _Background_DeleteExpoGuide_Handler,
		},
		{
			MethodName: "GetExpoPartnerType",
			Handler:    _Background_GetExpoPartnerType_Handler,
		},
		{
			MethodName: "AddExpoPartner",
			Handler:    _Background_AddExpoPartner_Handler,
		},
		{
			MethodName: "GetExpoPartner",
			Handler:    _Background_GetExpoPartner_Handler,
		},
		{
			MethodName: "UpdateExpoPartner",
			Handler:    _Background_UpdateExpoPartner_Handler,
		},
		{
			MethodName: "SetExpoPartnerEnable",
			Handler:    _Background_SetExpoPartnerEnable_Handler,
		},
		{
			MethodName: "ListExpoPartner",
			Handler:    _Background_ListExpoPartner_Handler,
		},
		{
			MethodName: "DeleteExpoPartner",
			Handler:    _Background_DeleteExpoPartner_Handler,
		},
		{
			MethodName: "AddExpoReview",
			Handler:    _Background_AddExpoReview_Handler,
		},
		{
			MethodName: "GetExpoReview",
			Handler:    _Background_GetExpoReview_Handler,
		},
		{
			MethodName: "UpdateExpoReview",
			Handler:    _Background_UpdateExpoReview_Handler,
		},
		{
			MethodName: "SetExpoReviewEnable",
			Handler:    _Background_SetExpoReviewEnable_Handler,
		},
		{
			MethodName: "ListExpoReview",
			Handler:    _Background_ListExpoReview_Handler,
		},
		{
			MethodName: "DeleteExpoReview",
			Handler:    _Background_DeleteExpoReview_Handler,
		},
		{
			MethodName: "AddExpoLive",
			Handler:    _Background_AddExpoLive_Handler,
		},
		{
			MethodName: "GetExpoLive",
			Handler:    _Background_GetExpoLive_Handler,
		},
		{
			MethodName: "UpdateExpoLive",
			Handler:    _Background_UpdateExpoLive_Handler,
		},
		{
			MethodName: "SetExpoLiveEnable",
			Handler:    _Background_SetExpoLiveEnable_Handler,
		},
		{
			MethodName: "ListExpoLive",
			Handler:    _Background_ListExpoLive_Handler,
		},
		{
			MethodName: "DeleteExpoLive",
			Handler:    _Background_DeleteExpoLive_Handler,
		},
		{
			MethodName: "FacePhotoUpload",
			Handler:    _Background_FacePhotoUpload_Handler,
		},
		{
			MethodName: "FaceGroupCreate",
			Handler:    _Background_FaceGroupCreate_Handler,
		},
		{
			MethodName: "GetFaceGroupInfo",
			Handler:    _Background_GetFaceGroupInfo_Handler,
		},
		{
			MethodName: "FaceGroupList",
			Handler:    _Background_FaceGroupList_Handler,
		},
		{
			MethodName: "FaceGroupsByExpo",
			Handler:    _Background_FaceGroupsByExpo_Handler,
		},
		{
			MethodName: "FacesPhotoList",
			Handler:    _Background_FacesPhotoList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "expo/v1/background.proto",
}
