// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: expo/v1/service.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	common "wiki_user_center/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExpoRightType int32

const (
	ExpoRightType_ExpoRightType_Interaction ExpoRightType = 0 // 展会互动
	ExpoRightType_ExpoRightType_Chat        ExpoRightType = 1 // 自由聊天
)

// Enum value maps for ExpoRightType.
var (
	ExpoRightType_name = map[int32]string{
		0: "ExpoRightType_Interaction",
		1: "ExpoRightType_Chat",
	}
	ExpoRightType_value = map[string]int32{
		"ExpoRightType_Interaction": 0,
		"ExpoRightType_Chat":        1,
	}
)

func (x ExpoRightType) Enum() *ExpoRightType {
	p := new(ExpoRightType)
	*p = x
	return p
}

func (x ExpoRightType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpoRightType) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_service_proto_enumTypes[0].Descriptor()
}

func (ExpoRightType) Type() protoreflect.EnumType {
	return &file_expo_v1_service_proto_enumTypes[0]
}

func (x ExpoRightType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpoRightType.Descriptor instead.
func (ExpoRightType) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{0}
}

type ExpoStatus int32

const (
	ExpoStatus_ExpoStatus_UNKNOWN    ExpoStatus = 0 // 未知状态
	ExpoStatus_ExpoStatus_NOT_START  ExpoStatus = 1 // 未开始
	ExpoStatus_ExpoStatus_PROCESSING ExpoStatus = 2 // 进行中
	ExpoStatus_ExpoStatus_END        ExpoStatus = 3 // 已结束
)

// Enum value maps for ExpoStatus.
var (
	ExpoStatus_name = map[int32]string{
		0: "ExpoStatus_UNKNOWN",
		1: "ExpoStatus_NOT_START",
		2: "ExpoStatus_PROCESSING",
		3: "ExpoStatus_END",
	}
	ExpoStatus_value = map[string]int32{
		"ExpoStatus_UNKNOWN":    0,
		"ExpoStatus_NOT_START":  1,
		"ExpoStatus_PROCESSING": 2,
		"ExpoStatus_END":        3,
	}
)

func (x ExpoStatus) Enum() *ExpoStatus {
	p := new(ExpoStatus)
	*p = x
	return p
}

func (x ExpoStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpoStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_service_proto_enumTypes[1].Descriptor()
}

func (ExpoStatus) Type() protoreflect.EnumType {
	return &file_expo_v1_service_proto_enumTypes[1]
}

func (x ExpoStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpoStatus.Descriptor instead.
func (ExpoStatus) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{1}
}

type ExpoAudienceGroupCategory int32

const (
	ExpoAudienceGroupCategory_Speakers      ExpoAudienceGroupCategory = 0 // 演讲嘉宾
	ExpoAudienceGroupCategory_Professionals ExpoAudienceGroupCategory = 1 // 从业者
	ExpoAudienceGroupCategory_Investors     ExpoAudienceGroupCategory = 2 // 投资者
	ExpoAudienceGroupCategory_NotGrouped    ExpoAudienceGroupCategory = 3 // 未分组
	ExpoAudienceGroupCategory_Exhibitors    ExpoAudienceGroupCategory = 4 // 赞助商
)

// Enum value maps for ExpoAudienceGroupCategory.
var (
	ExpoAudienceGroupCategory_name = map[int32]string{
		0: "Speakers",
		1: "Professionals",
		2: "Investors",
		3: "NotGrouped",
		4: "Exhibitors",
	}
	ExpoAudienceGroupCategory_value = map[string]int32{
		"Speakers":      0,
		"Professionals": 1,
		"Investors":     2,
		"NotGrouped":    3,
		"Exhibitors":    4,
	}
)

func (x ExpoAudienceGroupCategory) Enum() *ExpoAudienceGroupCategory {
	p := new(ExpoAudienceGroupCategory)
	*p = x
	return p
}

func (x ExpoAudienceGroupCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpoAudienceGroupCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_service_proto_enumTypes[2].Descriptor()
}

func (ExpoAudienceGroupCategory) Type() protoreflect.EnumType {
	return &file_expo_v1_service_proto_enumTypes[2]
}

func (x ExpoAudienceGroupCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpoAudienceGroupCategory.Descriptor instead.
func (ExpoAudienceGroupCategory) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{2}
}

type TicketStatus int32

const (
	TicketStatus_TICKET_STATUS_REVIEW  TicketStatus = 0 // 审核中
	TicketStatus_TICKET_STATUS_PASS    TicketStatus = 1 // 待使用
	TicketStatus_TICKET_STATUS_USED    TicketStatus = 2 // 已核销
	TicketStatus_TICKET_STATUS_EXPIRED TicketStatus = 3 // 已过期
	TicketStatus_TICKET_STATUS_REJECT  TicketStatus = 4 // 审核未通过
)

// Enum value maps for TicketStatus.
var (
	TicketStatus_name = map[int32]string{
		0: "TICKET_STATUS_REVIEW",
		1: "TICKET_STATUS_PASS",
		2: "TICKET_STATUS_USED",
		3: "TICKET_STATUS_EXPIRED",
		4: "TICKET_STATUS_REJECT",
	}
	TicketStatus_value = map[string]int32{
		"TICKET_STATUS_REVIEW":  0,
		"TICKET_STATUS_PASS":    1,
		"TICKET_STATUS_USED":    2,
		"TICKET_STATUS_EXPIRED": 3,
		"TICKET_STATUS_REJECT":  4,
	}
)

func (x TicketStatus) Enum() *TicketStatus {
	p := new(TicketStatus)
	*p = x
	return p
}

func (x TicketStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_service_proto_enumTypes[3].Descriptor()
}

func (TicketStatus) Type() protoreflect.EnumType {
	return &file_expo_v1_service_proto_enumTypes[3]
}

func (x TicketStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketStatus.Descriptor instead.
func (TicketStatus) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{3}
}

type OpenExpoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId       int64        `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Status       ExpoStatus   `protobuf:"varint,2,opt,name=status,json=status,proto3,enum=api.expo.v1.ExpoStatus" json:"status"`
	Logo         string       `protobuf:"bytes,3,opt,name=logo,json=logo,proto3" json:"logo"`
	Name         string       `protobuf:"bytes,4,opt,name=name,json=name,proto3" json:"name"`
	CountryCode  string       `protobuf:"bytes,5,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	StartTime    int64        `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	TicketCode   string       `protobuf:"bytes,7,opt,name=ticket_code,json=ticketCode,proto3" json:"ticket_code"`
	TicketStatus TicketStatus `protobuf:"varint,8,opt,name=ticket_status,json=ticketStatus,proto3,enum=api.expo.v1.TicketStatus" json:"ticket_status"`
	PaymentTotal string       `protobuf:"bytes,9,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	CreatedAt    int64        `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
}

func (x *OpenExpoReply) Reset() {
	*x = OpenExpoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenExpoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenExpoReply) ProtoMessage() {}

func (x *OpenExpoReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenExpoReply.ProtoReflect.Descriptor instead.
func (*OpenExpoReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *OpenExpoReply) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *OpenExpoReply) GetStatus() ExpoStatus {
	if x != nil {
		return x.Status
	}
	return ExpoStatus_ExpoStatus_UNKNOWN
}

func (x *OpenExpoReply) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *OpenExpoReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OpenExpoReply) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *OpenExpoReply) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *OpenExpoReply) GetTicketCode() string {
	if x != nil {
		return x.TicketCode
	}
	return ""
}

func (x *OpenExpoReply) GetTicketStatus() TicketStatus {
	if x != nil {
		return x.TicketStatus
	}
	return TicketStatus_TICKET_STATUS_REVIEW
}

func (x *OpenExpoReply) GetPaymentTotal() string {
	if x != nil {
		return x.PaymentTotal
	}
	return ""
}

func (x *OpenExpoReply) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type UserExpoRightRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *UserExpoRightRequest) Reset() {
	*x = UserExpoRightRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExpoRightRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExpoRightRequest) ProtoMessage() {}

func (x *UserExpoRightRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExpoRightRequest.ProtoReflect.Descriptor instead.
func (*UserExpoRightRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *UserExpoRightRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoRight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        ExpoRightType `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.expo.v1.ExpoRightType" json:"type"`
	Title       string        `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Lock        string        `protobuf:"bytes,3,opt,name=lock,json=lock,proto3" json:"lock"`
	Description []string      `protobuf:"bytes,4,rep,name=description,json=description,proto3" json:"description"`
	Activate    bool          `protobuf:"varint,5,opt,name=activate,json=activate,proto3" json:"activate"`
	Icon        string        `protobuf:"bytes,6,opt,name=icon,json=icon,proto3" json:"icon"`
	JumpTitle   string        `protobuf:"bytes,7,opt,name=jump_title,json=jumpTitle,proto3" json:"jump_title"`
}

func (x *ExpoRight) Reset() {
	*x = ExpoRight{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoRight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoRight) ProtoMessage() {}

func (x *ExpoRight) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoRight.ProtoReflect.Descriptor instead.
func (*ExpoRight) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *ExpoRight) GetType() ExpoRightType {
	if x != nil {
		return x.Type
	}
	return ExpoRightType_ExpoRightType_Interaction
}

func (x *ExpoRight) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExpoRight) GetLock() string {
	if x != nil {
		return x.Lock
	}
	return ""
}

func (x *ExpoRight) GetDescription() []string {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *ExpoRight) GetActivate() bool {
	if x != nil {
		return x.Activate
	}
	return false
}

func (x *ExpoRight) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ExpoRight) GetJumpTitle() string {
	if x != nil {
		return x.JumpTitle
	}
	return ""
}

type UserExpoRightReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rights []*ExpoRight `protobuf:"bytes,2,rep,name=rights,json=rights,proto3" json:"rights"`
}

func (x *UserExpoRightReply) Reset() {
	*x = UserExpoRightReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExpoRightReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExpoRightReply) ProtoMessage() {}

func (x *UserExpoRightReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExpoRightReply.ProtoReflect.Descriptor instead.
func (*UserExpoRightReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *UserExpoRightReply) GetRights() []*ExpoRight {
	if x != nil {
		return x.Rights
	}
	return nil
}

type ExpoTab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string     `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name    string     `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	SubTabs []*ExpoTab `protobuf:"bytes,3,rep,name=sub_tabs,json=subTabs,proto3" json:"sub_tabs"`
}

func (x *ExpoTab) Reset() {
	*x = ExpoTab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoTab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoTab) ProtoMessage() {}

func (x *ExpoTab) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoTab.ProtoReflect.Descriptor instead.
func (*ExpoTab) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *ExpoTab) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExpoTab) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoTab) GetSubTabs() []*ExpoTab {
	if x != nil {
		return x.SubTabs
	}
	return nil
}

type ExpoTabReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tabs []*ExpoTab `protobuf:"bytes,1,rep,name=tabs,json=tabs,proto3" json:"tabs"`
}

func (x *ExpoTabReply) Reset() {
	*x = ExpoTabReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoTabReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoTabReply) ProtoMessage() {}

func (x *ExpoTabReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoTabReply.ProtoReflect.Descriptor instead.
func (*ExpoTabReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *ExpoTabReply) GetTabs() []*ExpoTab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

type ExpoListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TabId    string `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id"`
	SubTabId string `protobuf:"bytes,2,opt,name=sub_tab_id,json=subTabId,proto3" json:"sub_tab_id"`
	Size     int32  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page     int32  `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
}

func (x *ExpoListRequest) Reset() {
	*x = ExpoListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoListRequest) ProtoMessage() {}

func (x *ExpoListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoListRequest.ProtoReflect.Descriptor instead.
func (*ExpoListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *ExpoListRequest) GetTabId() string {
	if x != nil {
		return x.TabId
	}
	return ""
}

func (x *ExpoListRequest) GetSubTabId() string {
	if x != nil {
		return x.SubTabId
	}
	return ""
}

func (x *ExpoListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ExpoListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type ExpoBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64      `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Logo        string     `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
	Status      ExpoStatus `protobuf:"varint,3,opt,name=status,json=status,proto3,enum=api.expo.v1.ExpoStatus" json:"status"`
	Name        string     `protobuf:"bytes,4,opt,name=name,json=name,proto3" json:"name"`
	Address     string     `protobuf:"bytes,5,opt,name=address,json=address,proto3" json:"address"`
	Audience    string     `protobuf:"bytes,6,opt,name=audience,json=audience,proto3" json:"audience"`
	CountryCode string     `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	StartTime   int64      `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	Zone        string     `protobuf:"bytes,9,opt,name=zone,json=zone,proto3" json:"zone"`
}

func (x *ExpoBase) Reset() {
	*x = ExpoBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoBase) ProtoMessage() {}

func (x *ExpoBase) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoBase.ProtoReflect.Descriptor instead.
func (*ExpoBase) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *ExpoBase) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoBase) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExpoBase) GetStatus() ExpoStatus {
	if x != nil {
		return x.Status
	}
	return ExpoStatus_ExpoStatus_UNKNOWN
}

func (x *ExpoBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoBase) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ExpoBase) GetAudience() string {
	if x != nil {
		return x.Audience
	}
	return ""
}

func (x *ExpoBase) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *ExpoBase) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ExpoBase) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

type ExpoListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expos []*ExpoBase `protobuf:"bytes,1,rep,name=expos,json=expos,proto3" json:"expos"`
}

func (x *ExpoListReply) Reset() {
	*x = ExpoListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoListReply) ProtoMessage() {}

func (x *ExpoListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoListReply.ProtoReflect.Descriptor instead.
func (*ExpoListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *ExpoListReply) GetExpos() []*ExpoBase {
	if x != nil {
		return x.Expos
	}
	return nil
}

type ExpoExhibitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Logo             string `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
	SponsorLevelIcon string `protobuf:"bytes,4,opt,name=sponsor_level_icon,json=sponsorLevelIcon,proto3" json:"sponsor_level_icon"`
}

func (x *ExpoExhibitor) Reset() {
	*x = ExpoExhibitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoExhibitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoExhibitor) ProtoMessage() {}

func (x *ExpoExhibitor) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoExhibitor.ProtoReflect.Descriptor instead.
func (*ExpoExhibitor) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *ExpoExhibitor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoExhibitor) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExpoExhibitor) GetSponsorLevelIcon() string {
	if x != nil {
		return x.SponsorLevelIcon
	}
	return ""
}

type ExpoAudienceGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category   ExpoAudienceGroupCategory `protobuf:"varint,1,opt,name=category,json=category,proto3,enum=api.expo.v1.ExpoAudienceGroupCategory" json:"category"`
	Name       string                    `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Count      string                    `protobuf:"bytes,3,opt,name=count,json=count,proto3" json:"count"`
	Avatars    []string                  `protobuf:"bytes,4,rep,name=avatars,json=avatars,proto3" json:"avatars"`
	Exhibitors []*ExpoExhibitor          `protobuf:"bytes,5,rep,name=exhibitors,json=exhibitors,proto3" json:"exhibitors"`
}

func (x *ExpoAudienceGroup) Reset() {
	*x = ExpoAudienceGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoAudienceGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoAudienceGroup) ProtoMessage() {}

func (x *ExpoAudienceGroup) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoAudienceGroup.ProtoReflect.Descriptor instead.
func (*ExpoAudienceGroup) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *ExpoAudienceGroup) GetCategory() ExpoAudienceGroupCategory {
	if x != nil {
		return x.Category
	}
	return ExpoAudienceGroupCategory_Speakers
}

func (x *ExpoAudienceGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoAudienceGroup) GetCount() string {
	if x != nil {
		return x.Count
	}
	return ""
}

func (x *ExpoAudienceGroup) GetAvatars() []string {
	if x != nil {
		return x.Avatars
	}
	return nil
}

func (x *ExpoAudienceGroup) GetExhibitors() []*ExpoExhibitor {
	if x != nil {
		return x.Exhibitors
	}
	return nil
}

type LiveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LiveUrl string `protobuf:"bytes,1,opt,name=live_url,json=liveUrl,proto3" json:"live_url"`
	Name    string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
}

func (x *LiveInfo) Reset() {
	*x = LiveInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveInfo) ProtoMessage() {}

func (x *LiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveInfo.ProtoReflect.Descriptor instead.
func (*LiveInfo) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *LiveInfo) GetLiveUrl() string {
	if x != nil {
		return x.LiveUrl
	}
	return ""
}

func (x *LiveInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ExpoDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64                `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Logo           string               `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
	Status         ExpoStatus           `protobuf:"varint,3,opt,name=status,json=status,proto3,enum=api.expo.v1.ExpoStatus" json:"status"`
	Name           string               `protobuf:"bytes,4,opt,name=name,json=name,proto3" json:"name"`
	Address        string               `protobuf:"bytes,5,opt,name=address,json=address,proto3" json:"address"`
	Description    string               `protobuf:"bytes,6,opt,name=description,json=description,proto3" json:"description"`
	CountryCode    string               `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	StartTime      int64                `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	Longitude      string               `protobuf:"bytes,9,opt,name=longitude,json=longitude,proto3" json:"longitude"`
	Latitude       string               `protobuf:"bytes,10,opt,name=latitude,json=latitude,proto3" json:"latitude"`
	BaiduImage     string               `protobuf:"bytes,11,opt,name=baidu_image,json=baiduImage,proto3" json:"baidu_image"`
	GoogleImage    string               `protobuf:"bytes,12,opt,name=google_image,json=googleImage,proto3" json:"google_image"`
	Registration   bool                 `protobuf:"varint,13,opt,name=registration,json=registration,proto3" json:"registration"`
	MainLive       string               `protobuf:"bytes,14,opt,name=main_live,json=mainLive,proto3" json:"main_live"`
	Lives          []*LiveInfo          `protobuf:"bytes,15,rep,name=lives,json=lives,proto3" json:"lives"`
	Images         []string             `protobuf:"bytes,16,rep,name=images,json=images,proto3" json:"images"`
	ImageCount     int64                `protobuf:"varint,17,opt,name=image_count,json=imageCount,proto3" json:"image_count"`
	AudienceGroups []*ExpoAudienceGroup `protobuf:"bytes,18,rep,name=audience_groups,json=audienceGroups,proto3" json:"audience_groups"`
}

func (x *ExpoDetail) Reset() {
	*x = ExpoDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoDetail) ProtoMessage() {}

func (x *ExpoDetail) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoDetail.ProtoReflect.Descriptor instead.
func (*ExpoDetail) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *ExpoDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExpoDetail) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExpoDetail) GetStatus() ExpoStatus {
	if x != nil {
		return x.Status
	}
	return ExpoStatus_ExpoStatus_UNKNOWN
}

func (x *ExpoDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoDetail) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ExpoDetail) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExpoDetail) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *ExpoDetail) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ExpoDetail) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *ExpoDetail) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *ExpoDetail) GetBaiduImage() string {
	if x != nil {
		return x.BaiduImage
	}
	return ""
}

func (x *ExpoDetail) GetGoogleImage() string {
	if x != nil {
		return x.GoogleImage
	}
	return ""
}

func (x *ExpoDetail) GetRegistration() bool {
	if x != nil {
		return x.Registration
	}
	return false
}

func (x *ExpoDetail) GetMainLive() string {
	if x != nil {
		return x.MainLive
	}
	return ""
}

func (x *ExpoDetail) GetLives() []*LiveInfo {
	if x != nil {
		return x.Lives
	}
	return nil
}

func (x *ExpoDetail) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ExpoDetail) GetImageCount() int64 {
	if x != nil {
		return x.ImageCount
	}
	return 0
}

func (x *ExpoDetail) GetAudienceGroups() []*ExpoAudienceGroup {
	if x != nil {
		return x.AudienceGroups
	}
	return nil
}

type ExpoDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *ExpoDetailRequest) Reset() {
	*x = ExpoDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoDetailRequest) ProtoMessage() {}

func (x *ExpoDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoDetailRequest.ProtoReflect.Descriptor instead.
func (*ExpoDetailRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{13}
}

func (x *ExpoDetailRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoUserSignUpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username      string   `protobuf:"bytes,1,opt,name=username,json=username,proto3" json:"username"`
	PhoneAreaCode string   `protobuf:"bytes,2,opt,name=phone_area_code,json=phoneAreaCode,proto3" json:"phone_area_code"`
	Phone         string   `protobuf:"bytes,3,opt,name=phone,json=phone,proto3" json:"phone"`
	Email         string   `protobuf:"bytes,4,opt,name=email,json=email,proto3" json:"email"`
	Company       string   `protobuf:"bytes,5,opt,name=company,json=company,proto3" json:"company"`
	Job           string   `protobuf:"bytes,6,opt,name=job,json=job,proto3" json:"job"`
	Industry      Industry `protobuf:"varint,7,opt,name=industry,json=industry,proto3,enum=api.expo.v1.Industry" json:"industry"`
	Identity      Identity `protobuf:"varint,8,opt,name=identity,json=identity,proto3,enum=api.expo.v1.Identity" json:"identity"`
}

func (x *ExpoUserSignUpRequest) Reset() {
	*x = ExpoUserSignUpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoUserSignUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoUserSignUpRequest) ProtoMessage() {}

func (x *ExpoUserSignUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoUserSignUpRequest.ProtoReflect.Descriptor instead.
func (*ExpoUserSignUpRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *ExpoUserSignUpRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ExpoUserSignUpRequest) GetPhoneAreaCode() string {
	if x != nil {
		return x.PhoneAreaCode
	}
	return ""
}

func (x *ExpoUserSignUpRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ExpoUserSignUpRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ExpoUserSignUpRequest) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *ExpoUserSignUpRequest) GetJob() string {
	if x != nil {
		return x.Job
	}
	return ""
}

func (x *ExpoUserSignUpRequest) GetIndustry() Industry {
	if x != nil {
		return x.Industry
	}
	return Industry_INDUSTRY_UNKNOWN
}

func (x *ExpoUserSignUpRequest) GetIdentity() Identity {
	if x != nil {
		return x.Identity
	}
	return Identity_IDENTITY_UNKNOWN
}

type ExpoUserSignUpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId       int64        `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Name         string       `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Address      string       `protobuf:"bytes,3,opt,name=address,json=address,proto3" json:"address"`
	Logo         string       `protobuf:"bytes,4,opt,name=logo,json=logo,proto3" json:"logo"`
	PaymentTotal string       `protobuf:"bytes,5,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	TicketCode   string       `protobuf:"bytes,6,opt,name=ticket_code,json=ticketCode,proto3" json:"ticket_code"`
	Status       TicketStatus `protobuf:"varint,7,opt,name=status,json=status,proto3,enum=api.expo.v1.TicketStatus" json:"status"`
}

func (x *ExpoUserSignUpReply) Reset() {
	*x = ExpoUserSignUpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoUserSignUpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoUserSignUpReply) ProtoMessage() {}

func (x *ExpoUserSignUpReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoUserSignUpReply.ProtoReflect.Descriptor instead.
func (*ExpoUserSignUpReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *ExpoUserSignUpReply) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoUserSignUpReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoUserSignUpReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ExpoUserSignUpReply) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExpoUserSignUpReply) GetPaymentTotal() string {
	if x != nil {
		return x.PaymentTotal
	}
	return ""
}

func (x *ExpoUserSignUpReply) GetTicketCode() string {
	if x != nil {
		return x.TicketCode
	}
	return ""
}

func (x *ExpoUserSignUpReply) GetStatus() TicketStatus {
	if x != nil {
		return x.Status
	}
	return TicketStatus_TICKET_STATUS_REVIEW
}

type ExhibitorSignUpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId        int64  `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	ExhibitSize   string `protobuf:"bytes,2,opt,name=exhibit_size,json=exhibitSize,proto3" json:"exhibit_size"`
	Company       string `protobuf:"bytes,3,opt,name=company,json=company,proto3" json:"company"`
	Website       string `protobuf:"bytes,4,opt,name=website,json=website,proto3" json:"website"`
	Contact       string `protobuf:"bytes,5,opt,name=contact,json=contact,proto3" json:"contact"`
	PhoneAreaCode string `protobuf:"bytes,6,opt,name=phone_area_code,json=phoneAreaCode,proto3" json:"phone_area_code"`
	Phone         string `protobuf:"bytes,7,opt,name=phone,json=phone,proto3" json:"phone"`
	Email         string `protobuf:"bytes,8,opt,name=email,json=email,proto3" json:"email"`
}

func (x *ExhibitorSignUpRequest) Reset() {
	*x = ExhibitorSignUpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitorSignUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitorSignUpRequest) ProtoMessage() {}

func (x *ExhibitorSignUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitorSignUpRequest.ProtoReflect.Descriptor instead.
func (*ExhibitorSignUpRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *ExhibitorSignUpRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExhibitorSignUpRequest) GetExhibitSize() string {
	if x != nil {
		return x.ExhibitSize
	}
	return ""
}

func (x *ExhibitorSignUpRequest) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *ExhibitorSignUpRequest) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *ExhibitorSignUpRequest) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

func (x *ExhibitorSignUpRequest) GetPhoneAreaCode() string {
	if x != nil {
		return x.PhoneAreaCode
	}
	return ""
}

func (x *ExhibitorSignUpRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ExhibitorSignUpRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type ExhibitorSignUpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExhibitorSignUpReply) Reset() {
	*x = ExhibitorSignUpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitorSignUpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitorSignUpReply) ProtoMessage() {}

func (x *ExhibitorSignUpReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitorSignUpReply.ProtoReflect.Descriptor instead.
func (*ExhibitorSignUpReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{17}
}

type ExpoInteractionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ExpoInteractionRequest) Reset() {
	*x = ExpoInteractionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoInteractionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoInteractionRequest) ProtoMessage() {}

func (x *ExpoInteractionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoInteractionRequest.ProtoReflect.Descriptor instead.
func (*ExpoInteractionRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *ExpoInteractionRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoInteractionRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ExpoInteractionRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ExpoInteraction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string             `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Avatar        string             `protobuf:"bytes,2,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Nickname      string             `protobuf:"bytes,3,opt,name=nickname,json=nickname,proto3" json:"nickname"`
	OriginContent string             `protobuf:"bytes,4,opt,name=origin_content,json=originContent,proto3" json:"origin_content"`
	Content       string             `protobuf:"bytes,5,opt,name=content,json=content,proto3" json:"content"`
	Time          string             `protobuf:"bytes,6,opt,name=time,json=time,proto3" json:"time"`
	Country       string             `protobuf:"bytes,7,opt,name=country,json=country,proto3" json:"country"`
	Like          string             `protobuf:"bytes,8,opt,name=like,json=like,proto3" json:"like"`
	Comments      []*ExpoInteraction `protobuf:"bytes,9,rep,name=comments,json=comments,proto3" json:"comments"`
}

func (x *ExpoInteraction) Reset() {
	*x = ExpoInteraction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoInteraction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoInteraction) ProtoMessage() {}

func (x *ExpoInteraction) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoInteraction.ProtoReflect.Descriptor instead.
func (*ExpoInteraction) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *ExpoInteraction) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExpoInteraction) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ExpoInteraction) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ExpoInteraction) GetOriginContent() string {
	if x != nil {
		return x.OriginContent
	}
	return ""
}

func (x *ExpoInteraction) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ExpoInteraction) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *ExpoInteraction) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ExpoInteraction) GetLike() string {
	if x != nil {
		return x.Like
	}
	return ""
}

func (x *ExpoInteraction) GetComments() []*ExpoInteraction {
	if x != nil {
		return x.Comments
	}
	return nil
}

type ExpoInteractionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total    string             `protobuf:"bytes,1,opt,name=total,json=total,proto3" json:"total"`
	Comments []*ExpoInteraction `protobuf:"bytes,2,rep,name=comments,json=comments,proto3" json:"comments"`
}

func (x *ExpoInteractionReply) Reset() {
	*x = ExpoInteractionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoInteractionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoInteractionReply) ProtoMessage() {}

func (x *ExpoInteractionReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoInteractionReply.ProtoReflect.Descriptor instead.
func (*ExpoInteractionReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{20}
}

func (x *ExpoInteractionReply) GetTotal() string {
	if x != nil {
		return x.Total
	}
	return ""
}

func (x *ExpoInteractionReply) GetComments() []*ExpoInteraction {
	if x != nil {
		return x.Comments
	}
	return nil
}

type ExpoInteractionPreviewReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Previews []string `protobuf:"bytes,1,rep,name=previews,json=previews,proto3" json:"previews"`
}

func (x *ExpoInteractionPreviewReply) Reset() {
	*x = ExpoInteractionPreviewReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoInteractionPreviewReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoInteractionPreviewReply) ProtoMessage() {}

func (x *ExpoInteractionPreviewReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoInteractionPreviewReply.ProtoReflect.Descriptor instead.
func (*ExpoInteractionPreviewReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *ExpoInteractionPreviewReply) GetPreviews() []string {
	if x != nil {
		return x.Previews
	}
	return nil
}

type PostExpoInteractionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"`
}

func (x *PostExpoInteractionRequest) Reset() {
	*x = PostExpoInteractionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostExpoInteractionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostExpoInteractionRequest) ProtoMessage() {}

func (x *PostExpoInteractionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostExpoInteractionRequest.ProtoReflect.Descriptor instead.
func (*PostExpoInteractionRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{22}
}

func (x *PostExpoInteractionRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type PostExpoInteractionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *PostExpoInteractionReply) Reset() {
	*x = PostExpoInteractionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostExpoInteractionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostExpoInteractionReply) ProtoMessage() {}

func (x *PostExpoInteractionReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostExpoInteractionReply.ProtoReflect.Descriptor instead.
func (*PostExpoInteractionReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{23}
}

func (x *PostExpoInteractionReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ExpoScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *ExpoScheduleRequest) Reset() {
	*x = ExpoScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleRequest) ProtoMessage() {}

func (x *ExpoScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleRequest.ProtoReflect.Descriptor instead.
func (*ExpoScheduleRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{24}
}

func (x *ExpoScheduleRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoScheduleHallLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp          int64  `protobuf:"varint,1,opt,name=timestamp,json=timestamp,proto3" json:"timestamp"`
	TimestampShow      string `protobuf:"bytes,2,opt,name=timestamp_show,json=timestampShow,proto3" json:"timestamp_show"`
	Theme              string `protobuf:"bytes,3,opt,name=theme,json=theme,proto3" json:"theme"`
	Description        string `protobuf:"bytes,4,opt,name=description,json=description,proto3" json:"description"`
	Speaker            string `protobuf:"bytes,5,opt,name=speaker,json=speaker,proto3" json:"speaker"`
	SpeakerAvatar      string `protobuf:"bytes,6,opt,name=speaker_avatar,json=speakerAvatar,proto3" json:"speaker_avatar"`
	SpeakerDescription string `protobuf:"bytes,7,opt,name=speaker_description,json=speakerDescription,proto3" json:"speaker_description"`
}

func (x *ExpoScheduleHallLine) Reset() {
	*x = ExpoScheduleHallLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleHallLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleHallLine) ProtoMessage() {}

func (x *ExpoScheduleHallLine) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleHallLine.ProtoReflect.Descriptor instead.
func (*ExpoScheduleHallLine) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{25}
}

func (x *ExpoScheduleHallLine) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ExpoScheduleHallLine) GetTimestampShow() string {
	if x != nil {
		return x.TimestampShow
	}
	return ""
}

func (x *ExpoScheduleHallLine) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

func (x *ExpoScheduleHallLine) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExpoScheduleHallLine) GetSpeaker() string {
	if x != nil {
		return x.Speaker
	}
	return ""
}

func (x *ExpoScheduleHallLine) GetSpeakerAvatar() string {
	if x != nil {
		return x.SpeakerAvatar
	}
	return ""
}

func (x *ExpoScheduleHallLine) GetSpeakerDescription() string {
	if x != nil {
		return x.SpeakerDescription
	}
	return ""
}

// 展会场次
type ExpoScheduleHall struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string                  `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Lines []*ExpoScheduleHallLine `protobuf:"bytes,2,rep,name=lines,json=lines,proto3" json:"lines"`
}

func (x *ExpoScheduleHall) Reset() {
	*x = ExpoScheduleHall{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleHall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleHall) ProtoMessage() {}

func (x *ExpoScheduleHall) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleHall.ProtoReflect.Descriptor instead.
func (*ExpoScheduleHall) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{26}
}

func (x *ExpoScheduleHall) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoScheduleHall) GetLines() []*ExpoScheduleHallLine {
	if x != nil {
		return x.Lines
	}
	return nil
}

type ExpoSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date  string              `protobuf:"bytes,1,opt,name=date,json=date,proto3" json:"date"`
	Halls []*ExpoScheduleHall `protobuf:"bytes,2,rep,name=halls,json=halls,proto3" json:"halls"`
}

func (x *ExpoSchedule) Reset() {
	*x = ExpoSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoSchedule) ProtoMessage() {}

func (x *ExpoSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoSchedule.ProtoReflect.Descriptor instead.
func (*ExpoSchedule) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{27}
}

func (x *ExpoSchedule) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *ExpoSchedule) GetHalls() []*ExpoScheduleHall {
	if x != nil {
		return x.Halls
	}
	return nil
}

type ExpoScheduleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schedules []*ExpoSchedule `protobuf:"bytes,1,rep,name=schedules,json=schedules,proto3" json:"schedules"`
}

func (x *ExpoScheduleReply) Reset() {
	*x = ExpoScheduleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoScheduleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoScheduleReply) ProtoMessage() {}

func (x *ExpoScheduleReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoScheduleReply.ProtoReflect.Descriptor instead.
func (*ExpoScheduleReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{28}
}

func (x *ExpoScheduleReply) GetSchedules() []*ExpoSchedule {
	if x != nil {
		return x.Schedules
	}
	return nil
}

type ExpoGuideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *ExpoGuideRequest) Reset() {
	*x = ExpoGuideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoGuideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoGuideRequest) ProtoMessage() {}

func (x *ExpoGuideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoGuideRequest.ProtoReflect.Descriptor instead.
func (*ExpoGuideRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{29}
}

func (x *ExpoGuideRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoBooth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrokerCode       string `protobuf:"bytes,1,opt,name=broker_code,json=brokerCode,proto3" json:"broker_code"`
	Logo             string `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
	Name             string `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	SponsorLevelIcon string `protobuf:"bytes,4,opt,name=sponsor_level_icon,json=sponsorLevelIcon,proto3" json:"sponsor_level_icon"`
	Booth            string `protobuf:"bytes,5,opt,name=booth,json=booth,proto3" json:"booth"`
}

func (x *ExpoBooth) Reset() {
	*x = ExpoBooth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoBooth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoBooth) ProtoMessage() {}

func (x *ExpoBooth) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoBooth.ProtoReflect.Descriptor instead.
func (*ExpoBooth) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{30}
}

func (x *ExpoBooth) GetBrokerCode() string {
	if x != nil {
		return x.BrokerCode
	}
	return ""
}

func (x *ExpoBooth) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExpoBooth) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoBooth) GetSponsorLevelIcon() string {
	if x != nil {
		return x.SponsorLevelIcon
	}
	return ""
}

func (x *ExpoBooth) GetBooth() string {
	if x != nil {
		return x.Booth
	}
	return ""
}

type ExpoGuideReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuideMapUrl  string       `protobuf:"bytes,1,opt,name=guide_map_url,json=guideMapUrl,proto3" json:"guide_map_url"`
	Booth        []*ExpoBooth `protobuf:"bytes,2,rep,name=booth,json=booth,proto3" json:"booth"`
	Address      string       `protobuf:"bytes,3,opt,name=address,json=address,proto3" json:"address"`
	Location     string       `protobuf:"bytes,4,opt,name=location,json=location,proto3" json:"location"`
	Longitude    string       `protobuf:"bytes,5,opt,name=longitude,json=longitude,proto3" json:"longitude"`
	Latitude     string       `protobuf:"bytes,6,opt,name=latitude,json=latitude,proto3" json:"latitude"`
	BaiduMapUrl  string       `protobuf:"bytes,7,opt,name=baidu_map_url,json=baiduMapUrl,proto3" json:"baidu_map_url"`
	GoogleMapUrl string       `protobuf:"bytes,8,opt,name=google_map_url,json=googleMapUrl,proto3" json:"google_map_url"`
}

func (x *ExpoGuideReply) Reset() {
	*x = ExpoGuideReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoGuideReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoGuideReply) ProtoMessage() {}

func (x *ExpoGuideReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoGuideReply.ProtoReflect.Descriptor instead.
func (*ExpoGuideReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{31}
}

func (x *ExpoGuideReply) GetGuideMapUrl() string {
	if x != nil {
		return x.GuideMapUrl
	}
	return ""
}

func (x *ExpoGuideReply) GetBooth() []*ExpoBooth {
	if x != nil {
		return x.Booth
	}
	return nil
}

func (x *ExpoGuideReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ExpoGuideReply) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ExpoGuideReply) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *ExpoGuideReply) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *ExpoGuideReply) GetBaiduMapUrl() string {
	if x != nil {
		return x.BaiduMapUrl
	}
	return ""
}

func (x *ExpoGuideReply) GetGoogleMapUrl() string {
	if x != nil {
		return x.GoogleMapUrl
	}
	return ""
}

type ExpoPartnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *ExpoPartnerRequest) Reset() {
	*x = ExpoPartnerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoPartnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoPartnerRequest) ProtoMessage() {}

func (x *ExpoPartnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoPartnerRequest.ProtoReflect.Descriptor instead.
func (*ExpoPartnerRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{32}
}

func (x *ExpoPartnerRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoPartnerGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string   `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Logo []string `protobuf:"bytes,2,rep,name=logo,json=logo,proto3" json:"logo"`
}

func (x *ExpoPartnerGroup) Reset() {
	*x = ExpoPartnerGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoPartnerGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoPartnerGroup) ProtoMessage() {}

func (x *ExpoPartnerGroup) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoPartnerGroup.ProtoReflect.Descriptor instead.
func (*ExpoPartnerGroup) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{33}
}

func (x *ExpoPartnerGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExpoPartnerGroup) GetLogo() []string {
	if x != nil {
		return x.Logo
	}
	return nil
}

type ExpoPartnerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*ExpoPartnerGroup `protobuf:"bytes,1,rep,name=groups,json=groups,proto3" json:"groups"`
}

func (x *ExpoPartnerReply) Reset() {
	*x = ExpoPartnerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoPartnerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoPartnerReply) ProtoMessage() {}

func (x *ExpoPartnerReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoPartnerReply.ProtoReflect.Descriptor instead.
func (*ExpoPartnerReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{34}
}

func (x *ExpoPartnerReply) GetGroups() []*ExpoPartnerGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

type ExpoTopicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *ExpoTopicRequest) Reset() {
	*x = ExpoTopicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoTopicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoTopicRequest) ProtoMessage() {}

func (x *ExpoTopicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoTopicRequest.ProtoReflect.Descriptor instead.
func (*ExpoTopicRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{35}
}

func (x *ExpoTopicRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *ExpoTopicRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ExpoTopicRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ExpoTopicItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Title    string `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	ImageUrl string `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`
	Avatar   string `protobuf:"bytes,4,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Nickname string `protobuf:"bytes,5,opt,name=nickname,json=nickname,proto3" json:"nickname"`
	Like     bool   `protobuf:"varint,6,opt,name=like,json=like,proto3" json:"like"`
}

func (x *ExpoTopicItem) Reset() {
	*x = ExpoTopicItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoTopicItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoTopicItem) ProtoMessage() {}

func (x *ExpoTopicItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoTopicItem.ProtoReflect.Descriptor instead.
func (*ExpoTopicItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{36}
}

func (x *ExpoTopicItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExpoTopicItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExpoTopicItem) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *ExpoTopicItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ExpoTopicItem) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ExpoTopicItem) GetLike() bool {
	if x != nil {
		return x.Like
	}
	return false
}

type ExpoTopicReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topics []*ExpoTopicItem `protobuf:"bytes,1,rep,name=topics,json=topics,proto3" json:"topics"`
}

func (x *ExpoTopicReply) Reset() {
	*x = ExpoTopicReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoTopicReply) ProtoMessage() {}

func (x *ExpoTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoTopicReply.ProtoReflect.Descriptor instead.
func (*ExpoTopicReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{37}
}

func (x *ExpoTopicReply) GetTopics() []*ExpoTopicItem {
	if x != nil {
		return x.Topics
	}
	return nil
}

type GetSpeakerListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetSpeakerListRequest) Reset() {
	*x = GetSpeakerListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpeakerListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpeakerListRequest) ProtoMessage() {}

func (x *GetSpeakerListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpeakerListRequest.ProtoReflect.Descriptor instead.
func (*GetSpeakerListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetSpeakerListRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type ExpoSpeakerSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date     string         `protobuf:"bytes,1,opt,name=date,json=date,proto3" json:"date"`
	Speakers []*ExpoSpeaker `protobuf:"bytes,2,rep,name=speakers,json=speakers,proto3" json:"speakers"`
}

func (x *ExpoSpeakerSchedule) Reset() {
	*x = ExpoSpeakerSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoSpeakerSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoSpeakerSchedule) ProtoMessage() {}

func (x *ExpoSpeakerSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoSpeakerSchedule.ProtoReflect.Descriptor instead.
func (*ExpoSpeakerSchedule) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{39}
}

func (x *ExpoSpeakerSchedule) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *ExpoSpeakerSchedule) GetSpeakers() []*ExpoSpeaker {
	if x != nil {
		return x.Speakers
	}
	return nil
}

type ExpoSpeaker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpeakerId          int64  `protobuf:"varint,1,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id"`
	SpeakerName        string `protobuf:"bytes,2,opt,name=speaker_name,json=speakerName,proto3" json:"speaker_name"`
	SpeakerAvatar      string `protobuf:"bytes,3,opt,name=speaker_avatar,json=speakerAvatar,proto3" json:"speaker_avatar"`
	Timestamp          int64  `protobuf:"varint,4,opt,name=timestamp,json=timestamp,proto3" json:"timestamp"`
	TimestampShow      string `protobuf:"bytes,5,opt,name=timestamp_show,json=timestampShow,proto3" json:"timestamp_show"`
	Theme              string `protobuf:"bytes,6,opt,name=theme,json=theme,proto3" json:"theme"`
	Description        string `protobuf:"bytes,7,opt,name=description,json=description,proto3" json:"description"`
	Speaker            string `protobuf:"bytes,8,opt,name=speaker,json=speaker,proto3" json:"speaker"`
	Subscribe          bool   `protobuf:"varint,9,opt,name=subscribe,json=subscribe,proto3" json:"subscribe"`
	SpeakerDescription string `protobuf:"bytes,10,opt,name=speaker_description,json=speakerDescription,proto3" json:"speaker_description"`
}

func (x *ExpoSpeaker) Reset() {
	*x = ExpoSpeaker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpoSpeaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpoSpeaker) ProtoMessage() {}

func (x *ExpoSpeaker) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpoSpeaker.ProtoReflect.Descriptor instead.
func (*ExpoSpeaker) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{40}
}

func (x *ExpoSpeaker) GetSpeakerId() int64 {
	if x != nil {
		return x.SpeakerId
	}
	return 0
}

func (x *ExpoSpeaker) GetSpeakerName() string {
	if x != nil {
		return x.SpeakerName
	}
	return ""
}

func (x *ExpoSpeaker) GetSpeakerAvatar() string {
	if x != nil {
		return x.SpeakerAvatar
	}
	return ""
}

func (x *ExpoSpeaker) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ExpoSpeaker) GetTimestampShow() string {
	if x != nil {
		return x.TimestampShow
	}
	return ""
}

func (x *ExpoSpeaker) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

func (x *ExpoSpeaker) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExpoSpeaker) GetSpeaker() string {
	if x != nil {
		return x.Speaker
	}
	return ""
}

func (x *ExpoSpeaker) GetSubscribe() bool {
	if x != nil {
		return x.Subscribe
	}
	return false
}

func (x *ExpoSpeaker) GetSpeakerDescription() string {
	if x != nil {
		return x.SpeakerDescription
	}
	return ""
}

type GetSpeakerListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schedule []*ExpoSpeakerSchedule `protobuf:"bytes,1,rep,name=schedule,json=schedule,proto3" json:"schedule"`
}

func (x *GetSpeakerListReply) Reset() {
	*x = GetSpeakerListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpeakerListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpeakerListReply) ProtoMessage() {}

func (x *GetSpeakerListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpeakerListReply.ProtoReflect.Descriptor instead.
func (*GetSpeakerListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetSpeakerListReply) GetSchedule() []*ExpoSpeakerSchedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

type GetSpeakerDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpeakerId int64 `protobuf:"varint,1,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id"`
	ExpoId    int64 `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetSpeakerDetailRequest) Reset() {
	*x = GetSpeakerDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpeakerDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpeakerDetailRequest) ProtoMessage() {}

func (x *GetSpeakerDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpeakerDetailRequest.ProtoReflect.Descriptor instead.
func (*GetSpeakerDetailRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetSpeakerDetailRequest) GetSpeakerId() int64 {
	if x != nil {
		return x.SpeakerId
	}
	return 0
}

func (x *GetSpeakerDetailRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type SocialMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon  string `protobuf:"bytes,1,opt,name=icon,json=icon,proto3" json:"icon"`
	Name  string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Value string `protobuf:"bytes,3,opt,name=value,json=value,proto3" json:"value"`
}

func (x *SocialMedia) Reset() {
	*x = SocialMedia{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SocialMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SocialMedia) ProtoMessage() {}

func (x *SocialMedia) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SocialMedia.ProtoReflect.Descriptor instead.
func (*SocialMedia) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{43}
}

func (x *SocialMedia) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *SocialMedia) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SocialMedia) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GetSpeakerDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Name        string                 `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Avatar      string                 `protobuf:"bytes,3,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Fans        int64                  `protobuf:"varint,4,opt,name=fans,json=fans,proto3" json:"fans"`
	Follow      int64                  `protobuf:"varint,5,opt,name=follow,json=follow,proto3" json:"follow"`
	Like        int64                  `protobuf:"varint,6,opt,name=like,json=like,proto3" json:"like"`
	Description string                 `protobuf:"bytes,7,opt,name=description,json=description,proto3" json:"description"`
	Label       []string               `protobuf:"bytes,8,rep,name=label,json=label,proto3" json:"label"`
	SocialMedia []*SocialMedia         `protobuf:"bytes,9,rep,name=social_media,json=socialMedia,proto3" json:"social_media"`
	Schedule    []*ExpoSpeakerSchedule `protobuf:"bytes,10,rep,name=schedule,json=schedule,proto3" json:"schedule"`
	UserId      string                 `protobuf:"bytes,11,opt,name=user_id,json=userId,proto3" json:"user_id"`
}

func (x *GetSpeakerDetailReply) Reset() {
	*x = GetSpeakerDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpeakerDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpeakerDetailReply) ProtoMessage() {}

func (x *GetSpeakerDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpeakerDetailReply.ProtoReflect.Descriptor instead.
func (*GetSpeakerDetailReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetSpeakerDetailReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetSpeakerDetailReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetSpeakerDetailReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetSpeakerDetailReply) GetFans() int64 {
	if x != nil {
		return x.Fans
	}
	return 0
}

func (x *GetSpeakerDetailReply) GetFollow() int64 {
	if x != nil {
		return x.Follow
	}
	return 0
}

func (x *GetSpeakerDetailReply) GetLike() int64 {
	if x != nil {
		return x.Like
	}
	return 0
}

func (x *GetSpeakerDetailReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetSpeakerDetailReply) GetLabel() []string {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *GetSpeakerDetailReply) GetSocialMedia() []*SocialMedia {
	if x != nil {
		return x.SocialMedia
	}
	return nil
}

func (x *GetSpeakerDetailReply) GetSchedule() []*ExpoSpeakerSchedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

func (x *GetSpeakerDetailReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetAudienceTabRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
}

func (x *GetAudienceTabRequest) Reset() {
	*x = GetAudienceTabRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAudienceTabRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudienceTabRequest) ProtoMessage() {}

func (x *GetAudienceTabRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudienceTabRequest.ProtoReflect.Descriptor instead.
func (*GetAudienceTabRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetAudienceTabRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

type AudienceTab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Id   string `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`
}

func (x *AudienceTab) Reset() {
	*x = AudienceTab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceTab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceTab) ProtoMessage() {}

func (x *AudienceTab) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceTab.ProtoReflect.Descriptor instead.
func (*AudienceTab) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{46}
}

func (x *AudienceTab) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AudienceTab) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetAudienceTabReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tabs []*AudienceTab `protobuf:"bytes,1,rep,name=tabs,json=tabs,proto3" json:"tabs"`
}

func (x *GetAudienceTabReply) Reset() {
	*x = GetAudienceTabReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAudienceTabReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudienceTabReply) ProtoMessage() {}

func (x *GetAudienceTabReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudienceTabReply.ProtoReflect.Descriptor instead.
func (*GetAudienceTabReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{47}
}

func (x *GetAudienceTabReply) GetTabs() []*AudienceTab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

type GetAudienceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId  int64  `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	TabId   string `protobuf:"bytes,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id"`
	Page    int32  `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`
	Size    int32  `protobuf:"varint,4,opt,name=size,json=size,proto3" json:"size"`
	Keyword string `protobuf:"bytes,5,opt,name=keyword,json=keyword,proto3" json:"keyword"`
}

func (x *GetAudienceRequest) Reset() {
	*x = GetAudienceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAudienceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudienceRequest) ProtoMessage() {}

func (x *GetAudienceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudienceRequest.ProtoReflect.Descriptor instead.
func (*GetAudienceRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetAudienceRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetAudienceRequest) GetTabId() string {
	if x != nil {
		return x.TabId
	}
	return ""
}

func (x *GetAudienceRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAudienceRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetAudienceRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type AudienceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string   `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Nickname    string   `protobuf:"bytes,2,opt,name=nickname,json=nickname,proto3" json:"nickname"`
	Avatar      string   `protobuf:"bytes,3,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	CountryCode string   `protobuf:"bytes,4,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	Identity    Identity `protobuf:"varint,5,opt,name=identity,json=identity,proto3,enum=api.expo.v1.Identity" json:"identity"`
	Check       bool     `protobuf:"varint,6,opt,name=check,json=check,proto3" json:"check"`
	Label       []string `protobuf:"bytes,7,rep,name=label,json=label,proto3" json:"label"`
}

func (x *AudienceItem) Reset() {
	*x = AudienceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceItem) ProtoMessage() {}

func (x *AudienceItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceItem.ProtoReflect.Descriptor instead.
func (*AudienceItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{49}
}

func (x *AudienceItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AudienceItem) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *AudienceItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AudienceItem) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *AudienceItem) GetIdentity() Identity {
	if x != nil {
		return x.Identity
	}
	return Identity_IDENTITY_UNKNOWN
}

func (x *AudienceItem) GetCheck() bool {
	if x != nil {
		return x.Check
	}
	return false
}

func (x *AudienceItem) GetLabel() []string {
	if x != nil {
		return x.Label
	}
	return nil
}

type GetAudienceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*AudienceItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetAudienceReply) Reset() {
	*x = GetAudienceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAudienceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudienceReply) ProtoMessage() {}

func (x *GetAudienceReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudienceReply.ProtoReflect.Descriptor instead.
func (*GetAudienceReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetAudienceReply) GetItems() []*AudienceItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetExhibitorListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *GetExhibitorListRequest) Reset() {
	*x = GetExhibitorListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExhibitorListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExhibitorListRequest) ProtoMessage() {}

func (x *GetExhibitorListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExhibitorListRequest.ProtoReflect.Descriptor instead.
func (*GetExhibitorListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetExhibitorListRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetExhibitorListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetExhibitorListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type Exhibitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string   `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name             string   `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Logo             string   `protobuf:"bytes,3,opt,name=logo,json=logo,proto3" json:"logo"`
	SponsorLevelIcon string   `protobuf:"bytes,4,opt,name=sponsor_level_icon,json=sponsorLevelIcon,proto3" json:"sponsor_level_icon"`
	Booth            string   `protobuf:"bytes,5,opt,name=booth,json=booth,proto3" json:"booth"`
	Employees        []string `protobuf:"bytes,6,rep,name=employees,json=employees,proto3" json:"employees"`
}

func (x *Exhibitor) Reset() {
	*x = Exhibitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Exhibitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Exhibitor) ProtoMessage() {}

func (x *Exhibitor) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Exhibitor.ProtoReflect.Descriptor instead.
func (*Exhibitor) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{52}
}

func (x *Exhibitor) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Exhibitor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Exhibitor) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *Exhibitor) GetSponsorLevelIcon() string {
	if x != nil {
		return x.SponsorLevelIcon
	}
	return ""
}

func (x *Exhibitor) GetBooth() string {
	if x != nil {
		return x.Booth
	}
	return ""
}

func (x *Exhibitor) GetEmployees() []string {
	if x != nil {
		return x.Employees
	}
	return nil
}

type GetExhibitorListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Exhibitor `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetExhibitorListReply) Reset() {
	*x = GetExhibitorListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExhibitorListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExhibitorListReply) ProtoMessage() {}

func (x *GetExhibitorListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExhibitorListReply.ProtoReflect.Descriptor instead.
func (*GetExhibitorListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{53}
}

func (x *GetExhibitorListReply) GetItems() []*Exhibitor {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetExhibitorEmployeeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *GetExhibitorEmployeeListRequest) Reset() {
	*x = GetExhibitorEmployeeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExhibitorEmployeeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExhibitorEmployeeListRequest) ProtoMessage() {}

func (x *GetExhibitorEmployeeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExhibitorEmployeeListRequest.ProtoReflect.Descriptor instead.
func (*GetExhibitorEmployeeListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetExhibitorEmployeeListRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetExhibitorEmployeeListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetExhibitorEmployeeListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ExhibitorEmployee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Avatar           string `protobuf:"bytes,2,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Name             string `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Title            string `protobuf:"bytes,4,opt,name=title,json=title,proto3" json:"title"`
	ExhibitorLogo    string `protobuf:"bytes,5,opt,name=exhibitor_logo,json=exhibitorLogo,proto3" json:"exhibitor_logo"`
	ExhibitorName    string `protobuf:"bytes,6,opt,name=exhibitor_name,json=exhibitorName,proto3" json:"exhibitor_name"`
	SponsorLevelIcon string `protobuf:"bytes,7,opt,name=sponsor_level_icon,json=sponsorLevelIcon,proto3" json:"sponsor_level_icon"`
	Booth            string `protobuf:"bytes,8,opt,name=booth,json=booth,proto3" json:"booth"`
}

func (x *ExhibitorEmployee) Reset() {
	*x = ExhibitorEmployee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitorEmployee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitorEmployee) ProtoMessage() {}

func (x *ExhibitorEmployee) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitorEmployee.ProtoReflect.Descriptor instead.
func (*ExhibitorEmployee) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{55}
}

func (x *ExhibitorEmployee) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExhibitorEmployee) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ExhibitorEmployee) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExhibitorEmployee) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExhibitorEmployee) GetExhibitorLogo() string {
	if x != nil {
		return x.ExhibitorLogo
	}
	return ""
}

func (x *ExhibitorEmployee) GetExhibitorName() string {
	if x != nil {
		return x.ExhibitorName
	}
	return ""
}

func (x *ExhibitorEmployee) GetSponsorLevelIcon() string {
	if x != nil {
		return x.SponsorLevelIcon
	}
	return ""
}

func (x *ExhibitorEmployee) GetBooth() string {
	if x != nil {
		return x.Booth
	}
	return ""
}

type GetExhibitorEmployeeListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Employees []*ExhibitorEmployee `protobuf:"bytes,1,rep,name=employees,json=employees,proto3" json:"employees"`
}

func (x *GetExhibitorEmployeeListReply) Reset() {
	*x = GetExhibitorEmployeeListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExhibitorEmployeeListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExhibitorEmployeeListReply) ProtoMessage() {}

func (x *GetExhibitorEmployeeListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExhibitorEmployeeListReply.ProtoReflect.Descriptor instead.
func (*GetExhibitorEmployeeListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetExhibitorEmployeeListReply) GetEmployees() []*ExhibitorEmployee {
	if x != nil {
		return x.Employees
	}
	return nil
}

type GetExhibitorBoothListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *GetExhibitorBoothListRequest) Reset() {
	*x = GetExhibitorBoothListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExhibitorBoothListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExhibitorBoothListRequest) ProtoMessage() {}

func (x *GetExhibitorBoothListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExhibitorBoothListRequest.ProtoReflect.Descriptor instead.
func (*GetExhibitorBoothListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{57}
}

func (x *GetExhibitorBoothListRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetExhibitorBoothListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetExhibitorBoothListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ExhibitorBooth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Booth            string `protobuf:"bytes,2,opt,name=booth,json=booth,proto3" json:"booth"`
	Name             string `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Logo             string `protobuf:"bytes,4,opt,name=logo,json=logo,proto3" json:"logo"`
	SponsorLevelIcon string `protobuf:"bytes,5,opt,name=sponsor_level_icon,json=sponsorLevelIcon,proto3" json:"sponsor_level_icon"`
}

func (x *ExhibitorBooth) Reset() {
	*x = ExhibitorBooth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitorBooth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitorBooth) ProtoMessage() {}

func (x *ExhibitorBooth) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitorBooth.ProtoReflect.Descriptor instead.
func (*ExhibitorBooth) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{58}
}

func (x *ExhibitorBooth) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExhibitorBooth) GetBooth() string {
	if x != nil {
		return x.Booth
	}
	return ""
}

func (x *ExhibitorBooth) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExhibitorBooth) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *ExhibitorBooth) GetSponsorLevelIcon() string {
	if x != nil {
		return x.SponsorLevelIcon
	}
	return ""
}

type GetExhibitorBoothListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Booths []*ExhibitorBooth `protobuf:"bytes,1,rep,name=booths,json=booths,proto3" json:"booths"`
}

func (x *GetExhibitorBoothListReply) Reset() {
	*x = GetExhibitorBoothListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExhibitorBoothListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExhibitorBoothListReply) ProtoMessage() {}

func (x *GetExhibitorBoothListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExhibitorBoothListReply.ProtoReflect.Descriptor instead.
func (*GetExhibitorBoothListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{59}
}

func (x *GetExhibitorBoothListReply) GetBooths() []*ExhibitorBooth {
	if x != nil {
		return x.Booths
	}
	return nil
}

type TicketBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TicketCode   string       `protobuf:"bytes,1,opt,name=ticket_code,json=ticketCode,proto3" json:"ticket_code"`
	ExpoId       int64        `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Name         string       `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Logo         string       `protobuf:"bytes,4,opt,name=logo,json=logo,proto3" json:"logo"`
	Location     string       `protobuf:"bytes,5,opt,name=location,json=location,proto3" json:"location"`
	Address      string       `protobuf:"bytes,6,opt,name=address,json=address,proto3" json:"address"`
	StartTime    int64        `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	PaymentTotal string       `protobuf:"bytes,8,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	Status       TicketStatus `protobuf:"varint,9,opt,name=status,json=status,proto3,enum=api.expo.v1.TicketStatus" json:"status"`
}

func (x *TicketBase) Reset() {
	*x = TicketBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketBase) ProtoMessage() {}

func (x *TicketBase) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketBase.ProtoReflect.Descriptor instead.
func (*TicketBase) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{60}
}

func (x *TicketBase) GetTicketCode() string {
	if x != nil {
		return x.TicketCode
	}
	return ""
}

func (x *TicketBase) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *TicketBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TicketBase) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *TicketBase) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *TicketBase) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TicketBase) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *TicketBase) GetPaymentTotal() string {
	if x != nil {
		return x.PaymentTotal
	}
	return ""
}

func (x *TicketBase) GetStatus() TicketStatus {
	if x != nil {
		return x.Status
	}
	return TicketStatus_TICKET_STATUS_REVIEW
}

type TicketDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TicketCode   string       `protobuf:"bytes,1,opt,name=ticket_code,json=ticketCode,proto3" json:"ticket_code"`
	ExpoId       int64        `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Name         string       `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Logo         string       `protobuf:"bytes,4,opt,name=logo,json=logo,proto3" json:"logo"`
	Location     string       `protobuf:"bytes,5,opt,name=location,json=location,proto3" json:"location"`
	Address      string       `protobuf:"bytes,6,opt,name=address,json=address,proto3" json:"address"`
	StartTime    int64        `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	PaymentTotal string       `protobuf:"bytes,8,opt,name=payment_total,json=paymentTotal,proto3" json:"payment_total"`
	Status       TicketStatus `protobuf:"varint,9,opt,name=status,json=status,proto3,enum=api.expo.v1.TicketStatus" json:"status"`
	Username     string       `protobuf:"bytes,10,opt,name=username,json=username,proto3" json:"username"`
	Phone        string       `protobuf:"bytes,11,opt,name=phone,json=phone,proto3" json:"phone"`
	Email        string       `protobuf:"bytes,12,opt,name=email,json=email,proto3" json:"email"`
	Industry     Industry     `protobuf:"varint,13,opt,name=industry,json=industry,proto3,enum=api.expo.v1.Industry" json:"industry"`
	Identity     Identity     `protobuf:"varint,14,opt,name=identity,json=identity,proto3,enum=api.expo.v1.Identity" json:"identity"`
	Company      string       `protobuf:"bytes,15,opt,name=company,json=company,proto3" json:"company"`
	Job          string       `protobuf:"bytes,16,opt,name=job,json=job,proto3" json:"job"`
	CreatedAt    int64        `protobuf:"varint,17,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
}

func (x *TicketDetail) Reset() {
	*x = TicketDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketDetail) ProtoMessage() {}

func (x *TicketDetail) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketDetail.ProtoReflect.Descriptor instead.
func (*TicketDetail) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{61}
}

func (x *TicketDetail) GetTicketCode() string {
	if x != nil {
		return x.TicketCode
	}
	return ""
}

func (x *TicketDetail) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *TicketDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TicketDetail) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *TicketDetail) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *TicketDetail) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TicketDetail) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *TicketDetail) GetPaymentTotal() string {
	if x != nil {
		return x.PaymentTotal
	}
	return ""
}

func (x *TicketDetail) GetStatus() TicketStatus {
	if x != nil {
		return x.Status
	}
	return TicketStatus_TICKET_STATUS_REVIEW
}

func (x *TicketDetail) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *TicketDetail) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *TicketDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *TicketDetail) GetIndustry() Industry {
	if x != nil {
		return x.Industry
	}
	return Industry_INDUSTRY_UNKNOWN
}

func (x *TicketDetail) GetIdentity() Identity {
	if x != nil {
		return x.Identity
	}
	return Identity_IDENTITY_UNKNOWN
}

func (x *TicketDetail) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *TicketDetail) GetJob() string {
	if x != nil {
		return x.Job
	}
	return ""
}

func (x *TicketDetail) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type GetTicketListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int32 `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
	Page int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
}

func (x *GetTicketListRequest) Reset() {
	*x = GetTicketListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketListRequest) ProtoMessage() {}

func (x *GetTicketListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketListRequest.ProtoReflect.Descriptor instead.
func (*GetTicketListRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{62}
}

func (x *GetTicketListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetTicketListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GetTicketListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*TicketBase `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetTicketListReply) Reset() {
	*x = GetTicketListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketListReply) ProtoMessage() {}

func (x *GetTicketListReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketListReply.ProtoReflect.Descriptor instead.
func (*GetTicketListReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{63}
}

func (x *GetTicketListReply) GetItems() []*TicketBase {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetTicketDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TicketCode string `protobuf:"bytes,1,opt,name=ticket_code,json=ticketCode,proto3" json:"ticket_code"`
}

func (x *GetTicketDetailRequest) Reset() {
	*x = GetTicketDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketDetailRequest) ProtoMessage() {}

func (x *GetTicketDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketDetailRequest.ProtoReflect.Descriptor instead.
func (*GetTicketDetailRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetTicketDetailRequest) GetTicketCode() string {
	if x != nil {
		return x.TicketCode
	}
	return ""
}

type GetLiveImagesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*LiveImage `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`     // 展会图片列表
	Total int64        `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"` // 总数
	Page  int32        `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`    // 当前页
	Size  int32        `protobuf:"varint,4,opt,name=size,json=size,proto3" json:"size"`
}

func (x *GetLiveImagesReply) Reset() {
	*x = GetLiveImagesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLiveImagesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLiveImagesReply) ProtoMessage() {}

func (x *GetLiveImagesReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLiveImagesReply.ProtoReflect.Descriptor instead.
func (*GetLiveImagesReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetLiveImagesReply) GetList() []*LiveImage {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetLiveImagesReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetLiveImagesReply) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLiveImagesReply) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type GetLiveImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId int64 `protobuf:"varint,1,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`
	Page   int32 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"` // 页码，从1开始
	Size   int32 `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"` // 每页数量
}

func (x *GetLiveImagesRequest) Reset() {
	*x = GetLiveImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLiveImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLiveImagesRequest) ProtoMessage() {}

func (x *GetLiveImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLiveImagesRequest.ProtoReflect.Descriptor instead.
func (*GetLiveImagesRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{66}
}

func (x *GetLiveImagesRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *GetLiveImagesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLiveImagesRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type LiveImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                uint64 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`                                                 // 图片ID
	ExpoId            int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`                                   // 展会ID
	ImageUrl          string `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url"`                              // 图片地址
	ImageThumbnailUrl string `protobuf:"bytes,4,opt,name=image_thumbnail_url,json=imageThumbnailUrl,proto3" json:"image_thumbnail_url"` // 图片地址
	CreatedAt         int64  `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`                          // 创建时间（Unix时间戳）
}

func (x *LiveImage) Reset() {
	*x = LiveImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiveImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveImage) ProtoMessage() {}

func (x *LiveImage) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveImage.ProtoReflect.Descriptor instead.
func (*LiveImage) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{67}
}

func (x *LiveImage) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LiveImage) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *LiveImage) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *LiveImage) GetImageThumbnailUrl() string {
	if x != nil {
		return x.ImageThumbnailUrl
	}
	return ""
}

func (x *LiveImage) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// 人脸搜索请求
type FaceSearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchPhotoUrl string `protobuf:"bytes,1,opt,name=search_photo_url,json=searchPhotoUrl,proto3" json:"search_photo_url"` // 用户自拍照URL
	ExpoId         int64  `protobuf:"varint,2,opt,name=expo_id,json=expoId,proto3" json:"expo_id"`                          // 展会id
}

func (x *FaceSearchRequest) Reset() {
	*x = FaceSearchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceSearchRequest) ProtoMessage() {}

func (x *FaceSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceSearchRequest.ProtoReflect.Descriptor instead.
func (*FaceSearchRequest) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{68}
}

func (x *FaceSearchRequest) GetSearchPhotoUrl() string {
	if x != nil {
		return x.SearchPhotoUrl
	}
	return ""
}

func (x *FaceSearchRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

// 人脸搜索响应
type FaceSearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchId           string                  `protobuf:"bytes,1,opt,name=search_id,json=searchId,proto3" json:"search_id"`                                  // 搜索批次ID
	TotalSearchedFaces int32                   `protobuf:"varint,2,opt,name=total_searched_faces,json=totalSearchedFaces,proto3" json:"total_searched_faces"` // 搜索到的总人脸数
	SearchDurationMs   int64                   `protobuf:"varint,3,opt,name=search_duration_ms,json=searchDurationMs,proto3" json:"search_duration_ms"`       // 搜索耗时(毫秒)
	Results            []*FaceSearchResultItem `protobuf:"bytes,4,rep,name=results,json=results,proto3" json:"results"`                                       // 搜索结果列表
	SearchFaceRect     []*FaceRect             `protobuf:"bytes,5,rep,name=search_face_rect,json=searchFaceRect,proto3" json:"search_face_rect"`
	TotalResults       int32                   `protobuf:"varint,6,opt,name=total_results,json=totalResults,proto3" json:"total_results"` // 总结果数
	Page               int32                   `protobuf:"varint,7,opt,name=page,json=page,proto3" json:"page"`                           // 当前页码
	PageSize           int32                   `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size"`             // 每页大小
	FromCache          bool                    `protobuf:"varint,9,opt,name=from_cache,json=fromCache,proto3" json:"from_cache"`          // 是否来自缓存
}

func (x *FaceSearchReply) Reset() {
	*x = FaceSearchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceSearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceSearchReply) ProtoMessage() {}

func (x *FaceSearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceSearchReply.ProtoReflect.Descriptor instead.
func (*FaceSearchReply) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{69}
}

func (x *FaceSearchReply) GetSearchId() string {
	if x != nil {
		return x.SearchId
	}
	return ""
}

func (x *FaceSearchReply) GetTotalSearchedFaces() int32 {
	if x != nil {
		return x.TotalSearchedFaces
	}
	return 0
}

func (x *FaceSearchReply) GetSearchDurationMs() int64 {
	if x != nil {
		return x.SearchDurationMs
	}
	return 0
}

func (x *FaceSearchReply) GetResults() []*FaceSearchResultItem {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *FaceSearchReply) GetSearchFaceRect() []*FaceRect {
	if x != nil {
		return x.SearchFaceRect
	}
	return nil
}

func (x *FaceSearchReply) GetTotalResults() int32 {
	if x != nil {
		return x.TotalResults
	}
	return 0
}

func (x *FaceSearchReply) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FaceSearchReply) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FaceSearchReply) GetFromCache() bool {
	if x != nil {
		return x.FromCache
	}
	return false
}

// 人脸搜索结果项
type FaceSearchResultItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhotoUrl          string  `protobuf:"bytes,1,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url"`                              // 匹配到的展会照片URL列表
	PhotoThumbnailUrl string  `protobuf:"bytes,2,opt,name=photo_thumbnail_url,json=photoThumbnailUrl,proto3" json:"photo_thumbnail_url"` //缩略图
	MaxSimilarity     float32 `protobuf:"fixed32,3,opt,name=max_similarity,json=maxSimilarity,proto3" json:"max_similarity"`             // 最高相似度
}

func (x *FaceSearchResultItem) Reset() {
	*x = FaceSearchResultItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceSearchResultItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceSearchResultItem) ProtoMessage() {}

func (x *FaceSearchResultItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceSearchResultItem.ProtoReflect.Descriptor instead.
func (*FaceSearchResultItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_service_proto_rawDescGZIP(), []int{70}
}

func (x *FaceSearchResultItem) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *FaceSearchResultItem) GetPhotoThumbnailUrl() string {
	if x != nil {
		return x.PhotoThumbnailUrl
	}
	return ""
}

func (x *FaceSearchResultItem) GetMaxSimilarity() float32 {
	if x != nil {
		return x.MaxSimilarity
	}
	return 0
}

var File_expo_v1_service_proto protoreflect.FileDescriptor

var file_expo_v1_service_proto_rawDesc = []byte{
	0x0a, 0x15, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x04,
	0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x89, 0x80, 0xe5, 0x9c, 0xa8,
	0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe9, 0x97, 0xa8,
	0xe7, 0xa5, 0xa8, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x57, 0x0a, 0x0d, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0xe9, 0x97, 0xa8, 0xe7, 0xa5, 0xa8, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0x52, 0x0c, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c,
	0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe9, 0x97, 0xa8, 0xe7, 0xa5, 0xa8, 0xe6, 0x80, 0xbb, 0xe9, 0xa2, 0x9d, 0x52, 0x0c,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x36, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x88, 0x9b,
	0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x3e, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6f,
	0x52, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x49, 0x64, 0x22, 0xf8, 0x02, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x67, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x52, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x37, 0x92, 0x41,
	0x34, 0x2a, 0x32, 0xe6, 0x9d, 0x83, 0xe7, 0x9b, 0x8a, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef,
	0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe4, 0xba, 0x92, 0xe5,
	0x8a, 0xa8, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe8, 0x87, 0xaa, 0xe7, 0x94, 0xb1, 0xe8,
	0x81, 0x8a, 0xe5, 0xa4, 0xa9, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x9d, 0x83, 0xe7, 0x9b, 0x8a, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0x94, 0x81, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x52, 0x04, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9d, 0x83, 0xe7, 0x9b, 0x8a, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0xbf, 0x80, 0xe6,
	0xb4, 0xbb, 0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x04,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x30, 0x0a,
	0x0a, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe6, 0xa0,
	0x87, 0xe9, 0xa2, 0x98, 0x52, 0x09, 0x6a, 0x75, 0x6d, 0x70, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x22,
	0x44, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x69, 0x67, 0x68, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x69, 0x67, 0x68, 0x74, 0x52, 0x06, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x07, 0x45, 0x78, 0x70, 0x6f, 0x54, 0x61,
	0x62, 0x12, 0x1b, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0x74, 0x61, 0x62, 0x20, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x61, 0x62,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x54, 0x61, 0x62, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe5, 0xad, 0x90, 0x74, 0x61, 0x62, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54,
	0x61, 0x62, 0x73, 0x22, 0x43, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x54, 0x61, 0x62, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x54, 0x61, 0x62, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04, 0x74, 0x61,
	0x62, 0x73, 0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x22, 0xcd, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x70,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06,
	0x74, 0x61, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0x74, 0x61, 0x62, 0x5f, 0x69, 0x64, 0x52, 0x05, 0x74, 0x61, 0x62, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x61, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x61, 0x62, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x61, 0x62, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x22, 0x92,
	0x41, 0x1f, 0x2a, 0x1d, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae,
	0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31,
	0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe5, 0x88, 0x86, 0xe9,
	0xa1, 0xb5, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae,
	0xa4, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xf0, 0x03, 0x0a, 0x08, 0x45, 0x78, 0x70,
	0x6f, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x69, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x6c,
	0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x77, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x46, 0x92, 0x41, 0x43, 0x2a, 0x41, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9a, 0x30, 0x2d, 0xe6, 0x9c, 0xaa, 0xe7, 0x9f, 0xa5,
	0xef, 0xbc, 0x9b, 0x31, 0x2d, 0xe6, 0x9c, 0xaa, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xef, 0xbc,
	0x9b, 0x32, 0x2d, 0xe8, 0xbf, 0x9b, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0xad, 0xef, 0xbc, 0x9b, 0x33,
	0x2d, 0xe5, 0xb7, 0xb2, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xa7, 0x82, 0xe4, 0xbc, 0x97, 0x52, 0x08, 0x61, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe4, 0xbb,
	0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x41, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x22, 0x92, 0x41, 0x1f, 0x2a, 0x1d, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x28, 0xe5,
	0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x29, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6,
	0x97, 0xb6, 0xe5, 0x8c, 0xba, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x3c, 0x0a, 0x0d, 0x45,
	0x78, 0x70, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x05,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x05, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x0d, 0x45, 0x78,
	0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9,
	0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x45, 0x0a,
	0x12, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x9b, 0xbe, 0xe6,
	0xa0, 0x87, 0x52, 0x10, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x49, 0x63, 0x6f, 0x6e, 0x22, 0xba, 0x02, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x6f, 0x41, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x55, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x41,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa7, 0x82, 0xe4, 0xbc,
	0x97, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe7, 0xbb, 0x84, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba,
	0xba, 0xe5, 0x91, 0x98, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x12, 0x51,
	0x0a, 0x0a, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x15,
	0x92, 0x41, 0x12, 0x2a, 0x10, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0a, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72,
	0x73, 0x22, 0x5b, 0x0a, 0x08, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a,
	0x08, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x69, 0x64, 0x52, 0x07,
	0x6c, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x9b, 0xb4, 0xe6,
	0x92, 0xad, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9e,
	0x08, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x04,
	0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a,
	0x0a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67,
	0x6f, 0x12, 0x77, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x46, 0x92, 0x41, 0x43, 0x2a,
	0x41, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9a,
	0x30, 0x2d, 0xe6, 0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xef, 0xbc, 0x9b, 0x31, 0x2d, 0xe6, 0x9c, 0xaa,
	0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xef, 0xbc, 0x9b, 0x32, 0x2d, 0xe8, 0xbf, 0x9b, 0xe8, 0xa1,
	0x8c, 0xe4, 0xb8, 0xad, 0xef, 0xbc, 0x9b, 0x33, 0x2d, 0xe5, 0xb7, 0xb2, 0xe7, 0xbb, 0x93, 0xe6,
	0x9d, 0x9f, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x33,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe4, 0xbb, 0xa3, 0xe7,
	0xa0, 0x81, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x41, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x22, 0x92, 0x41, 0x1f, 0x2a, 0x1d, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x28, 0xe5, 0x8d, 0x95,
	0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x29, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe7, 0xbb, 0x8f, 0xe5, 0xba, 0xa6, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe7, 0xba, 0xac, 0xe5, 0xba, 0xa6, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x99,
	0xbe, 0xe5, 0xba, 0xa6, 0xe5, 0x9c, 0xb0, 0xe5, 0x9b, 0xbe, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87,
	0x52, 0x0a, 0x62, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x0c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xb0, 0xb7, 0xe6, 0xad, 0x8c, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9b, 0xbe, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x0b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0xe6, 0x8a, 0xa5, 0xe5, 0x90, 0x8d, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6c, 0x69,
	0x76, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x6d, 0x61, 0x69,
	0x6e, 0x4c, 0x69, 0x76, 0x65, 0x12, 0x47, 0x0a, 0x05, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe5, 0xa4, 0x9a, 0xe8, 0xa7, 0x86, 0xe8, 0xa7, 0x92, 0xe7, 0x9b, 0xb4, 0xe6, 0x92,
	0xad, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x05, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x12, 0x29,
	0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92,
	0xad, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92,
	0xad, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x0f, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x41,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe8, 0xa7, 0x82, 0xe4, 0xbc, 0x97, 0xe5, 0x88, 0x86, 0xe7, 0xbb, 0x84, 0x52,
	0x0e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22,
	0x3b, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x8d, 0x03, 0x0a,
	0x15, 0x45, 0x78, 0x70, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x90, 0x8d, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x0d,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x85,
	0xac, 0xe5, 0x8f, 0xb8, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x1d, 0x0a,
	0x03, 0x6a, 0x6f, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe8, 0x81, 0x8c, 0xe4, 0xbd, 0x8d, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x3e, 0x0a, 0x08,
	0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xa1, 0x8c, 0xe4,
	0xb8, 0x9a, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x3e, 0x0a, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xba, 0xab, 0xe4,
	0xbb, 0xbd, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xee, 0x02, 0x0a,
	0x13, 0x45, 0x78, 0x70, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe5,
	0x95, 0x86, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x23, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f,
	0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52,
	0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x3c, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe6, 0x94, 0xb6, 0xe6, 0xac, 0xbe, 0xe9,
	0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0x52, 0x0a, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfe, 0x02,
	0x0a, 0x16, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64,
	0x12, 0x34, 0x0a, 0x0c, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95,
	0xe4, 0xbd, 0x8d, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x0b, 0x65, 0x78, 0x68, 0x69, 0x62,
	0x69, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x85,
	0xac, 0xe5, 0x8f, 0xb8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x85, 0xac, 0xe5, 0x8f,
	0xb8, 0xe7, 0xbd, 0x91, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65,
	0x12, 0x28, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0x81, 0x94, 0xe7, 0xb3, 0xbb, 0xe4, 0xba,
	0xba, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x39, 0x0a, 0x0f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba,
	0xe5, 0x8c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x0d, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x41, 0x72, 0x65,
	0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x89, 0x8b, 0xe6, 0x9c,
	0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x16,
	0x0a, 0x14, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55,
	0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x59, 0x0a, 0x16, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x22, 0x9b, 0x03, 0x0a, 0x0f, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0x69, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xa4, 0xb4, 0xe5, 0x83,
	0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe6, 0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0d, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0x92, 0xe5, 0x8a, 0xa8, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe7, 0x82, 0xb9, 0xe8,
	0xb5, 0x9e, 0x52, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8,
	0xaf, 0x84, 0xe8, 0xae, 0xba, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0x89, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x80,
	0xbb, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x45, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0x84, 0xe8, 0xae,
	0xba, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x4c, 0x0a, 0x1b, 0x45,
	0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x08, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe9, 0xa2, 0x84, 0xe5, 0x91, 0x8a, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0x52,
	0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x73, 0x22, 0x43, 0x0a, 0x1a, 0x50, 0x6f, 0x73,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x33,
	0x0a, 0x18, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x3d, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x22, 0x96, 0x03, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a,
	0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x88, 0xe5, 0x8d,
	0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0xef, 0xbc, 0x89, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x38, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x53, 0x68, 0x6f, 0x77, 0x12,
	0x27, 0x0a, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe4, 0xb8, 0xbb, 0xe9, 0xa2,
	0x98, 0x52, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a,
	0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0x52, 0x07,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0xe5,
	0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x45, 0x0a, 0x13, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80,
	0x85, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x12, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x88, 0x01, 0x0a, 0x10,
	0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x48, 0x61, 0x6c, 0x6c,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9c, 0xba, 0xe6, 0xac, 0xa1, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x48, 0x61, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe5, 0x9c, 0xba, 0xe6, 0xac, 0xa1, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe7, 0xba, 0xbf, 0x52,
	0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x71, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x68, 0x61, 0x6c, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x48, 0x61, 0x6c, 0x6c, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9c, 0xba, 0xe6,
	0xac, 0xa1, 0x52, 0x05, 0x68, 0x61, 0x6c, 0x6c, 0x73, 0x22, 0x4c, 0x0a, 0x11, 0x45, 0x78, 0x70,
	0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37,
	0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x3a, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x47,
	0x75, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65,
	0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70,
	0x6f, 0x49, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x6f, 0x42, 0x6f, 0x6f, 0x74,
	0x68, 0x12, 0x33, 0x0a, 0x0b, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x62, 0x72, 0x6f, 0x6b,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x52,
	0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x12, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f,
	0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9, 0xe7,
	0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x10, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbd, 0x8d, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68,
	0x22, 0xc0, 0x03, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a, 0x0d, 0x67, 0x75, 0x69, 0x64, 0x65, 0x5f, 0x6d, 0x61, 0x70,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe5, 0xaf, 0xbc, 0xe8, 0xa7, 0x88, 0xe5, 0x9b, 0xbe, 0x52, 0x0b, 0x67, 0x75, 0x69, 0x64,
	0x65, 0x4d, 0x61, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x42, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x42, 0x6f, 0x6f, 0x74, 0x68, 0x42, 0x14,
	0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0xb1,
	0x95, 0xe4, 0xbd, 0x8d, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x12, 0x2b, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0xa6, 0xe7, 0xbb, 0x86, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd, 0xae, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0xbb, 0x8f, 0xe5, 0xba, 0xa6, 0x52, 0x09, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0xba, 0xac, 0xe5, 0xba, 0xa6, 0x52, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x99, 0xbe, 0xe5, 0xba, 0xa6, 0xe5, 0x9c, 0xb0, 0xe5, 0x9b,
	0xbe, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x52, 0x0b, 0x62, 0x61, 0x69, 0x64, 0x75, 0x4d, 0x61,
	0x70, 0x55, 0x72, 0x6c, 0x12, 0x3d, 0x0a, 0x0e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6d,
	0x61, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe8, 0xb0, 0xb7, 0xe6, 0xad, 0x8c, 0xe5, 0x9c, 0xb0, 0xe5, 0x9b, 0xbe, 0xe9,
	0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x52, 0x0c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4d, 0x61, 0x70,
	0x55, 0x72, 0x6c, 0x22, 0x3c, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49,
	0x64, 0x22, 0x6a, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c,
	0xe5, 0x88, 0x86, 0xe7, 0xbb, 0x84, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99,
	0xe4, 0xbc, 0xb4, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x22, 0x62, 0x0a,
	0x10, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x4e, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99,
	0xe4, 0xbc, 0xb4, 0xe5, 0x88, 0x86, 0xe7, 0xbb, 0x84, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x22, 0x53, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x82, 0x02, 0x0a, 0x0d, 0x45, 0x78, 0x70, 0x6f, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xaf, 0x9d, 0xe9, 0xa2,
	0x98, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0x9d,
	0xe9, 0xa2, 0x98, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x2e, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0x9d, 0xe9, 0xa2, 0x98,
	0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x29, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0x9d, 0xe9, 0xa2, 0x98, 0xe5, 0xa4, 0xb4,
	0xe5, 0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe6, 0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe7,
	0x82, 0xb9, 0xe8, 0xb5, 0x9e, 0x52, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x22, 0x44, 0x0a, 0x0e, 0x45,
	0x78, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a,
	0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x73, 0x22, 0x30, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70,
	0x6f, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x70, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x08, 0x73,
	0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0xbc,
	0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0x52, 0x08, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x73, 0x22, 0xa8, 0x04, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x12, 0x2f, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe6, 0xbc, 0x94, 0xe8,
	0xae, 0xb2, 0xe8, 0x80, 0x85, 0x49, 0x44, 0x52, 0x09, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6,
	0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0b,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2,
	0xe8, 0x80, 0x85, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x88, 0xe5, 0x8d, 0x95, 0xe4, 0xbd,
	0x8d, 0xe7, 0xa7, 0x92, 0xef, 0xbc, 0x89, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x38, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x73, 0x68, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0d, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x27, 0x0a, 0x05,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe4, 0xb8, 0xbb, 0xe9, 0xa2, 0x98, 0x52, 0x05,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x07, 0x73, 0x70,
	0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0x52, 0x07, 0x73, 0x70, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98,
	0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0xae, 0xa2, 0xe9, 0x98, 0x85, 0x52, 0x09, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x45, 0x0a, 0x13, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8,
	0x80, 0x85, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x12, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x66, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xae,
	0xae, 0xe7, 0xa8, 0x8b, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x22, 0x51, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x0b, 0x53, 0x6f, 0x63, 0x69, 0x61,
	0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x23, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xaa, 0x92, 0xe4, 0xbd,
	0x93, 0x69, 0x63, 0x6f, 0x6e, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xaa, 0x92, 0xe4, 0xbd, 0x93, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xaa, 0x92, 0xe4, 0xbd, 0x93, 0xe5, 0x80,
	0xbc, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa5, 0x04, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10,
	0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8,
	0x80, 0x85, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c,
	0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14,
	0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80, 0x85, 0xe5, 0xa4,
	0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x22, 0x0a, 0x04,
	0x66, 0x61, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe7, 0xb2, 0x89, 0xe4, 0xb8, 0x9d, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x66, 0x61, 0x6e, 0x73,
	0x12, 0x26, 0x0a, 0x06, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x85, 0xb3, 0xe6, 0xb3, 0xa8, 0xe6, 0x95, 0xb0,
	0x52, 0x06, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x22, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7, 0x82, 0xb9,
	0xe8, 0xb5, 0x9e, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x36, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe8, 0x80,
	0x85, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x4e, 0x0a, 0x0c, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa4,
	0xbe, 0xe4, 0xba, 0xa4, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0b, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x4f, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x70, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe8, 0xae, 0xae, 0xe7, 0xa8, 0x8b, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x30, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x61, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x22, 0x53, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61,
	0x62, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x43, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c,
	0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x22, 0xe9, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x06, 0x74,
	0x61, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0x92, 0x41, 0x07,
	0x2a, 0x05, 0x74, 0x61, 0x62, 0x49, 0x44, 0x52, 0x05, 0x74, 0x61, 0x62, 0x49, 0x64, 0x12, 0x29,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x15, 0x92, 0x41,
	0x12, 0x2a, 0x10, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8,
	0xae, 0xa4, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe9, 0xa1,
	0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4,
	0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe5, 0x85, 0xb3, 0xe9, 0x94, 0xae, 0xe5, 0xad, 0x97, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0xbb, 0x02, 0x0a, 0x0c, 0x41, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe6, 0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x32, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41,
	0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd,
	0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0xaa, 0x8c, 0xe8, 0xaf, 0x81, 0x52, 0x05, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x12, 0x21, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x43, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x15, 0x92,
	0x41, 0x12, 0x2a, 0x10, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98,
	0xe8, 0xae, 0xa4, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe9,
	0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae,
	0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x94, 0x02, 0x0a, 0x09, 0x45, 0x78,
	0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86,
	0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe5, 0x95,
	0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c,
	0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f,
	0x67, 0x6f, 0x12, 0x45, 0x0a, 0x12, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9, 0xe7, 0xad, 0x89, 0xe7, 0xba,
	0xa7, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x10, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x05, 0x62, 0x6f, 0x6f,
	0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5,
	0xb1, 0x95, 0xe4, 0xbd, 0x8d, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x12,
	0x2f, 0x0a, 0x09, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe5,
	0x91, 0x98, 0xe5, 0xb7, 0xa5, 0x52, 0x09, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73,
	0x22, 0x45, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x65,
	0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78, 0x70,
	0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0xef, 0xbc,
	0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x2d,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x19, 0x92, 0x41,
	0x16, 0x2a, 0x14, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0xef, 0xbc, 0x8c, 0xe9,
	0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x8c, 0x03,
	0x0a, 0x11, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x65, 0x65, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x91, 0x98, 0xe5, 0xb7, 0xa5, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x91, 0x98, 0xe5, 0xb7, 0xa5, 0xe5,
	0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x25, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x91, 0x98, 0xe5, 0xb7, 0xa5, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x91, 0x98, 0xe5, 0xb7, 0xa5,
	0xe8, 0x81, 0x8c, 0xe4, 0xbd, 0x8d, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x36, 0x0a,
	0x0e, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe5,
	0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x0d, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f,
	0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x38, 0x0a, 0x0e, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74,
	0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x0d, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x45, 0x0a, 0x12, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x9b,
	0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x10, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xb1, 0x95, 0xe4,
	0xbd, 0x8d, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x22, 0x5d, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x65, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3c, 0x0a,
	0x09, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x52, 0x09, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x6f, 0x6f, 0x74,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07,
	0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x6f, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0xef,
	0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x2d, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x19, 0x92,
	0x41, 0x16, 0x2a, 0x14, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0xef, 0xbc, 0x8c,
	0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xe8,
	0x01, 0x0a, 0x0e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x6f, 0x6f, 0x74,
	0x68, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x24, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xb1, 0x95, 0xe4, 0xbd, 0x8d, 0xe5, 0x8f, 0xb7, 0x52,
	0x05, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe5, 0x95,
	0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c,
	0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f,
	0x67, 0x6f, 0x12, 0x45, 0x0a, 0x12, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xb5, 0x9e, 0xe5, 0x8a, 0xa9, 0xe7, 0xad, 0x89, 0xe7, 0xba,
	0xa7, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x10, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x51, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x6f, 0x6f, 0x74, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x33, 0x0a, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x68,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42,
	0x6f, 0x6f, 0x74, 0x68, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x73, 0x22, 0xdb, 0x03, 0x0a,
	0x0a, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x61, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0xe7, 0xbc, 0x96,
	0xe7, 0xa0, 0x81, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41,
	0x0c, 0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c,
	0x6f, 0x67, 0x6f, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x42, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x9b, 0xe5,
	0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x80, 0xbb,
	0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd4, 0x06, 0x0a, 0x0c, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x32, 0x0a, 0x0b, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0xe7, 0xbc, 0x96,
	0xe7, 0xa0, 0x81, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x26, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x49, 0x44, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41,
	0x0c, 0x2a, 0x0a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04, 0x6c,
	0x6f, 0x67, 0x6f, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x42, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a,
	0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x9b, 0xe5,
	0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x80, 0xbb,
	0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x90, 0x8d, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x89, 0x8b, 0xe6,
	0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x3e, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8,
	0xa1, 0x8c, 0xe4, 0xb8, 0x9a, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12,
	0x3e, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8,
	0xba, 0xab, 0xe4, 0xbb, 0xbd, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12,
	0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x1d, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0x81, 0x8c, 0xe4, 0xbd, 0x8d,
	0x52, 0x03, 0x6a, 0x6f, 0x62, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16,
	0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x3b, 0xe5, 0x8d, 0x95,
	0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0x7f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x22, 0x92, 0x41, 0x1f, 0x2a, 0x1d, 0xe5, 0x88,
	0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f,
	0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x2f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe9, 0xa1, 0xb5, 0xe6,
	0x95, 0xb0, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x31, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0x43, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x39, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x7e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x22, 0x57, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70,
	0x6f, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x09,
	0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x6f,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x2e, 0x0a, 0x13, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61,
	0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x56,
	0x0a, 0x11, 0x46, 0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x22, 0x81, 0x03, 0x0a, 0x0f, 0x46, 0x61, 0x63, 0x65, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x65, 0x64, 0x46, 0x61, 0x63, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x66,
	0x61, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x63, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x46,
	0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c,
	0x12, 0x2e, 0x0a, 0x13, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e,
	0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c,
	0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x6d,
	0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x2a, 0x46, 0x0a, 0x0d, 0x45, 0x78, 0x70, 0x6f, 0x52,
	0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x78, 0x70, 0x6f,
	0x52, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x6f, 0x52,
	0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x68, 0x61, 0x74, 0x10, 0x01, 0x2a,
	0x6d, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x12, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x78,
	0x70, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x03, 0x2a, 0x6b,
	0x0a, 0x19, 0x45, 0x78, 0x70, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0c, 0x0a, 0x08, 0x53,
	0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x73, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x72, 0x6f,
	0x66, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x73, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x73, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4e,
	0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x65, 0x64, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x45,
	0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x10, 0x04, 0x2a, 0x8d, 0x01, 0x0a, 0x0c,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x14,
	0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45,
	0x56, 0x49, 0x45, 0x57, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x04, 0x32, 0xf1, 0x1d, 0x0a, 0x07,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x79, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a,
	0x12, 0x75, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x78, 0x70, 0x6f, 0x12,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x78, 0x70, 0x6f, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x34, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x12,
	0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0xbc, 0x80, 0xe5, 0xb1, 0x8f, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x8f, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72,
	0x45, 0x78, 0x70, 0x6f, 0x52, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6f,
	0x52, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x45,
	0x78, 0x70, 0x6f, 0x52, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x92,
	0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x12, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x9d, 0x83, 0xe7, 0x9b, 0x8a, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x12, 0x66, 0x0a, 0x07, 0x45, 0x78, 0x70,
	0x6f, 0x54, 0x61, 0x62, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x54, 0x61, 0x62,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2a, 0x92, 0x41, 0x13, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0x12, 0x09, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x74, 0x61, 0x62, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0e, 0x12, 0x0c, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x74, 0x61,
	0x62, 0x12, 0x74, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x7a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x22, 0x30, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0c,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0xa6, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12,
	0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x8a, 0xa5,
	0xe5, 0x90, 0x8d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa7, 0x01, 0x0a,
	0x15, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x69,
	0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69,
	0x74, 0x6f, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46,
	0x92, 0x41, 0x19, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0f, 0xe5, 0x8f, 0x82,
	0xe5, 0xb1, 0x95, 0xe5, 0x95, 0x86, 0xe6, 0x8a, 0xa5, 0xe5, 0x90, 0x8d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x90, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x70, 0x6f, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x35, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12,
	0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe4, 0xba, 0x92, 0xe5, 0x8a, 0xa8, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa3, 0x01, 0x0a, 0x16, 0x45, 0x78,
	0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x49, 0x92, 0x41, 0x22, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0x12, 0x18, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe4, 0xba, 0x92, 0xe5, 0x8a, 0xa8, 0xe9,
	0xa2, 0x84, 0xe5, 0x91, 0x8a, 0xe7, 0x9f, 0xad, 0xe8, 0xaf, 0xad, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12,
	0xa5, 0x01, 0x0a, 0x13, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x12, 0xe5, 0x8f, 0x91, 0xe8, 0xa1, 0xa8, 0xe5, 0xb1, 0x95, 0xe4,
	0xbc, 0x9a, 0xe4, 0xba, 0x92, 0xe5, 0x8a, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01,
	0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x84, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x32, 0x92, 0x41, 0x16, 0x0a,
	0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8,
	0xae, 0xae, 0xe7, 0xa8, 0x8b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x78,
	0x0a, 0x09, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x47, 0x75, 0x69,
	0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0xb1,
	0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8c, 0x87, 0xe5,
	0x8d, 0x97, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x2f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x0b, 0x45, 0x78, 0x70,
	0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x12, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x90, 0x88,
	0xe4, 0xbd, 0x9c, 0xe4, 0xbc, 0x99, 0xe4, 0xbc, 0xb4, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12,
	0x10, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x12, 0x78, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x6f, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x92, 0x41, 0x16, 0x0a,
	0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe8,
	0xaf, 0x9d, 0xe9, 0xa2, 0x98, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x89, 0x01, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0x12, 0x0c, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe5, 0x98, 0x89, 0xe5, 0xae, 0xbe, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x9c, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53,
	0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x70,
	0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0x12, 0x12, 0xe6, 0xbc, 0x94, 0xe8, 0xae, 0xb2, 0xe5, 0x98, 0x89, 0xe5, 0xae,
	0xbe, 0xe4, 0xb8, 0xbb, 0xe9, 0xa1, 0xb5, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x2f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x91, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x64, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x39, 0x92, 0x41, 0x19, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0f, 0xe8, 0xa7,
	0x82, 0xe5, 0xb1, 0x95, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x74, 0x61, 0x62, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x61, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x74, 0x61, 0x62, 0x12, 0x90, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3d,
	0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x12, 0xe8, 0xa7, 0x82,
	0xe5, 0xb1, 0x95, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x96, 0x01,
	0x0a, 0x0d, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3b, 0x92, 0x41, 0x19, 0x0a, 0x06,
	0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x0f, 0xe5, 0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5, 0x95,
	0x86, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f,
	0x72, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xbd, 0x01, 0x0a, 0x15, 0x45, 0x78, 0x68, 0x69, 0x62,
	0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x65, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4a, 0x92, 0x41, 0x1f, 0x0a,
	0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0x12, 0x15, 0xe5, 0x8f, 0x82, 0xe5, 0xb1, 0x95, 0xe5,
	0x95, 0x86, 0xe5, 0x91, 0x98, 0xe5, 0xb7, 0xa5, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x65,
	0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xab, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x6f, 0x6f, 0x74, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x6f, 0x6f, 0x74, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x68,
	0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x6f, 0x6f, 0x74, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0xb1, 0x95, 0xe4, 0xbc,
	0x9a, 0x12, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbd, 0x8d, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f,
	0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x62, 0x6f, 0x6f, 0x74, 0x68, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x0a, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe7, 0xa5,
	0xa8, 0xe5, 0xa4, 0xb9, 0x12, 0x0c, 0xe7, 0xa5, 0xa8, 0xe5, 0xa4, 0xb9, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x32, 0x92,
	0x41, 0x16, 0x0a, 0x06, 0xe7, 0xa5, 0xa8, 0xe5, 0xa4, 0xb9, 0x12, 0x0c, 0xe7, 0xa5, 0xa8, 0xe5,
	0xa4, 0xb9, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0xce, 0x01, 0x0a, 0x0a, 0x46, 0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x81,
	0x01, 0x92, 0x41, 0x60, 0x0a, 0x0c, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8, 0xe8, 0xaf, 0x86, 0xe5,
	0x88, 0xab, 0x12, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xba, 0xba, 0xe8, 0x84, 0xb8,
	0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0x1a, 0x3c, 0xe5, 0x89, 0x8d, 0xe5, 0x8f, 0xb0, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0x87, 0xaa, 0xe5, 0x8a, 0xa9, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3,
	0xef, 0xbc, 0x8c, 0xe4, 0xb8, 0x8a, 0xe4, 0xbc, 0xa0, 0xe8, 0x87, 0xaa, 0xe6, 0x8b, 0x8d, 0xe7,
	0x85, 0xa7, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe7, 0x85,
	0xa7, 0xe7, 0x89, 0x87, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f,
	0x76, 0x31, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x2f, 0x66, 0x61, 0x63, 0x65, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x12, 0xca, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x75, 0x92, 0x41, 0x56, 0x0a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x12, 0x18, 0xe5, 0x88, 0x86,
	0xe9, 0xa1, 0xb5, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x1a, 0x2c, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0x49, 0x44, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x78, 0x70, 0x6f, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x42,
	0x10, 0x5a, 0x0e, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_expo_v1_service_proto_rawDescOnce sync.Once
	file_expo_v1_service_proto_rawDescData = file_expo_v1_service_proto_rawDesc
)

func file_expo_v1_service_proto_rawDescGZIP() []byte {
	file_expo_v1_service_proto_rawDescOnce.Do(func() {
		file_expo_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_expo_v1_service_proto_rawDescData)
	})
	return file_expo_v1_service_proto_rawDescData
}

var file_expo_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_expo_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 71)
var file_expo_v1_service_proto_goTypes = []interface{}{
	(ExpoRightType)(0),                      // 0: api.expo.v1.ExpoRightType
	(ExpoStatus)(0),                         // 1: api.expo.v1.ExpoStatus
	(ExpoAudienceGroupCategory)(0),          // 2: api.expo.v1.ExpoAudienceGroupCategory
	(TicketStatus)(0),                       // 3: api.expo.v1.TicketStatus
	(*OpenExpoReply)(nil),                   // 4: api.expo.v1.OpenExpoReply
	(*UserExpoRightRequest)(nil),            // 5: api.expo.v1.UserExpoRightRequest
	(*ExpoRight)(nil),                       // 6: api.expo.v1.ExpoRight
	(*UserExpoRightReply)(nil),              // 7: api.expo.v1.UserExpoRightReply
	(*ExpoTab)(nil),                         // 8: api.expo.v1.ExpoTab
	(*ExpoTabReply)(nil),                    // 9: api.expo.v1.ExpoTabReply
	(*ExpoListRequest)(nil),                 // 10: api.expo.v1.ExpoListRequest
	(*ExpoBase)(nil),                        // 11: api.expo.v1.ExpoBase
	(*ExpoListReply)(nil),                   // 12: api.expo.v1.ExpoListReply
	(*ExpoExhibitor)(nil),                   // 13: api.expo.v1.ExpoExhibitor
	(*ExpoAudienceGroup)(nil),               // 14: api.expo.v1.ExpoAudienceGroup
	(*LiveInfo)(nil),                        // 15: api.expo.v1.LiveInfo
	(*ExpoDetail)(nil),                      // 16: api.expo.v1.ExpoDetail
	(*ExpoDetailRequest)(nil),               // 17: api.expo.v1.ExpoDetailRequest
	(*ExpoUserSignUpRequest)(nil),           // 18: api.expo.v1.ExpoUserSignUpRequest
	(*ExpoUserSignUpReply)(nil),             // 19: api.expo.v1.ExpoUserSignUpReply
	(*ExhibitorSignUpRequest)(nil),          // 20: api.expo.v1.ExhibitorSignUpRequest
	(*ExhibitorSignUpReply)(nil),            // 21: api.expo.v1.ExhibitorSignUpReply
	(*ExpoInteractionRequest)(nil),          // 22: api.expo.v1.ExpoInteractionRequest
	(*ExpoInteraction)(nil),                 // 23: api.expo.v1.ExpoInteraction
	(*ExpoInteractionReply)(nil),            // 24: api.expo.v1.ExpoInteractionReply
	(*ExpoInteractionPreviewReply)(nil),     // 25: api.expo.v1.ExpoInteractionPreviewReply
	(*PostExpoInteractionRequest)(nil),      // 26: api.expo.v1.PostExpoInteractionRequest
	(*PostExpoInteractionReply)(nil),        // 27: api.expo.v1.PostExpoInteractionReply
	(*ExpoScheduleRequest)(nil),             // 28: api.expo.v1.ExpoScheduleRequest
	(*ExpoScheduleHallLine)(nil),            // 29: api.expo.v1.ExpoScheduleHallLine
	(*ExpoScheduleHall)(nil),                // 30: api.expo.v1.ExpoScheduleHall
	(*ExpoSchedule)(nil),                    // 31: api.expo.v1.ExpoSchedule
	(*ExpoScheduleReply)(nil),               // 32: api.expo.v1.ExpoScheduleReply
	(*ExpoGuideRequest)(nil),                // 33: api.expo.v1.ExpoGuideRequest
	(*ExpoBooth)(nil),                       // 34: api.expo.v1.ExpoBooth
	(*ExpoGuideReply)(nil),                  // 35: api.expo.v1.ExpoGuideReply
	(*ExpoPartnerRequest)(nil),              // 36: api.expo.v1.ExpoPartnerRequest
	(*ExpoPartnerGroup)(nil),                // 37: api.expo.v1.ExpoPartnerGroup
	(*ExpoPartnerReply)(nil),                // 38: api.expo.v1.ExpoPartnerReply
	(*ExpoTopicRequest)(nil),                // 39: api.expo.v1.ExpoTopicRequest
	(*ExpoTopicItem)(nil),                   // 40: api.expo.v1.ExpoTopicItem
	(*ExpoTopicReply)(nil),                  // 41: api.expo.v1.ExpoTopicReply
	(*GetSpeakerListRequest)(nil),           // 42: api.expo.v1.GetSpeakerListRequest
	(*ExpoSpeakerSchedule)(nil),             // 43: api.expo.v1.ExpoSpeakerSchedule
	(*ExpoSpeaker)(nil),                     // 44: api.expo.v1.ExpoSpeaker
	(*GetSpeakerListReply)(nil),             // 45: api.expo.v1.GetSpeakerListReply
	(*GetSpeakerDetailRequest)(nil),         // 46: api.expo.v1.GetSpeakerDetailRequest
	(*SocialMedia)(nil),                     // 47: api.expo.v1.SocialMedia
	(*GetSpeakerDetailReply)(nil),           // 48: api.expo.v1.GetSpeakerDetailReply
	(*GetAudienceTabRequest)(nil),           // 49: api.expo.v1.GetAudienceTabRequest
	(*AudienceTab)(nil),                     // 50: api.expo.v1.AudienceTab
	(*GetAudienceTabReply)(nil),             // 51: api.expo.v1.GetAudienceTabReply
	(*GetAudienceRequest)(nil),              // 52: api.expo.v1.GetAudienceRequest
	(*AudienceItem)(nil),                    // 53: api.expo.v1.AudienceItem
	(*GetAudienceReply)(nil),                // 54: api.expo.v1.GetAudienceReply
	(*GetExhibitorListRequest)(nil),         // 55: api.expo.v1.GetExhibitorListRequest
	(*Exhibitor)(nil),                       // 56: api.expo.v1.Exhibitor
	(*GetExhibitorListReply)(nil),           // 57: api.expo.v1.GetExhibitorListReply
	(*GetExhibitorEmployeeListRequest)(nil), // 58: api.expo.v1.GetExhibitorEmployeeListRequest
	(*ExhibitorEmployee)(nil),               // 59: api.expo.v1.ExhibitorEmployee
	(*GetExhibitorEmployeeListReply)(nil),   // 60: api.expo.v1.GetExhibitorEmployeeListReply
	(*GetExhibitorBoothListRequest)(nil),    // 61: api.expo.v1.GetExhibitorBoothListRequest
	(*ExhibitorBooth)(nil),                  // 62: api.expo.v1.ExhibitorBooth
	(*GetExhibitorBoothListReply)(nil),      // 63: api.expo.v1.GetExhibitorBoothListReply
	(*TicketBase)(nil),                      // 64: api.expo.v1.TicketBase
	(*TicketDetail)(nil),                    // 65: api.expo.v1.TicketDetail
	(*GetTicketListRequest)(nil),            // 66: api.expo.v1.GetTicketListRequest
	(*GetTicketListReply)(nil),              // 67: api.expo.v1.GetTicketListReply
	(*GetTicketDetailRequest)(nil),          // 68: api.expo.v1.GetTicketDetailRequest
	(*GetLiveImagesReply)(nil),              // 69: api.expo.v1.GetLiveImagesReply
	(*GetLiveImagesRequest)(nil),            // 70: api.expo.v1.GetLiveImagesRequest
	(*LiveImage)(nil),                       // 71: api.expo.v1.LiveImage
	(*FaceSearchRequest)(nil),               // 72: api.expo.v1.FaceSearchRequest
	(*FaceSearchReply)(nil),                 // 73: api.expo.v1.FaceSearchReply
	(*FaceSearchResultItem)(nil),            // 74: api.expo.v1.FaceSearchResultItem
	(Industry)(0),                           // 75: api.expo.v1.Industry
	(Identity)(0),                           // 76: api.expo.v1.Identity
	(*FaceRect)(nil),                        // 77: api.expo.v1.FaceRect
	(*common.EmptyRequest)(nil),             // 78: common.EmptyRequest
	(*common.HealthyReply)(nil),             // 79: common.HealthyReply
}
var file_expo_v1_service_proto_depIdxs = []int32{
	1,  // 0: api.expo.v1.OpenExpoReply.status:type_name -> api.expo.v1.ExpoStatus
	3,  // 1: api.expo.v1.OpenExpoReply.ticket_status:type_name -> api.expo.v1.TicketStatus
	0,  // 2: api.expo.v1.ExpoRight.type:type_name -> api.expo.v1.ExpoRightType
	6,  // 3: api.expo.v1.UserExpoRightReply.rights:type_name -> api.expo.v1.ExpoRight
	8,  // 4: api.expo.v1.ExpoTab.sub_tabs:type_name -> api.expo.v1.ExpoTab
	8,  // 5: api.expo.v1.ExpoTabReply.tabs:type_name -> api.expo.v1.ExpoTab
	1,  // 6: api.expo.v1.ExpoBase.status:type_name -> api.expo.v1.ExpoStatus
	11, // 7: api.expo.v1.ExpoListReply.expos:type_name -> api.expo.v1.ExpoBase
	2,  // 8: api.expo.v1.ExpoAudienceGroup.category:type_name -> api.expo.v1.ExpoAudienceGroupCategory
	13, // 9: api.expo.v1.ExpoAudienceGroup.exhibitors:type_name -> api.expo.v1.ExpoExhibitor
	1,  // 10: api.expo.v1.ExpoDetail.status:type_name -> api.expo.v1.ExpoStatus
	15, // 11: api.expo.v1.ExpoDetail.lives:type_name -> api.expo.v1.LiveInfo
	14, // 12: api.expo.v1.ExpoDetail.audience_groups:type_name -> api.expo.v1.ExpoAudienceGroup
	75, // 13: api.expo.v1.ExpoUserSignUpRequest.industry:type_name -> api.expo.v1.Industry
	76, // 14: api.expo.v1.ExpoUserSignUpRequest.identity:type_name -> api.expo.v1.Identity
	3,  // 15: api.expo.v1.ExpoUserSignUpReply.status:type_name -> api.expo.v1.TicketStatus
	23, // 16: api.expo.v1.ExpoInteraction.comments:type_name -> api.expo.v1.ExpoInteraction
	23, // 17: api.expo.v1.ExpoInteractionReply.comments:type_name -> api.expo.v1.ExpoInteraction
	29, // 18: api.expo.v1.ExpoScheduleHall.lines:type_name -> api.expo.v1.ExpoScheduleHallLine
	30, // 19: api.expo.v1.ExpoSchedule.halls:type_name -> api.expo.v1.ExpoScheduleHall
	31, // 20: api.expo.v1.ExpoScheduleReply.schedules:type_name -> api.expo.v1.ExpoSchedule
	34, // 21: api.expo.v1.ExpoGuideReply.booth:type_name -> api.expo.v1.ExpoBooth
	37, // 22: api.expo.v1.ExpoPartnerReply.groups:type_name -> api.expo.v1.ExpoPartnerGroup
	40, // 23: api.expo.v1.ExpoTopicReply.topics:type_name -> api.expo.v1.ExpoTopicItem
	44, // 24: api.expo.v1.ExpoSpeakerSchedule.speakers:type_name -> api.expo.v1.ExpoSpeaker
	43, // 25: api.expo.v1.GetSpeakerListReply.schedule:type_name -> api.expo.v1.ExpoSpeakerSchedule
	47, // 26: api.expo.v1.GetSpeakerDetailReply.social_media:type_name -> api.expo.v1.SocialMedia
	43, // 27: api.expo.v1.GetSpeakerDetailReply.schedule:type_name -> api.expo.v1.ExpoSpeakerSchedule
	50, // 28: api.expo.v1.GetAudienceTabReply.tabs:type_name -> api.expo.v1.AudienceTab
	76, // 29: api.expo.v1.AudienceItem.identity:type_name -> api.expo.v1.Identity
	53, // 30: api.expo.v1.GetAudienceReply.items:type_name -> api.expo.v1.AudienceItem
	56, // 31: api.expo.v1.GetExhibitorListReply.items:type_name -> api.expo.v1.Exhibitor
	59, // 32: api.expo.v1.GetExhibitorEmployeeListReply.employees:type_name -> api.expo.v1.ExhibitorEmployee
	62, // 33: api.expo.v1.GetExhibitorBoothListReply.booths:type_name -> api.expo.v1.ExhibitorBooth
	3,  // 34: api.expo.v1.TicketBase.status:type_name -> api.expo.v1.TicketStatus
	3,  // 35: api.expo.v1.TicketDetail.status:type_name -> api.expo.v1.TicketStatus
	75, // 36: api.expo.v1.TicketDetail.industry:type_name -> api.expo.v1.Industry
	76, // 37: api.expo.v1.TicketDetail.identity:type_name -> api.expo.v1.Identity
	64, // 38: api.expo.v1.GetTicketListReply.items:type_name -> api.expo.v1.TicketBase
	71, // 39: api.expo.v1.GetLiveImagesReply.list:type_name -> api.expo.v1.LiveImage
	74, // 40: api.expo.v1.FaceSearchReply.results:type_name -> api.expo.v1.FaceSearchResultItem
	77, // 41: api.expo.v1.FaceSearchReply.search_face_rect:type_name -> api.expo.v1.FaceRect
	78, // 42: api.expo.v1.Service.Healthy:input_type -> common.EmptyRequest
	78, // 43: api.expo.v1.Service.GetOpenExpo:input_type -> common.EmptyRequest
	5,  // 44: api.expo.v1.Service.UserExpoRight:input_type -> api.expo.v1.UserExpoRightRequest
	78, // 45: api.expo.v1.Service.ExpoTab:input_type -> common.EmptyRequest
	10, // 46: api.expo.v1.Service.ExpoList:input_type -> api.expo.v1.ExpoListRequest
	17, // 47: api.expo.v1.Service.GetExpoDetail:input_type -> api.expo.v1.ExpoDetailRequest
	18, // 48: api.expo.v1.Service.ExpoUserRegistration:input_type -> api.expo.v1.ExpoUserSignUpRequest
	20, // 49: api.expo.v1.Service.ExhibitorRegistration:input_type -> api.expo.v1.ExhibitorSignUpRequest
	22, // 50: api.expo.v1.Service.ExpoInteraction:input_type -> api.expo.v1.ExpoInteractionRequest
	78, // 51: api.expo.v1.Service.ExpoInteractionPreview:input_type -> common.EmptyRequest
	26, // 52: api.expo.v1.Service.PostExpoInteraction:input_type -> api.expo.v1.PostExpoInteractionRequest
	28, // 53: api.expo.v1.Service.ExpoSchedule:input_type -> api.expo.v1.ExpoScheduleRequest
	33, // 54: api.expo.v1.Service.ExpoGuide:input_type -> api.expo.v1.ExpoGuideRequest
	36, // 55: api.expo.v1.Service.ExpoPartner:input_type -> api.expo.v1.ExpoPartnerRequest
	39, // 56: api.expo.v1.Service.ExpoTopic:input_type -> api.expo.v1.ExpoTopicRequest
	42, // 57: api.expo.v1.Service.GetSpeakerList:input_type -> api.expo.v1.GetSpeakerListRequest
	46, // 58: api.expo.v1.Service.GetSpeakerDetail:input_type -> api.expo.v1.GetSpeakerDetailRequest
	49, // 59: api.expo.v1.Service.GetAudienceTab:input_type -> api.expo.v1.GetAudienceTabRequest
	52, // 60: api.expo.v1.Service.GetAudienceList:input_type -> api.expo.v1.GetAudienceRequest
	55, // 61: api.expo.v1.Service.ExhibitorList:input_type -> api.expo.v1.GetExhibitorListRequest
	58, // 62: api.expo.v1.Service.ExhibitorEmployeeList:input_type -> api.expo.v1.GetExhibitorEmployeeListRequest
	61, // 63: api.expo.v1.Service.GetExhibitorBoothList:input_type -> api.expo.v1.GetExhibitorBoothListRequest
	66, // 64: api.expo.v1.Service.TicketList:input_type -> api.expo.v1.GetTicketListRequest
	68, // 65: api.expo.v1.Service.GetTicketDetail:input_type -> api.expo.v1.GetTicketDetailRequest
	72, // 66: api.expo.v1.Service.FaceSearch:input_type -> api.expo.v1.FaceSearchRequest
	70, // 67: api.expo.v1.Service.GetLiveImages:input_type -> api.expo.v1.GetLiveImagesRequest
	79, // 68: api.expo.v1.Service.Healthy:output_type -> common.HealthyReply
	4,  // 69: api.expo.v1.Service.GetOpenExpo:output_type -> api.expo.v1.OpenExpoReply
	7,  // 70: api.expo.v1.Service.UserExpoRight:output_type -> api.expo.v1.UserExpoRightReply
	9,  // 71: api.expo.v1.Service.ExpoTab:output_type -> api.expo.v1.ExpoTabReply
	12, // 72: api.expo.v1.Service.ExpoList:output_type -> api.expo.v1.ExpoListReply
	16, // 73: api.expo.v1.Service.GetExpoDetail:output_type -> api.expo.v1.ExpoDetail
	19, // 74: api.expo.v1.Service.ExpoUserRegistration:output_type -> api.expo.v1.ExpoUserSignUpReply
	21, // 75: api.expo.v1.Service.ExhibitorRegistration:output_type -> api.expo.v1.ExhibitorSignUpReply
	24, // 76: api.expo.v1.Service.ExpoInteraction:output_type -> api.expo.v1.ExpoInteractionReply
	25, // 77: api.expo.v1.Service.ExpoInteractionPreview:output_type -> api.expo.v1.ExpoInteractionPreviewReply
	27, // 78: api.expo.v1.Service.PostExpoInteraction:output_type -> api.expo.v1.PostExpoInteractionReply
	32, // 79: api.expo.v1.Service.ExpoSchedule:output_type -> api.expo.v1.ExpoScheduleReply
	35, // 80: api.expo.v1.Service.ExpoGuide:output_type -> api.expo.v1.ExpoGuideReply
	38, // 81: api.expo.v1.Service.ExpoPartner:output_type -> api.expo.v1.ExpoPartnerReply
	41, // 82: api.expo.v1.Service.ExpoTopic:output_type -> api.expo.v1.ExpoTopicReply
	45, // 83: api.expo.v1.Service.GetSpeakerList:output_type -> api.expo.v1.GetSpeakerListReply
	48, // 84: api.expo.v1.Service.GetSpeakerDetail:output_type -> api.expo.v1.GetSpeakerDetailReply
	51, // 85: api.expo.v1.Service.GetAudienceTab:output_type -> api.expo.v1.GetAudienceTabReply
	54, // 86: api.expo.v1.Service.GetAudienceList:output_type -> api.expo.v1.GetAudienceReply
	57, // 87: api.expo.v1.Service.ExhibitorList:output_type -> api.expo.v1.GetExhibitorListReply
	60, // 88: api.expo.v1.Service.ExhibitorEmployeeList:output_type -> api.expo.v1.GetExhibitorEmployeeListReply
	63, // 89: api.expo.v1.Service.GetExhibitorBoothList:output_type -> api.expo.v1.GetExhibitorBoothListReply
	67, // 90: api.expo.v1.Service.TicketList:output_type -> api.expo.v1.GetTicketListReply
	65, // 91: api.expo.v1.Service.GetTicketDetail:output_type -> api.expo.v1.TicketDetail
	73, // 92: api.expo.v1.Service.FaceSearch:output_type -> api.expo.v1.FaceSearchReply
	69, // 93: api.expo.v1.Service.GetLiveImages:output_type -> api.expo.v1.GetLiveImagesReply
	68, // [68:94] is the sub-list for method output_type
	42, // [42:68] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_expo_v1_service_proto_init() }
func file_expo_v1_service_proto_init() {
	if File_expo_v1_service_proto != nil {
		return
	}
	file_expo_v1_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_expo_v1_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenExpoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExpoRightRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoRight); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExpoRightReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoTab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoTabReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoExhibitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoAudienceGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoUserSignUpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoUserSignUpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitorSignUpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitorSignUpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoInteractionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoInteraction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoInteractionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoInteractionPreviewReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostExpoInteractionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostExpoInteractionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleHallLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleHall); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoScheduleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoGuideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoBooth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoGuideReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoPartnerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoPartnerGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoPartnerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoTopicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoTopicItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoTopicReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpeakerListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoSpeakerSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpoSpeaker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpeakerListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpeakerDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SocialMedia); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpeakerDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAudienceTabRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceTab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAudienceTabReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAudienceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAudienceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExhibitorListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Exhibitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExhibitorListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExhibitorEmployeeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitorEmployee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExhibitorEmployeeListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExhibitorBoothListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitorBooth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExhibitorBoothListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLiveImagesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLiveImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiveImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceSearchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceSearchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceSearchResultItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_expo_v1_service_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   71,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_expo_v1_service_proto_goTypes,
		DependencyIndexes: file_expo_v1_service_proto_depIdxs,
		EnumInfos:         file_expo_v1_service_proto_enumTypes,
		MessageInfos:      file_expo_v1_service_proto_msgTypes,
	}.Build()
	File_expo_v1_service_proto = out.File
	file_expo_v1_service_proto_rawDesc = nil
	file_expo_v1_service_proto_goTypes = nil
	file_expo_v1_service_proto_depIdxs = nil
}
