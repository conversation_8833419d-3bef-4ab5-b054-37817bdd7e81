// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: expo/v1/service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	common "wiki_user_center/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceExhibitorEmployeeList = "/api.expo.v1.Service/ExhibitorEmployeeList"
const OperationServiceExhibitorList = "/api.expo.v1.Service/ExhibitorList"
const OperationServiceExhibitorRegistration = "/api.expo.v1.Service/ExhibitorRegistration"
const OperationServiceExpoGuide = "/api.expo.v1.Service/ExpoGuide"
const OperationServiceExpoInteraction = "/api.expo.v1.Service/ExpoInteraction"
const OperationServiceExpoInteractionPreview = "/api.expo.v1.Service/ExpoInteractionPreview"
const OperationServiceExpoList = "/api.expo.v1.Service/ExpoList"
const OperationServiceExpoPartner = "/api.expo.v1.Service/ExpoPartner"
const OperationServiceExpoSchedule = "/api.expo.v1.Service/ExpoSchedule"
const OperationServiceExpoTab = "/api.expo.v1.Service/ExpoTab"
const OperationServiceExpoTopic = "/api.expo.v1.Service/ExpoTopic"
const OperationServiceExpoUserRegistration = "/api.expo.v1.Service/ExpoUserRegistration"
const OperationServiceFaceSearch = "/api.expo.v1.Service/FaceSearch"
const OperationServiceGetAudienceList = "/api.expo.v1.Service/GetAudienceList"
const OperationServiceGetAudienceTab = "/api.expo.v1.Service/GetAudienceTab"
const OperationServiceGetExhibitorBoothList = "/api.expo.v1.Service/GetExhibitorBoothList"
const OperationServiceGetExpoDetail = "/api.expo.v1.Service/GetExpoDetail"
const OperationServiceGetLiveImages = "/api.expo.v1.Service/GetLiveImages"
const OperationServiceGetOpenExpo = "/api.expo.v1.Service/GetOpenExpo"
const OperationServiceGetSpeakerDetail = "/api.expo.v1.Service/GetSpeakerDetail"
const OperationServiceGetSpeakerList = "/api.expo.v1.Service/GetSpeakerList"
const OperationServiceGetTicketDetail = "/api.expo.v1.Service/GetTicketDetail"
const OperationServiceHealthy = "/api.expo.v1.Service/Healthy"
const OperationServicePostExpoInteraction = "/api.expo.v1.Service/PostExpoInteraction"
const OperationServiceTicketList = "/api.expo.v1.Service/TicketList"
const OperationServiceUserExpoRight = "/api.expo.v1.Service/UserExpoRight"

type ServiceHTTPServer interface {
	// ExhibitorEmployeeList 参展商员工列表
	ExhibitorEmployeeList(context.Context, *GetExhibitorEmployeeListRequest) (*GetExhibitorEmployeeListReply, error)
	// ExhibitorList 参展商列表
	ExhibitorList(context.Context, *GetExhibitorListRequest) (*GetExhibitorListReply, error)
	// ExhibitorRegistration 展会参展商报名
	ExhibitorRegistration(context.Context, *ExhibitorSignUpRequest) (*ExhibitorSignUpReply, error)
	// ExpoGuide 展会指南
	ExpoGuide(context.Context, *ExpoGuideRequest) (*ExpoGuideReply, error)
	// ExpoInteraction 展会互动
	ExpoInteraction(context.Context, *ExpoInteractionRequest) (*ExpoInteractionReply, error)
	// ExpoInteractionPreview 展会互动预告短语
	ExpoInteractionPreview(context.Context, *common.EmptyRequest) (*ExpoInteractionPreviewReply, error)
	// ExpoList 获取展会列表
	ExpoList(context.Context, *ExpoListRequest) (*ExpoListReply, error)
	// ExpoPartner 获取伙伴
	ExpoPartner(context.Context, *ExpoPartnerRequest) (*ExpoPartnerReply, error)
	// ExpoSchedule 展会议程
	ExpoSchedule(context.Context, *ExpoScheduleRequest) (*ExpoScheduleReply, error)
	// ExpoTab 展会tab
	ExpoTab(context.Context, *common.EmptyRequest) (*ExpoTabReply, error)
	// ExpoTopic 展会话题
	ExpoTopic(context.Context, *ExpoTopicRequest) (*ExpoTopicReply, error)
	// ExpoUserRegistration 展会用户报名
	ExpoUserRegistration(context.Context, *ExpoUserSignUpRequest) (*ExpoUserSignUpReply, error)
	// FaceSearch 用户人脸搜索接口
	FaceSearch(context.Context, *FaceSearchRequest) (*FaceSearchReply, error)
	// GetAudienceList 观展用户列表
	GetAudienceList(context.Context, *GetAudienceRequest) (*GetAudienceReply, error)
	// GetAudienceTab 观展用户tab
	GetAudienceTab(context.Context, *GetAudienceTabRequest) (*GetAudienceTabReply, error)
	// GetExhibitorBoothList 展位列表
	GetExhibitorBoothList(context.Context, *GetExhibitorBoothListRequest) (*GetExhibitorBoothListReply, error)
	// GetExpoDetail 展会详情
	GetExpoDetail(context.Context, *ExpoDetailRequest) (*ExpoDetail, error)
	// GetLiveImages 分页图片直播列表
	GetLiveImages(context.Context, *GetLiveImagesRequest) (*GetLiveImagesReply, error)
	// GetOpenExpo ===========================================================
	// =========================== 展会 ===========================
	// ===========================================================
	// 获取开屏展会
	GetOpenExpo(context.Context, *common.EmptyRequest) (*OpenExpoReply, error)
	// GetSpeakerDetail 演讲嘉宾主页
	GetSpeakerDetail(context.Context, *GetSpeakerDetailRequest) (*GetSpeakerDetailReply, error)
	// GetSpeakerList 演讲嘉宾列表
	GetSpeakerList(context.Context, *GetSpeakerListRequest) (*GetSpeakerListReply, error)
	// GetTicketDetail 票夹详情
	GetTicketDetail(context.Context, *GetTicketDetailRequest) (*TicketDetail, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// PostExpoInteraction 发表展会互动
	PostExpoInteraction(context.Context, *PostExpoInteractionRequest) (*PostExpoInteractionReply, error)
	// TicketList ===========================================================
	// =========================== 票夹 ===========================
	// ===========================================================
	// 票夹列表
	TicketList(context.Context, *GetTicketListRequest) (*GetTicketListReply, error)
	// UserExpoRight 用户展会权益
	UserExpoRight(context.Context, *UserExpoRightRequest) (*UserExpoRightReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy3_HTTP_Handler(srv))
	r.GET("/v1/expo/open", _Service_GetOpenExpo0_HTTP_Handler(srv))
	r.GET("/v1/expo/user_right", _Service_UserExpoRight0_HTTP_Handler(srv))
	r.GET("/v1/expo/tab", _Service_ExpoTab0_HTTP_Handler(srv))
	r.GET("/v1/expo/list", _Service_ExpoList0_HTTP_Handler(srv))
	r.GET("/v1/expo/detail", _Service_GetExpoDetail0_HTTP_Handler(srv))
	r.POST("/v1/expo/audience/registration", _Service_ExpoUserRegistration0_HTTP_Handler(srv))
	r.POST("/v1/expo/exhibitor/registration", _Service_ExhibitorRegistration0_HTTP_Handler(srv))
	r.GET("/v1/expo/interaction", _Service_ExpoInteraction0_HTTP_Handler(srv))
	r.GET("/v1/expo/interaction/preview", _Service_ExpoInteractionPreview0_HTTP_Handler(srv))
	r.POST("/v1/expo/interaction", _Service_PostExpoInteraction0_HTTP_Handler(srv))
	r.GET("/v1/expo/schedule", _Service_ExpoSchedule0_HTTP_Handler(srv))
	r.GET("/v1/expo/guide", _Service_ExpoGuide0_HTTP_Handler(srv))
	r.GET("/v1/expo/partner", _Service_ExpoPartner0_HTTP_Handler(srv))
	r.GET("/v1/expo/topic", _Service_ExpoTopic0_HTTP_Handler(srv))
	r.GET("/v1/expo/speaker", _Service_GetSpeakerList0_HTTP_Handler(srv))
	r.GET("/v1/expo/speaker/detail", _Service_GetSpeakerDetail0_HTTP_Handler(srv))
	r.GET("/v1/expo/audience/tab", _Service_GetAudienceTab0_HTTP_Handler(srv))
	r.GET("/v1/expo/audience/list", _Service_GetAudienceList0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/list", _Service_ExhibitorList0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/employee/list", _Service_ExhibitorEmployeeList0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/booth/list", _Service_GetExhibitorBoothList0_HTTP_Handler(srv))
	r.GET("/v1/ticket/list", _Service_TicketList0_HTTP_Handler(srv))
	r.GET("/v1/ticket/detail", _Service_GetTicketDetail0_HTTP_Handler(srv))
	r.POST("/v1/face/faceSearch", _Service_FaceSearch0_HTTP_Handler(srv))
	r.GET("/v1/expo/images/list", _Service_GetLiveImages0_HTTP_Handler(srv))
}

func _Service_Healthy3_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOpenExpo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOpenExpo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOpenExpo(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OpenExpoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserExpoRight0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserExpoRightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserExpoRight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserExpoRight(ctx, req.(*UserExpoRightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserExpoRightReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoTab(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoList(ctx, req.(*ExpoListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExpoDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExpoDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoDetail(ctx, req.(*ExpoDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoDetail)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoUserRegistration0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoUserSignUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoUserRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoUserRegistration(ctx, req.(*ExpoUserSignUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoUserSignUpReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExhibitorRegistration0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExhibitorSignUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExhibitorRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorRegistration(ctx, req.(*ExhibitorSignUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExhibitorSignUpReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteraction0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteraction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteraction(ctx, req.(*ExpoInteractionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteractionPreview0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteractionPreview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteractionPreview(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionPreviewReply)
		return ctx.Result(200, reply)
	}
}

func _Service_PostExpoInteraction0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PostExpoInteractionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServicePostExpoInteraction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PostExpoInteraction(ctx, req.(*PostExpoInteractionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PostExpoInteractionReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoSchedule0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoSchedule(ctx, req.(*ExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoGuide0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoGuideRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoGuide(ctx, req.(*ExpoGuideRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoGuideReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoPartner0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoPartnerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoPartner(ctx, req.(*ExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoPartnerReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoTopic0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoTopicRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoTopic(ctx, req.(*ExpoTopicRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoTopicReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSpeakerList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSpeakerListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSpeakerList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSpeakerList(ctx, req.(*GetSpeakerListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSpeakerListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSpeakerDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSpeakerDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSpeakerDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSpeakerDetail(ctx, req.(*GetSpeakerDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSpeakerDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAudienceTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAudienceTabRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAudienceTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAudienceTab(ctx, req.(*GetAudienceTabRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAudienceTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAudienceList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAudienceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAudienceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAudienceList(ctx, req.(*GetAudienceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAudienceReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExhibitorList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExhibitorListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExhibitorList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorList(ctx, req.(*GetExhibitorListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExhibitorEmployeeList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExhibitorEmployeeListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExhibitorEmployeeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorEmployeeList(ctx, req.(*GetExhibitorEmployeeListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorEmployeeListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExhibitorBoothList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExhibitorBoothListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExhibitorBoothList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExhibitorBoothList(ctx, req.(*GetExhibitorBoothListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorBoothListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TicketList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTicketListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTicketList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TicketList(ctx, req.(*GetTicketListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTicketListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetTicketDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTicketDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetTicketDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTicketDetail(ctx, req.(*GetTicketDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TicketDetail)
		return ctx.Result(200, reply)
	}
}

func _Service_FaceSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FaceSearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFaceSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FaceSearch(ctx, req.(*FaceSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FaceSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLiveImages0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLiveImagesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLiveImages)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLiveImages(ctx, req.(*GetLiveImagesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLiveImagesReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	ExhibitorEmployeeList(ctx context.Context, req *GetExhibitorEmployeeListRequest, opts ...http.CallOption) (rsp *GetExhibitorEmployeeListReply, err error)
	ExhibitorList(ctx context.Context, req *GetExhibitorListRequest, opts ...http.CallOption) (rsp *GetExhibitorListReply, err error)
	ExhibitorRegistration(ctx context.Context, req *ExhibitorSignUpRequest, opts ...http.CallOption) (rsp *ExhibitorSignUpReply, err error)
	ExpoGuide(ctx context.Context, req *ExpoGuideRequest, opts ...http.CallOption) (rsp *ExpoGuideReply, err error)
	ExpoInteraction(ctx context.Context, req *ExpoInteractionRequest, opts ...http.CallOption) (rsp *ExpoInteractionReply, err error)
	ExpoInteractionPreview(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *ExpoInteractionPreviewReply, err error)
	ExpoList(ctx context.Context, req *ExpoListRequest, opts ...http.CallOption) (rsp *ExpoListReply, err error)
	ExpoPartner(ctx context.Context, req *ExpoPartnerRequest, opts ...http.CallOption) (rsp *ExpoPartnerReply, err error)
	ExpoSchedule(ctx context.Context, req *ExpoScheduleRequest, opts ...http.CallOption) (rsp *ExpoScheduleReply, err error)
	ExpoTab(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *ExpoTabReply, err error)
	ExpoTopic(ctx context.Context, req *ExpoTopicRequest, opts ...http.CallOption) (rsp *ExpoTopicReply, err error)
	ExpoUserRegistration(ctx context.Context, req *ExpoUserSignUpRequest, opts ...http.CallOption) (rsp *ExpoUserSignUpReply, err error)
	FaceSearch(ctx context.Context, req *FaceSearchRequest, opts ...http.CallOption) (rsp *FaceSearchReply, err error)
	GetAudienceList(ctx context.Context, req *GetAudienceRequest, opts ...http.CallOption) (rsp *GetAudienceReply, err error)
	GetAudienceTab(ctx context.Context, req *GetAudienceTabRequest, opts ...http.CallOption) (rsp *GetAudienceTabReply, err error)
	GetExhibitorBoothList(ctx context.Context, req *GetExhibitorBoothListRequest, opts ...http.CallOption) (rsp *GetExhibitorBoothListReply, err error)
	GetExpoDetail(ctx context.Context, req *ExpoDetailRequest, opts ...http.CallOption) (rsp *ExpoDetail, err error)
	GetLiveImages(ctx context.Context, req *GetLiveImagesRequest, opts ...http.CallOption) (rsp *GetLiveImagesReply, err error)
	GetOpenExpo(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *OpenExpoReply, err error)
	GetSpeakerDetail(ctx context.Context, req *GetSpeakerDetailRequest, opts ...http.CallOption) (rsp *GetSpeakerDetailReply, err error)
	GetSpeakerList(ctx context.Context, req *GetSpeakerListRequest, opts ...http.CallOption) (rsp *GetSpeakerListReply, err error)
	GetTicketDetail(ctx context.Context, req *GetTicketDetailRequest, opts ...http.CallOption) (rsp *TicketDetail, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	PostExpoInteraction(ctx context.Context, req *PostExpoInteractionRequest, opts ...http.CallOption) (rsp *PostExpoInteractionReply, err error)
	TicketList(ctx context.Context, req *GetTicketListRequest, opts ...http.CallOption) (rsp *GetTicketListReply, err error)
	UserExpoRight(ctx context.Context, req *UserExpoRightRequest, opts ...http.CallOption) (rsp *UserExpoRightReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) ExhibitorEmployeeList(ctx context.Context, in *GetExhibitorEmployeeListRequest, opts ...http.CallOption) (*GetExhibitorEmployeeListReply, error) {
	var out GetExhibitorEmployeeListReply
	pattern := "/v1/expo/exhibitor/employee/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExhibitorEmployeeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExhibitorList(ctx context.Context, in *GetExhibitorListRequest, opts ...http.CallOption) (*GetExhibitorListReply, error) {
	var out GetExhibitorListReply
	pattern := "/v1/expo/exhibitor/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExhibitorList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExhibitorRegistration(ctx context.Context, in *ExhibitorSignUpRequest, opts ...http.CallOption) (*ExhibitorSignUpReply, error) {
	var out ExhibitorSignUpReply
	pattern := "/v1/expo/exhibitor/registration"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExhibitorRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoGuide(ctx context.Context, in *ExpoGuideRequest, opts ...http.CallOption) (*ExpoGuideReply, error) {
	var out ExpoGuideReply
	pattern := "/v1/expo/guide"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoInteraction(ctx context.Context, in *ExpoInteractionRequest, opts ...http.CallOption) (*ExpoInteractionReply, error) {
	var out ExpoInteractionReply
	pattern := "/v1/expo/interaction"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoInteraction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoInteractionPreview(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*ExpoInteractionPreviewReply, error) {
	var out ExpoInteractionPreviewReply
	pattern := "/v1/expo/interaction/preview"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoInteractionPreview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoList(ctx context.Context, in *ExpoListRequest, opts ...http.CallOption) (*ExpoListReply, error) {
	var out ExpoListReply
	pattern := "/v1/expo/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoPartner(ctx context.Context, in *ExpoPartnerRequest, opts ...http.CallOption) (*ExpoPartnerReply, error) {
	var out ExpoPartnerReply
	pattern := "/v1/expo/partner"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoSchedule(ctx context.Context, in *ExpoScheduleRequest, opts ...http.CallOption) (*ExpoScheduleReply, error) {
	var out ExpoScheduleReply
	pattern := "/v1/expo/schedule"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoTab(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*ExpoTabReply, error) {
	var out ExpoTabReply
	pattern := "/v1/expo/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoTopic(ctx context.Context, in *ExpoTopicRequest, opts ...http.CallOption) (*ExpoTopicReply, error) {
	var out ExpoTopicReply
	pattern := "/v1/expo/topic"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) ExpoUserRegistration(ctx context.Context, in *ExpoUserSignUpRequest, opts ...http.CallOption) (*ExpoUserSignUpReply, error) {
	var out ExpoUserSignUpReply
	pattern := "/v1/expo/audience/registration"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExpoUserRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) FaceSearch(ctx context.Context, in *FaceSearchRequest, opts ...http.CallOption) (*FaceSearchReply, error) {
	var out FaceSearchReply
	pattern := "/v1/face/faceSearch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceFaceSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetAudienceList(ctx context.Context, in *GetAudienceRequest, opts ...http.CallOption) (*GetAudienceReply, error) {
	var out GetAudienceReply
	pattern := "/v1/expo/audience/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAudienceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetAudienceTab(ctx context.Context, in *GetAudienceTabRequest, opts ...http.CallOption) (*GetAudienceTabReply, error) {
	var out GetAudienceTabReply
	pattern := "/v1/expo/audience/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAudienceTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetExhibitorBoothList(ctx context.Context, in *GetExhibitorBoothListRequest, opts ...http.CallOption) (*GetExhibitorBoothListReply, error) {
	var out GetExhibitorBoothListReply
	pattern := "/v1/expo/exhibitor/booth/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExhibitorBoothList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetExpoDetail(ctx context.Context, in *ExpoDetailRequest, opts ...http.CallOption) (*ExpoDetail, error) {
	var out ExpoDetail
	pattern := "/v1/expo/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExpoDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetLiveImages(ctx context.Context, in *GetLiveImagesRequest, opts ...http.CallOption) (*GetLiveImagesReply, error) {
	var out GetLiveImagesReply
	pattern := "/v1/expo/images/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLiveImages))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetOpenExpo(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*OpenExpoReply, error) {
	var out OpenExpoReply
	pattern := "/v1/expo/open"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOpenExpo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetSpeakerDetail(ctx context.Context, in *GetSpeakerDetailRequest, opts ...http.CallOption) (*GetSpeakerDetailReply, error) {
	var out GetSpeakerDetailReply
	pattern := "/v1/expo/speaker/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSpeakerDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetSpeakerList(ctx context.Context, in *GetSpeakerListRequest, opts ...http.CallOption) (*GetSpeakerListReply, error) {
	var out GetSpeakerListReply
	pattern := "/v1/expo/speaker"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSpeakerList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetTicketDetail(ctx context.Context, in *GetTicketDetailRequest, opts ...http.CallOption) (*TicketDetail, error) {
	var out TicketDetail
	pattern := "/v1/ticket/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetTicketDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) PostExpoInteraction(ctx context.Context, in *PostExpoInteractionRequest, opts ...http.CallOption) (*PostExpoInteractionReply, error) {
	var out PostExpoInteractionReply
	pattern := "/v1/expo/interaction"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServicePostExpoInteraction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) TicketList(ctx context.Context, in *GetTicketListRequest, opts ...http.CallOption) (*GetTicketListReply, error) {
	var out GetTicketListReply
	pattern := "/v1/ticket/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceTicketList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) UserExpoRight(ctx context.Context, in *UserExpoRightRequest, opts ...http.CallOption) (*UserExpoRightReply, error) {
	var out UserExpoRightReply
	pattern := "/v1/expo/user_right"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserExpoRight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
