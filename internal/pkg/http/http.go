package http

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"sync"
	"time"
)

var clientPool = sync.Pool{
	New: func() interface{} {
		return http.Client{Timeout: 20 * time.Second}
	},
}

func NewHttpGetRequest(url, country, language, basicData string) (error, string) {
	if url == "" || country == "" || language == "" || basicData == "" {
		return errors.New("params can not be null"), ""
	}
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return err, ""
	}
	req.Header.Set("CountryCode", country)
	req.Header.Set("LanguageCode", language)
	req.Header.Set("BasicData", basicData)

	client := clientPool.Get()
	defer clientPool.Put(client) //对象回收

	var result string
	if c, ok := client.(http.Client); ok {
		resp, err := c.Do(req)
		if err != nil {
			return err, ""
		}
		if resp.StatusCode != http.StatusOK {
			s := fmt.Sprintf("request url:【%s】,country:【%s】,language:【%s】,basicData:【%s】 return error statusCode:%d", url, country, language, basicData, resp.StatusCode)
			return errors.New(s), ""
		}
		defer resp.Body.Close()
		content, err := ioutil.ReadAll(resp.Body)
		if content != nil && len(content) > 0 {
			result = string(content)
		}
	}

	return nil, result
}

func NewHttpPostRequest(url string, data interface{}, header ...map[string]string) (string, error) {
	if url == "" || data == nil {
		return "", errors.New("params can not be null")
	}
	d, _ := json.Marshal(data)
	req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(d))
	if err != nil {
		return "", err
	}

	header = append(header, map[string]string{"Content-Type": "application/json; charset=utf-8"})
	for _, v := range header {
		for k, sv := range v {
			req.Header.Set(k, sv)
		}
	}

	client := clientPool.Get()
	defer clientPool.Put(client) //对象回收

	var result string
	if c, ok := client.(http.Client); ok {
		resp, err := c.Do(req)
		if err != nil {
			return "", err
		}

		defer resp.Body.Close()
		content, err := ioutil.ReadAll(resp.Body)
		if content != nil && len(content) > 0 {
			result = string(content)
		}

		if resp.StatusCode != http.StatusOK {
			return "", errors.New(fmt.Sprintf("return error statusCode:%d,Result:%s", resp.StatusCode, result))
		}

	}
	return result, nil

}
