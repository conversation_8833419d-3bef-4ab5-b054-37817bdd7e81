package log

import (
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
)

var _ log.Logger = (*WikiFxLogger)(nil)

type options struct {
	timeFmt    string
	path       string
	age        int
	lineEnding string
	console    bool // 是否输出到控制台
}

type Option func(o *options)

func WithTimeLayout(layout string) Option {
	return func(o *options) {
		o.timeFmt = layout
	}
}

func WithFilePath(f string) Option {
	return func(o *options) {
		o.path = f
	}
}

func WithLogAge(age int) Option {
	return func(o *options) {
		o.age = age
	}
}

func WithConsole() Option {
	return func(o *options) {
		o.console = true
	}
}

type WikiFxLogger struct {
	log    *zap.Logger
	msgKey string
}

func NewWikiFxLogger(opts ...Option) *WikiFxLogger {
	var (
		d            = getDefault()
		coreArr      []zapcore.Core
		c            = zap.NewProductionEncoderConfig()
		highPriority = zap.LevelEnablerFunc(func(lev zapcore.Level) bool { //error级别
			return lev >= zap.ErrorLevel
		})
		lowPriority = zap.LevelEnablerFunc(func(lev zapcore.Level) bool { //info和debug级别,debug级别是最低的
			return lev < zap.ErrorLevel && lev >= zap.DebugLevel
		})
	)

	for _, f := range opts {
		f(d)
	}

	if d.console {
		return &WikiFxLogger{
			log: zap.New(zapcore.NewCore(
				zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig()),
				zapcore.AddSync(os.Stdout),
				zapcore.DebugLevel,
			)),
			msgKey: "msg",
		}
	}

	//NewJSONEncoder()输出json格式，NewConsoleEncoder()输出普通文本格式
	c.EncodeTime = zapcore.TimeEncoderOfLayout(d.timeFmt) //指定时间格式
	c.EncodeLevel = zapcore.CapitalLevelEncoder           //按级别显示不同颜色，不需要的话取值zapcore.CapitalLevelEncoder就可以了
	c.ConsoleSeparator = " "
	c.LineEnding = d.lineEnding
	encoder := zapcore.NewConsoleEncoder(c)

	//info文件writeSyncer
	infoFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename:   fmt.Sprintf("%sinfo.log", d.path), //日志文件存放目录，如果文件夹不存在会自动创建
		MaxSize:    2,                                 //文件大小限制,单位MB
		MaxBackups: 100,                               //最大保留日志文件数量
		MaxAge:     d.age,                             //日志文件保留天数
		Compress:   false,                             //是否压缩处理
	})

	infoFileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(infoFileWriteSyncer), lowPriority) //第三个及之后的参数为写入文件的日志级别,ErrorLevel模式只记录error级别的日志
	//error文件writeSyncer
	errorFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename:   fmt.Sprintf("%serror.log", d.path), //日志文件存放目录
		MaxSize:    1,                                  //文件大小限制,单位MB
		MaxBackups: 5,                                  //最大保留日志文件数量
		MaxAge:     d.age,                              //日志文件保留天数
		Compress:   false,                              //是否压缩处理
	})
	errorFileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(errorFileWriteSyncer), highPriority) //第三个及之后的参数为写入文件的日志级别,ErrorLevel模式只记录error级别的日志

	coreArr = append(coreArr, infoFileCore, errorFileCore)

	return &WikiFxLogger{
		log: zap.New(zapcore.NewTee(coreArr...)),
	}
}

func (l *WikiFxLogger) Log(level log.Level, keyVal ...interface{}) error {

	var (
		msg    = ""
		keyLen = len(keyVal)
	)
	if keyLen == 0 || keyLen%2 != 0 {
		l.log.Warn(fmt.Sprint("Keyvalues must appear in pairs: ", keyVal))
		return nil
	}

	data := make([]zap.Field, 0, (keyLen/2)+1)
	for i := 0; i < keyLen; i += 2 {
		if keyVal[i].(string) == l.msgKey {
			msg, _ = keyVal[i+1].(string)
			continue
		}
		data = append(data, zap.Any(fmt.Sprint(keyVal[i]), keyVal[i+1]))
	}

	switch level {
	case log.LevelDebug:
		l.log.Debug(msg, data...)
	case log.LevelInfo:
		l.log.Info(msg, data...)
	case log.LevelWarn:
		l.log.Warn(msg, data...)
	case log.LevelError:
		l.log.Error(msg, data...)
	case log.LevelFatal:
		l.log.Fatal(msg, data...)
	}
	return nil
}

func (l *WikiFxLogger) Sync() error {
	return l.log.Sync()
}

func (l *WikiFxLogger) Close() error {
	return l.Sync()
}

func getDefault() *options {
	return &options{
		timeFmt:    "2006-01-02 15:04:05,000",
		age:        30,
		lineEnding: "\n\n",
	}
}
