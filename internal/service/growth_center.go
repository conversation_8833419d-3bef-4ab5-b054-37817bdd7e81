// Package service 用户成长中心服务层实现
//
// 用户成长中心是一个完整的用户身份管理和等级升级系统，支持以下核心功能：
//
// 身份体系：
// - 投资者(INVESTOR)：支持资产验证、实名认证、账户绑定等任务
// - KOL：支持粉丝数量、内容发布等社交影响力任务
// - 服务商(SERVICE_PROVIDER)：支持企业员工、合作数量等企业发展任务
// - 交易商(TRADER)：支持企业员工、合作数量等交易业务任务
// - 个人IB(PERSONAL_IB)：支持账户资产验证等投资业务任务
//
// 等级体系：
// - 普通(NORMAL)：基础等级，切换身份时自动获得
// - 青铜(BRONZE)：完成初级任务后获得
// - 白银(SILVER)：完成中级任务后获得
// - 黄金(GOLD)：完成高级任务后获得，最高等级
//
// 核心机制：
// - 身份切换：用户可以在不同身份间切换，每个身份独立管理等级
// - 任务系统：每个身份等级都有对应的任务要求
// - 自动升级：任务完成后自动检查并升级到下一等级
// - 消息驱动：通过Kafka消息更新任务进度和触发升级
// - 数据一致性：所有操作在事务中执行，保证数据完整性
//
// 技术架构：
// - HTTP API：提供身份规则查询、身份切换、升级信息等接口
// - Kafka Consumer：监听任务进度更新消息
// - 数据分层：身份信息、等级配置、任务进度、历史记录分层存储
// - 多语言支持：支持中英文等多语言界面
package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	pb "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/models"
	"wiki_user_center/internal/pkg/http"

	"git55.fxeyeinterface.com/public-projects/go-tools/i18n"
	"git55.fxeyeinterface.com/public-projects/go-tools/icontext"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/tidwall/gjson"
	"github.com/tx7do/kratos-transport/broker"
)

var (
	_gradeSortBase    []pb.UserGrade
	_identitySortBase []pb.UserIdentity
	// 表驱动：身份中文名（作为 i18n 中文默认值）
	_identityCnNames = map[pb.UserIdentity]string{
		pb.UserIdentity_INVESTOR:         "投资者",
		pb.UserIdentity_KOL:              "KOL",
		pb.UserIdentity_SERVICE_PROVIDER: "服务商",
		pb.UserIdentity_TRADER:           "交易商",
		pb.UserIdentity_PERSONAL_IB:      "个人IB",
	}
	// 表驱动：等级中文名（作为 i18n 中文默认值）
	_gradeCnNames = map[pb.UserGrade]string{
		pb.UserGrade_NORMAL: "普通",
		pb.UserGrade_BRONZE: "青铜",
		pb.UserGrade_SILVER: "白银",
		pb.UserGrade_GOLD:   "黄金",
	}
)

// 引用以避免未使用的静态检查（方法值，无副作用）
var _ = (*Service).getEnterpriseInfoByUser

const (
	identityRoleUpGradeTipsFmt    = "如何升级{0}身份？"                // 成长规则提示
	identityRoleUpGradeDescFmt    = "按以下规则完成任务，升级对应身份" // 成长规则升级描述
	identityUpGradeTitleFmt       = "完成以下任务，即可升级{0}"
	kolGradeDescFmt               = "升级KOL身份，扩张更强影响力"
	serviceProviderGradeDescFmt   = "升级服务商，发掘更多合作商机"
	investorGradeDescFmt          = "升级投资者身份，开启投资新篇章"
	traderGradeDescFmt            = "升级交易商，扩张更强影响力"
	personalIBGradeDescFmt        = "升级个人IB，获得更多合作机会"
	bronzeInvestUpgradeWelfareFmt = "升级成功送天眼VIP"
)

func init() {
	_gradeSortBase = []pb.UserGrade{pb.UserGrade_NORMAL, pb.UserGrade_BRONZE, pb.UserGrade_SILVER, pb.UserGrade_GOLD}
	// _identitySortBase = []pb.UserIdentity{pb.UserIdentity_INVESTOR, pb.UserIdentity_KOL, pb.UserIdentity_SERVICE_PROVIDER}
	_identitySortBase = []pb.UserIdentity{pb.UserIdentity_INVESTOR, pb.UserIdentity_KOL, pb.UserIdentity_SERVICE_PROVIDER, pb.UserIdentity_TRADER, pb.UserIdentity_PERSONAL_IB}
}

type (
	// InfoOutput 用户加入或创建企业信息
	InfoOutput struct {
		Code        string   `json:"code"`
		MainService string   `json:"mainService"`
		SubService  []string `json:"subService"`
	}
	TaskScheduler func(tsk *models.IdentityGradeTaskInfo) *models.TaskCompletion
)

// fmtI18n 占位格式化 + 多语言获取
func fmtI18n(language, base string, args ...interface{}) string {
	txt := i18n.GetWithChineseValueDefaultEnglish(base, language)
	if len(args) == 0 {
		return txt
	}
	for i := range args {
		placeholder := fmt.Sprintf("{%d}", i)
		txt = strings.ReplaceAll(txt, placeholder, "%v")
	}
	return fmt.Sprintf(txt, args...)
}

func getIdentityName(identity pb.UserIdentity, language string) string {
	name := _identityCnNames[identity]
	return i18n.GetWithChineseValueDefaultEnglish(name, language)
}

func getGradeName(grade pb.UserGrade, language string) string {
	name := _gradeCnNames[grade]
	return i18n.GetWithChineseValueDefaultEnglish(name, language)
}

func getIdentityGradeName(identity pb.UserIdentity, grade pb.UserGrade, language string) string {

	identityName := getIdentityName(identity, "zh-CN")
	identityName = fmt.Sprintf("%s%s", "{0}", identityName)
	identityName = i18n.GetWithChineseValueDefaultEnglish(identityName, language)

	if strings.EqualFold(language, "de") || strings.EqualFold(language, "ko") {
		identityName = strings.ReplaceAll(identityName, "{0}", "{0} ")
	}

	return fmt.Sprintf(strings.ReplaceAll(identityName, "{0}", "%s"), getGradeName(grade, language))
}

func getIdentityUpGradeTips(identity pb.UserIdentity, language string) string {
	name := getIdentityName(identity, language)
	return fmtI18n(language, identityRoleUpGradeTipsFmt, name)
}

func getIdentityUpGradeDesc(_ pb.UserIdentity, language string) string {
	return i18n.GetWithChineseValueDefaultEnglish(identityRoleUpGradeDescFmt, language)
}

func getIdentityGradeDesc(identity pb.UserIdentity, language string) string {
	switch identity {
	case pb.UserIdentity_INVESTOR:
		return i18n.GetWithChineseValueDefaultEnglish(investorGradeDescFmt, language)
	case pb.UserIdentity_KOL:
		return i18n.GetWithChineseValueDefaultEnglish(kolGradeDescFmt, language)
	case pb.UserIdentity_SERVICE_PROVIDER:
		return i18n.GetWithChineseValueDefaultEnglish(serviceProviderGradeDescFmt, language)
	case pb.UserIdentity_TRADER:
		return i18n.GetWithChineseValueDefaultEnglish(traderGradeDescFmt, language)
	case pb.UserIdentity_PERSONAL_IB:
		return i18n.GetWithChineseValueDefaultEnglish(personalIBGradeDescFmt, language)
	default:
		return ""
	}
}

func getIdentityGradeUpgradeTitle(identity pb.UserIdentity, grade pb.UserGrade, language string) string {
	name := getIdentityGradeName(identity, grade, language)
	return fmtI18n(language, identityUpGradeTitleFmt, name)
}

// getIdentityRule 构建单个身份的规则信息
// 执行步骤：
// 1. 检查用户是否已获得该身份
// 2. 按等级分组整理规则数据
// 3. 为每个等级生成任务信息和徽章
// 4. 构建身份规则的完整信息
// 注意事项：
// - 支持多语言的身份名称和描述
// - 根据用户获得状态设置isAcquire标识
// - 每个等级包含任务列表和完成状态
// - 返回的等级按预定义顺序排列
func (s *Service) getIdentityRule(identity pb.UserIdentity, rules []*models.IdentityGradeRule, language string) *pb.IdentityRuleBaseInfo {

	var (
		isAcquire bool
		grades    = make([]*pb.IdentityGradeInfo, 0, len(rules))
		mapGrade  = make(map[pb.UserGrade][]*models.IdentityGradeRule)
	)

	for _, v := range rules {
		if v.IdentityGradeId != "" {
			isAcquire = true
		}
		mapGrade[v.Grade] = append(mapGrade[v.Grade], v)
	}

	for _, v := range _gradeSortBase {
		tasks := mapGrade[v]
		gradeTask := make([]*pb.IdentityGradeTask, 0, len(tasks))
		if len(tasks) > 0 {
			tskContents := make([]string, 0, len(tasks))
			for _, vv := range tasks {
				tskContents = append(tskContents, vv.Content)
			}
			var content string
			if len(tskContents) > 1 {
				content = i18n.GetWithChineseValueDefaultEnglish(strings.Join(tskContents, "且"), language)
			} else if len(tskContents) == 1 {
				content = i18n.GetWithChineseValueDefaultEnglish(tskContents[0], language)
			}
			for i, task := range tasks {
				if strings.Contains(content, fmt.Sprintf("{%d}", i)) { // 是否包含占位符
					if task.Type == 1 {
						if task.Target > 0 {
							content = fmt.Sprintf(strings.ReplaceAll(content, fmt.Sprintf("{%d}", i), "%v"), getDigitalFormat(task.Target))
						} else {
							content = fmt.Sprintf(strings.ReplaceAll(content, fmt.Sprintf("{%d}", i), "%v"), getIdentityName(identity, language))
						}
					} else if task.Type == 2 {
						content = fmt.Sprintf(strings.ReplaceAll(content, fmt.Sprintf("{%d}", i), "%v"), getDigitalFormat(task.Target))
					}
				}
			}
			gradeTask = append(gradeTask, &pb.IdentityGradeTask{TaskContent: content})
		}

		grades = append(grades, &pb.IdentityGradeInfo{
			GradeName: getGradeName(v, language),
			GradeTask: gradeTask,
			BgColor:   _identityGradeBg[identity][v],
		})
	}

	return &pb.IdentityRuleBaseInfo{
		Name:          getIdentityName(identity, language),
		IsAcquire:     isAcquire,
		IdentityTips:  getIdentityUpGradeTips(identity, language),
		IdentityDesc:  getIdentityUpGradeDesc(identity, language),
		IdentityGrade: grades,
	}
}

// getGradeColor / getIdentityGradeColor / getIdentityBannerBgColor / getIdentityGradeProcessImg
// 已迁移至 growth_center_assets.go

func getDigitalFormat(d float64) string {
	f := fmt.Sprintf("%.f", d)
	if len(f) < 3 {
		return f
	}

	commaIndex := len(f) % 3
	if commaIndex == 0 {
		commaIndex = 3
	}
	builder := strings.Builder{}
	builder.WriteString(f[:commaIndex])
	for i := commaIndex; i < len(f); i += 3 {
		builder.WriteString(",")
		builder.WriteString(f[i : i+3])
	}
	return builder.String()
}

func (s *Service) getEnterpriseInfoByUser(_ context.Context, userId string) *InfoOutput {
	err, resp := http.NewHttpGetRequest(s.business.ServiceProviderAddr+"api/serviceproviderservice/serviceinfo?userId="+userId, "156", "zh-CN", "0,111,3,210,0,111,1")
	if err != nil {
		fmt.Println("获取用户加入的企业信息失败,err:" + err.Error())
		return nil
	}
	var output *InfoOutput
	if gjson.Get(resp, "succeed").Bool() {
		raw := gjson.Get(resp, "result").Raw
		if strings.TrimSpace(raw) != "" {
			err := json.Unmarshal([]byte(raw), &output)
			if err != nil {
				s.log.Errorf("Unmarshal InfoOutput,Err:%v", err)
				return nil
			}
		}
	}
	return output
}

// PostUserIdentitySwitch APP用户手动切换身份的HTTP接口
// 执行步骤：
// 1. 验证请求参数的有效性（用户ID和身份类型）
// 2. 将身份枚举转换为字符串格式
// 3. 调用身份切换消息处理逻辑
// 4. 返回切换结果
// 注意事项：
// - 支持五种身份类型：投资者、KOL、服务商、交易商、个人IB
// - 不允许切换到UNKNOWN身份
// - 内部通过调用ReceiveUserIdentityMsg实现身份切换
// - 切换失败时返回false和错误信息
// - 切换成功时返回true
func (s *Service) PostUserIdentitySwitch(ctx context.Context, in *pb.PostUserIdentitySwitchRequest) (*pb.PostUserIdentitySwitchReply, error) {
	s.log.Info(fmt.Sprintf("APP 切换身份传入的参数为:%#v", in))

	if strings.TrimSpace(in.UserId) == "" {
		return nil, errors.New("用户ID为空")
	}
	if in.Identity == pb.UserIdentity_UNKNOWN {
		return nil, errors.New("身份选择无效")
	}
	var identity string
	if in.Identity == pb.UserIdentity_INVESTOR {
		identity = "investor"
	} else if in.Identity == pb.UserIdentity_KOL {
		identity = "kol"
	} else if in.Identity == pb.UserIdentity_SERVICE_PROVIDER {
		identity = "service"
	} else if in.Identity == pb.UserIdentity_TRADER {
		identity = "trader"
	} else if in.Identity == pb.UserIdentity_PERSONAL_IB {
		identity = "personal_ib"
	}

	err := s.ReceiveUserIdentityMsg(ctx, "", nil, &pb.UserIdentityMsg{
		UserId:   in.UserId,
		Identity: identity,
	})
	if err != nil {
		return &pb.PostUserIdentitySwitchReply{Result: false}, err
	}
	return &pb.PostUserIdentitySwitchReply{Result: true}, nil
}

// GetUpgradeIdentity 获取用户身份升级信息的HTTP接口
// 执行步骤：
// 1. 获取用户当前身份和等级信息
// 2. 查询该身份下所有等级配置
// 3. 找到下一个可升级的等级
// 4. 获取升级所需的任务信息
// 5. 返回升级提示和任务列表
// 注意事项：
// - 如果用户没有身份，返回空结果
// - 如果已达到最高等级，不返回升级信息
// - 支持多语言的升级提示文案
// - 返回下一等级的任务列表和完成状态
func (s *Service) GetUpgradeIdentity(ctx context.Context, in *pb.GetUpgradeIdentityRequest) (*pb.GetUpgradeIdentityReply, error) {
	l := s.log.WithContext(ctx)
	language, _ := icontext.LanguageCodeFrom(ctx)

	userGrade, err := s.userIdentity.GetUserIdentityGrade(ctx, in.UserId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetUpgradeIdentity,Err:%v", err)
		return nil, err
	}
	if userGrade == nil {
		return &pb.GetUpgradeIdentityReply{
			UserId: in.UserId,
		}, nil
		//return nil, errors.New("do not get any identity")
	}

	grades, err := s.identityGrade.FindWithIdentity(ctx, userGrade.Identity)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetUpgradeIdentity,Err:%v", err)
		return nil, err
	}
	var nextGrade *models.IdentityGrade
	for _, v := range grades {
		if v.Grade > userGrade.Grade {
			nextGrade = v
			break
		}
	}

	if nextGrade == nil {
		return &pb.GetUpgradeIdentityReply{
			UserId: in.UserId,
		}, nil
		//return nil, errors.New("已经升级到最高级或没有任何等级")
	}

	tasks, err := s.gradeTask.FindTaskWithGradeId(ctx, nextGrade.ID)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetUpgradeIdentity,Err:%v", err)
		return nil, err
	}
	if len(tasks) < 1 {
		return nil, errors.New("no any processing task")
	}

	useTasks, err := s.userTaskSchedule.FindUserTaskScheduleWithGradeId(ctx, in.UserId, nextGrade.ID)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetUpgradeIdentity,Err:%v", err)
		return nil, err
	}

	var current *models.GradeTask

	if len(useTasks) == 0 {
		current = &tasks[0].GradeTask
	} else {
		for _, v := range tasks {
			for _, vv := range useTasks {
				if !vv.IsFinish && v.ID == vv.GradeTaskId {
					current = &v.GradeTask
					break
				}
			}
		}
	}

	if current == nil {
		return nil, errors.New("没有任务")
	}

	return &pb.GetUpgradeIdentityReply{
		UserId:      in.UserId,
		Name:        i18n.GetWithChineseValueDefaultEnglish("升级身份", language),
		Logo:        s.asset("growth_center/upgrade.png"),
		JumpAddress: current.JumpAddress,
	}, nil
}

// GetGrowthCenterEntry 成长中心入展示开关
func (s *Service) GetGrowthCenterEntry(_ context.Context, _ *pb.GetGrowthCenterEntryRequest) (*pb.GetGrowthCenterEntryReply, error) {
	return &pb.GetGrowthCenterEntryReply{
		IsShowGrowthCenter: true,
		IsShowGradeRule:    true,
	}, nil
}

// GetIdentityCarousel 获取我的页面banner位置轮播图（有屏蔽点位）
func (s *Service) GetIdentityCarousel(ctx context.Context, _ *pb.GetIdentityCarouselRequest) (*pb.GetIdentityCarouselReply, error) {
	l := s.log.WithContext(ctx)
	language, _ := icontext.LanguageCodeFrom(ctx)
	data, err := s.banner.FindBanner(ctx, language)
	if err != nil {
		l.Errorf("GetIdentityCarousel,Error:%v", err)
		return nil, err
	}
	banners := make([]*pb.CarouselInfo, 0, len(data))

	for _, v := range data {
		banners = append(banners, &pb.CarouselInfo{
			Name:    v.Name,
			Img:     s.asset(v.ImgsBanner),
			JumpUrl: v.JumpUrl,
		})
	}

	return &pb.GetIdentityCarouselReply{
		Banners:  banners,
		TimeSpan: 0,
	}, nil
}

// GetIdentityRule 获取用户身份升级规则列表的HTTP接口
// 执行步骤：
// 1. 获取用户语言偏好设置
// 2. 查询所有身份的升级规则配置
// 3. 按身份类型分组整理规则数据
// 4. 为每个身份生成规则信息和徽章图片
// 5. 按预定义顺序返回身份规则列表
// 注意事项：
// - 支持多语言的规则描述
// - 每个身份都有对应的规则徽章图片
// - 返回的身份顺序：投资者、KOL、服务商、交易商、个人IB
// - 如果没有规则配置，返回空列表
// - 包含每个身份的等级信息和任务要求
func (s *Service) GetIdentityRule(ctx context.Context, in *pb.GetIdentityRuleRequest) (*pb.GetIdentityRuleReply, error) {

	language, _ := icontext.LanguageCodeFrom(ctx)
	l := s.log.WithContext(ctx)
	userId := in.UserId

	rules, err := s.userIdentity.FindIdentityRule(ctx, userId)
	if err != nil {
		l.Errorf("FindIdentityRule,Err:%v", err)
		return nil, err
	}

	if len(rules) == 0 {
		return &pb.GetIdentityRuleReply{}, nil
	}

	var outRules []*pb.IdentityRuleBaseInfo
	mapIdentity := make(map[pb.UserIdentity][]*models.IdentityGradeRule)

	outRules = make([]*pb.IdentityRuleBaseInfo, 0, len(rules))
	for _, v := range rules {
		mapIdentity[v.Identity] = append(mapIdentity[v.Identity], v)
	}

	for _, v := range _identitySortBase {
		baseInfo := mapIdentity[v]
		if len(baseInfo) > 0 {
			r := s.getIdentityRule(v, baseInfo, language)
			var badge string
			if p, ok := _identityRuleBadge[v]; ok {
				badge = s.asset(p)
			}

			if r != nil {
				r.Badge = badge
				outRules = append(outRules, r)
			}
		}
	}

	return &pb.GetIdentityRuleReply{
		Rules: outRules,
	}, nil

}

// GetIdentityShare 获取用户身份分享图片的HTTP接口
// 执行步骤：
// 1. 获取用户语言偏好设置
// 2. 查询用户身份分享信息
// 3. 生成身份名称和获得日期
// 4. 获取对应的分享徽章图片
// 5. 返回分享图片URL和相关信息
// 注意事项：
// - 支持多语言的身份名称显示
// - 分享徽章根据身份类型和等级动态生成
// - 包含用户获得身份的日期信息
// - 如果用户没有身份，返回空结果
func (s *Service) GetIdentityShare(ctx context.Context, in *pb.GetIdentityShareRequest) (*pb.GetIdentityShareReply, error) {
	language, _ := icontext.LanguageCodeFrom(ctx)

	if in.UserId == "" {
		return nil, errors.New("param is null or empty")
	}

	shareInfo, err := s.userIdentity.GetUserShare(ctx, in.UserId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		s.log.Errorf("GetIdentityShare,Error:%v", err)
		return nil, err
	}

	if shareInfo == nil {
		return &pb.GetIdentityShareReply{}, nil
	}

	acquireDate := shareInfo.AcquireTime.Format("2006/01/02")
	identity := getIdentityGradeName(shareInfo.Identity, shareInfo.Grade, language)
	var (
		shareBadge  string
		badgeHeight float64
		badgeWidth  float64
	)
	badgeWidth, badgeHeight, shareBadge = getShareBadge(shareInfo.Identity, shareInfo.Grade, language, shareInfo.ShareBadge)
	return &pb.GetIdentityShareReply{
		UserId:      shareInfo.UserId,
		Badge:       s.asset(shareBadge),
		BadgeWidth:  badgeWidth,
		BadgeHeight: badgeHeight,
		Identity:    identity,
		AcquireTime: fmt.Sprintf(strings.ReplaceAll(i18n.GetWithChineseValueDefaultEnglish("{0} 获得", language), "{0}", "%s"), acquireDate),
		Content: []string{
			fmt.Sprintf(strings.ReplaceAll(i18n.GetWithChineseValueDefaultEnglish("在WikiFX成功升级至{0}！", language), "{0}", "%s"), identity),
			i18n.GetWithChineseValueDefaultEnglish("有付出就有收获，值得鼓励！", language),
		},
		FooterContent: []string{
			i18n.GetWithChineseValueDefaultEnglish("来WikiFX达成更多成就", language),
		},
		FooterQrImage: s.asset("growth_center/wikifx_QR_img.png"),
	}, nil
}

// GetUserGrowthDetail 身份成长中心详情页
func (s *Service) GetUserGrowthDetail(ctx context.Context, in *pb.GetUserGrowthDetailRequest) (*pb.GetUserGrowthDetailReply, error) {
	l := s.log.WithContext(ctx)
	language, _ := icontext.LanguageCodeFrom(ctx)

	if in.UserId == "" {
		return nil, errors.New("param is null or empty")
	}

	identityGrade, err := s.userIdentity.GetUserIdentityGrade(ctx, in.UserId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetUserGrowthDetail,Err:%v", err)
		return nil, err
	}

	if errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("没有获得身份等级信息")
		return nil, errors.New("没有获得身份等级信息")
	}

	gradeTasks, err := s.gradeTask.FindTaskWithIdentity(ctx, identityGrade.Identity)
	if err != nil {
		l.Errorf("GetUserGrowthDetail,Err:%v", err)
		return nil, err
	}
	if len(gradeTasks) < 1 {
		return nil, errors.New("没有任何升级任务")
	}

	userRelation, err := s.userRelation.GetEnabledUserRelation(ctx, in.UserId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetEnabledUserRelation,Err:%v", err)
		return nil, err
	}
	if userRelation == nil {
		l.Errorf("没有用户映射关系")
		return nil, errors.New("没有用户映射关系")
	}

	userTasks, err := s.userTaskSchedule.FindUserTaskScheduleWithIdentity(ctx, userRelation.ObjectCode, identityGrade.Identity)
	if err != nil {
		l.Errorf("GetUserGrowthDetail,Err:%v", err)
		return nil, err
	}
	var (
		mapGradeTask = make(map[pb.UserGrade][]*pb.IdentityGradeTask)
		mapGradeInfo = make(map[pb.UserGrade]*models.IdentityGradeTaskInfo)
	)

	taskTipsFun := func(tsk pb.TaskEnum) []string {
		if tsk == pb.TaskEnum_VERIFY_ACCOUNT_ASSETS || tsk == pb.TaskEnum_VERIFY_DEPOSIT_SCALE {
			t := "数据存在一定延迟，请等待"
			t1 := i18n.GetWithChineseValueDefaultEnglish(t, language)
			if t1 != "" {
				t = t1
			}
			return []string{"(" + t + ")"}
		}
		return nil
	}

	for _, v := range gradeTasks {
		// 获取每一个等级的信息
		if _, ok := mapGradeInfo[v.Grade]; !ok {
			mapGradeInfo[v.Grade] = v
		}

		if v.Label == "" {
			v.Label = v.Content
		}
		var (
			tskContent = i18n.GetWithChineseValueDefaultEnglish(v.Label, language)
			target     string
			current    = "0"
			tasksColor string
			isFinish   bool
			userTask   *models.UserIdentityTaskSchedule
		)
		for _, sv := range userTasks {
			if v.ID == sv.GradeTaskId {
				userTask = sv
				break
			}
		}

		if userTask != nil {
			isFinish = userTask.IsFinish
			current = fmt.Sprintf("%.0f", userTask.Schedule)
		}
		// 如果已经获得，则将任务进度设置成阈值（保证已获得的等级不回退）
		if identityGrade.Grade >= v.Grade {
			isFinish = true
			current = fmt.Sprintf("%d", mapGradeInfo[v.Grade].Target)
		}
		if strings.Contains(tskContent, "{0}") { // 是否包含占位符
			if v.Type == 1 {
				if v.Target > 0 {
					tskContent = fmt.Sprintf(strings.ReplaceAll(tskContent, "{0}", "%v"), getDigitalFormat(float64(v.Target)))
				} else {
					tskContent = fmt.Sprintf(strings.ReplaceAll(tskContent, "{0}", "%v"), getIdentityName(identityGrade.Identity, language))
				}

			} else if v.Type == 2 {
				tskContent = fmt.Sprintf(strings.ReplaceAll(tskContent, "{0}", "%v"), getDigitalFormat(float64(v.Target)))
				target = fmt.Sprintf("%d", v.Target)
			}
		}
		if isFinish {
			tasksColor = "#C9CDD4"
		} else {
			tasksColor = "#FADC19"
		}

		task := &pb.IdentityGradeTask{
			TaskId:          v.ID,
			TaskName:        i18n.GetWithChineseValueDefaultEnglish(v.Name, language),
			TaskContent:     tskContent,
			TaskType:        v.Type,
			TaskTarget:      target,
			TaskCurrent:     current,
			IsFinish:        isFinish,
			TaskStatusColor: tasksColor,
			TaskIco:         s.asset("/growth_center/task_icon.png"),
			JumpAddress:     v.JumpAddress,
			JumpAddressType: 1,
			TaskTips:        taskTipsFun(v.Task),
		}
		if val, ok := mapGradeTask[v.Grade]; ok {
			val = append(val, task)
			mapGradeTask[v.Grade] = val
		} else {
			mapGradeTask[v.Grade] = []*pb.IdentityGradeTask{task}
		}
	}

	var (
		//isIBServiceProvider bool
		gradeInfo = make([]*pb.IdentityGradeInfo, 0, len(mapGradeTask))
		//enterprise          *InfoOutput
	)

	nextTaskSort := []pb.UserGrade{pb.UserGrade_BRONZE, pb.UserGrade_SILVER, pb.UserGrade_GOLD} // 下一级任务的对应关系

	for i, v := range _gradeSortBase {
		var (
			badge              string
			badgeHeight        float64
			badgeWidth         float64
			banner             string
			isAcquire          bool
			tasks              []*pb.IdentityGradeTask
			upGradeSuccessTips []string
			gradeUpgradeTitle  string
			upGradeSuccessImg  string
		)

		if i < len(nextTaskSort) {
			grade := nextTaskSort[i]
			tasks = mapGradeTask[grade]
			gradeUpgradeTitle = getIdentityGradeUpgradeTitle(identityGrade.Identity, grade, language)
		}

		if val, ok := mapGradeInfo[v]; ok && val != nil {
			badgeWidth, badgeHeight, badge = getBadge(identityGrade.Identity, v, language, val.Badge)
			banner = val.Banner
		}

		if identityGrade.Grade >= v { // 小于当前获得的等级，都记为获得
			isAcquire = true
		}

		if v == pb.UserGrade_GOLD { // 黄金等级需要判断提示词
			if isAcquire {
				t1 := i18n.GetWithChineseValueDefaultEnglish("恭喜您", language)
				tmp := i18n.GetWithChineseValueDefaultEnglish("已升至{0}最高等级", language)
				t2 := fmt.Sprintf(strings.ReplaceAll(tmp, "{0}", "%s"), getIdentityName(identityGrade.Identity, language))
				upGradeSuccessTips = []string{
					t1, t2,
				}
				upGradeSuccessImg = s.asset("/growth_center/upgrade_success.png")
			} else {
				t1 := i18n.GetWithChineseValueDefaultEnglish("完成全部升级任务", language)
				tmp := i18n.GetWithChineseValueDefaultEnglish("即可升级至{0}", language)
				t2 := fmt.Sprintf(strings.ReplaceAll(tmp, "{0}", "%s"), getIdentityGradeName(identityGrade.Identity, v, language))
				upGradeSuccessTips = []string{
					t1,
					t2,
				}
			}
		}

		processNameFunc := func(identity pb.UserIdentity, grade pb.UserGrade, language string) string {
			if strings.EqualFold("zh-cn", language) { // 短语言
				return getIdentityGradeName(identity, grade, language)
			} else {
				return getGradeName(grade, language)
			}
		}

		gradeNameFontColorFun := func(u pb.UserGrade) string {
			if u == pb.UserGrade_NORMAL {
				return "#8F96A5"
			}
			return "#FFFFFF"
		}

		gradeInfo = append(gradeInfo, &pb.IdentityGradeInfo{
			GradeName:          getIdentityGradeName(identityGrade.Identity, v, language),
			GradeUpgradeTitle:  gradeUpgradeTitle,
			GradeDesc:          getIdentityGradeDesc(identityGrade.Identity, language),
			Badge:              s.asset(badge),
			BadgeHeight:        badgeHeight,
			BadgeWidth:         badgeWidth,
			GradeTask:          tasks,
			IsAcquire:          isAcquire,
			UpgradeSuccessTips: upGradeSuccessTips,
			Banner:             s.asset(banner),
			BgColor:            getGradeColor(v),
			UpgradeSuccessImg:  upGradeSuccessImg,
			GradeNameColor:     getIdentityGradeColor(v),
			GradeProcessImg:    s.asset(getIdentityGradeProcessImg(v)),
			ProcessName:        processNameFunc(identityGrade.Identity, v, language),
			BannerBgColor:      getIdentityBannerBgColor(v),
			GradeNameFontColor: gradeNameFontColorFun(v),
		})
	}

	return &pb.GetUserGrowthDetailReply{
		UserId:    in.UserId,
		GradeInfo: gradeInfo,
	}, nil
}

// 徽章计算与辅助函数已迁移至 growth_center_assets.go

/*==========================kafka consumer=========================*/

// HandleGrowthMsg 处理用户成长中心的Kafka消息分发
// 执行步骤：
// 1. 根据消息类型分发到对应的处理函数
// 2. 支持六种消息类型的处理
// 3. 对未知消息类型返回错误
// 注意事项：
// - SWITCH_IDENTITY_TYPE：用户身份切换消息
// - INVESTOR_TYPE：投资者任务进度消息
// - KOL_TYPE：KOL任务进度消息
// - PERSONAL_IB_TYPE：个人IB任务进度消息
// - SERVICE_PROVIDER_TYPE：服务商任务进度消息
// - TRADER_TYPE：交易商任务进度消息
// - 消息处理失败时会返回具体错误信息
func (s *Service) HandleGrowthMsg(ctx context.Context, m string, h broker.Headers, in *pb.UserGrowthMsg) error {
	switch in.Type {
	case pb.GrowthMsgType_SWITCH_IDENTITY_TYPE:
		return s.ReceiveUserIdentityMsg(ctx, m, h, in.Identity)
	case pb.GrowthMsgType_INVESTOR_TYPE:
		return s.ReceiveInvestorMsg(ctx, m, h, in.Investor)
	case pb.GrowthMsgType_KOL_TYPE:
		return s.ReceiveKolMsg(ctx, m, h, in.Kol)
	case pb.GrowthMsgType_PERSONAL_IB_TYPE:
		return s.ReceivePersonalIBTaskMsg(ctx, m, h, in.PersonalIb)
	case pb.GrowthMsgType_SERVICE_PROVIDER_TYPE:
		return s.ReceiveServiceProviderMsg(ctx, m, h, in.Service)
	case pb.GrowthMsgType_TRADER_TYPE:
		return s.ReceiveTraderMsg(ctx, m, h, in.Trader)
	default:
		return fmt.Errorf("消息类型不合法:%d", in.Type)
	}
}

// HandleIdentityGradeUpgrade 处理用户身份等级升级的核心逻辑
// 执行步骤：
// 1. 验证用户当前身份状态和等级信息
// 2. 获取指定身份下所有等级的任务配置
// 3. 根据任务完成情况计算用户可达到的最高等级
// 4. 更新任务进度和用户身份等级信息
// 5. 记录身份升级历史和触发相关奖励
// 注意事项：
// - 等级升级必须按顺序进行，不能跳级
// - 支持两种任务完成模式：全部完成(ALL_FINISH)和任意完成(ANY_ONE)
// - 身份不一致的消息会被丢弃，防止数据错乱
// - 升级到青铜投资者时会自动获得天眼VIP奖励
// - 所有数据更新在事务中执行，保证数据一致性
func (s *Service) HandleIdentityGradeUpgrade(ctx context.Context, userId string, identity pb.UserIdentity, fun TaskScheduler) error {
	userIdentity, err := s.userIdentity.GetUserIdentity(ctx, userId)
	if err != nil {
		return err
	}
	if userIdentity == nil || userIdentity.IdentityGradeId == "" || userIdentity.Status == 0 {
		return errors.New("not acquire any identity ")
	}

	userGrade, err := s.userIdentity.GetUserIdentityGrade(ctx, userId)
	if err != nil {
		return err
	}

	if userGrade.Identity != identity {
		//防止生产端推送过来身份不一致的消息，导致任务进度错乱，身份不一致，消息丢弃
		return errors.New("身份不一致，消息丢弃")
	}

	if userGrade.Grade == pb.UserGrade_GOLD {
		return errors.New("用户已经升级至最高等级")
	}

	userRelation, err := s.userRelation.GetEnabledUserRelation(ctx, userId)
	if err != nil {
		return err
	}

	if userRelation == nil {
		return errors.New("没有查询到用户映射信息")
	}

	identityGrade, err := s.gradeTask.FindTaskWithIdentity(ctx, userGrade.Identity)
	if err != nil {
		return err
	}

	var (
		mapGradeTask           = make(map[pb.UserGrade][]*models.IdentityGradeTaskInfo)
		taskSchedule           = make([]*models.UserIdentityTaskSchedule, 0, len(identityGrade))
		currentIdentityGradeId string
		identityHistory        = make([]*models.UserIdentityHistory, 0, len(identityGrade))
		processingTaskSchedule []*models.UserIdentityTaskSchedule
		//preGradeFinished       = true
		currentFinishGrade       = userGrade.Grade
		gradeFinishList          = make([]pb.UserGrade, 0)
		gradeFinishMapping       = make(map[pb.UserGrade]string)
		gradeTaskScheduleMapping = make(map[pb.UserGrade][]*models.UserIdentityTaskSchedule)
		objectCode               = userRelation.ObjectCode
	)

	for _, v := range identityGrade {
		if v.Grade > currentFinishGrade {
			if val, ok := mapGradeTask[v.Grade]; ok {
				val = append(val, v)
				mapGradeTask[v.Grade] = val
			} else {
				mapGradeTask[v.Grade] = []*models.IdentityGradeTaskInfo{v}
			}
		}
	}

	for _, v := range _gradeSortBase {
		tasks := mapGradeTask[v]
		if len(tasks) > 0 {
			var gradeFinish = true
			var mode = tasks[0].Mode
			for _, sv := range tasks {
				uts := &models.UserIdentityTaskSchedule{
					UserId:          objectCode,
					Identity:        userGrade.Identity,
					Grade:           v,
					IdentityGradeId: sv.IdentityGradeId,
					GradeTaskId:     sv.ID,
					CreateTime:      time.Now().UTC(),
					UpdateTime:      time.Now().UTC(),
				}

				taskCompletion := fun(sv)

				uts.ID = uts.GetId()
				uts.Schedule = taskCompletion.Schedule
				uts.IsFinish = taskCompletion.IsFinished

				if mode == pb.IdentityGradeMode_ALL_FINISH { // 全部完成
					gradeFinish = gradeFinish && taskCompletion.IsFinished
				} else if mode == pb.IdentityGradeMode_ANY_ONE { // 部分完成
					gradeFinish = gradeFinish || taskCompletion.IsFinished
				}

				//taskSchedule = append(taskSchedule, uts)
				if val, ok := gradeTaskScheduleMapping[v]; ok {
					val = append(val, uts)
					gradeTaskScheduleMapping[v] = val
				} else {
					gradeTaskScheduleMapping[v] = []*models.UserIdentityTaskSchedule{uts}
				}
			}

			if gradeFinish {
				gradeFinishMapping[v] = tasks[0].IdentityGradeId
				gradeFinishList = append(gradeFinishList, v)
			}

		}
	}

	for _, v := range gradeFinishList {
		if v-currentFinishGrade == 1 { // 完成不能跳跃

			currentFinishGrade = v
			currentIdentityGradeId = gradeFinishMapping[v]

			uih := &models.UserIdentityHistory{
				UserId:          objectCode,
				Identity:        userGrade.Identity,
				Grade:           v,
				IdentityGradeId: currentIdentityGradeId,
				CreateTime:      time.Now().UTC(),
			}
			uih.ID = uih.GetId()
			identityHistory = append(identityHistory, uih)
		}
	}

	for _, v := range _gradeSortBase {
		if val, ok := gradeTaskScheduleMapping[v]; ok {
			if v-currentFinishGrade > 1 { // 只有前一个完成，相邻的任务有多个的，部分完成的，标记完成
				for i := range val {
					val[i].IsFinish = false
				}
			}
			taskSchedule = append(taskSchedule, val...)
		}
	}

	if currentIdentityGradeId != "" {
		userIdentity.IdentityGradeId = currentIdentityGradeId
		userIdentity.AcquireTime = time.Now().UTC()
		userIdentity.UpdateTime = time.Now().UTC()
	}

	processingTaskSchedule, err = s.userTaskSchedule.FindUserTaskScheduleWithIdentity(ctx, objectCode, userGrade.Identity)
	if err != nil {
		return err
	}

	for i, v := range taskSchedule {
		for _, vv := range processingTaskSchedule {
			if v.GradeTaskId == vv.GradeTaskId {
				taskSchedule[i].ID = vv.ID
				taskSchedule[i].CreateTime = vv.CreateTime
			}
		}
	}

	err = s.userIdentity.Save(ctx, &models.SaveUserIdentityModel{
		UserIdentity:     *userIdentity,
		UserHistory:      identityHistory,
		UserTaskSchedule: taskSchedule,
	})

	if err != nil {
		return err
	}

	return nil
}

// AddFxVIP 为用户添加天眼VIP会员权益
// 执行步骤：
// 1. 构建天眼VIP接口的请求URL
// 2. 发送HTTP POST请求添加VIP权益
// 3. 解析响应结果并记录日志
// 4. 处理请求失败的情况
// 注意事项：
// - 主要用于投资者升级到青铜等级时的奖励
// - VIP时长为100年（终身会员）
// - 请求失败时只记录错误日志，不影响主流程
// - BasicData参数包含VIP配置信息
func (s *Service) AddFxVIP(uid string) {
	url := s.business.GetWikiApiAddr() + "usercenter/addvip100"
	resp, err := http.NewHttpPostRequest(url, struct {
		UserId string `json:"UserId"`
	}{uid},
		map[string]string{"BasicData": "999,111,3,999,0,111,1"})
	if err != nil {
		s.log.Errorf("AddFxVIP,Err:%v", err)
	}
	s.log.Infof("call [%s],with userId [%s], result:[%s]", url, uid, resp)
	b := gjson.Get(resp, "succeed").Bool()
	if !b {
		msg := gjson.Get(resp, "message")
		s.log.Errorf("call [%s],occur error:%s", url, msg)
	}
}

// ReceiveTraderMsg 处理交易商任务进度更新的Kafka消息
// 执行步骤：
// 1. 验证消息参数的有效性
// 2. 记录接收到的任务数据用于调试和监控
// 3. 获取交易商成长规则处理器
// 4. 调用身份等级升级处理逻辑
// 注意事项：
// - 主要用于处理交易商企业的员工数量和合作数量任务
// - 消息为空时返回错误
// - 处理失败时会记录详细错误日志
// - 交易商属于企业身份，需要企业代码关联
func (s *Service) ReceiveTraderMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.EnterpriseTaskMsg) error {

	l := s.log.WithContext(ctx)
	if in == nil {
		return errors.New("交易商收到的消息为nil")
	}
	l.Infof("交易商收到的任务为:%#v", in)
	method := s.getTraderGrowthRule(in)

	err := s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_TRADER, method)
	if err != nil {
		l.Errorf("ReceiveKolMsg,Err:%v", err)
		return err
	}
	return nil
}

// ReceiveServiceProviderMsg 处理服务商任务进度更新的Kafka消息
// 执行步骤：
// 1. 验证消息参数的有效性
// 2. 记录接收到的任务数据用于调试和监控
// 3. 获取服务商成长规则处理器
// 4. 调用身份等级升级处理逻辑
// 注意事项：
// - 主要用于处理服务商企业的员工数量和合作数量任务
// - 消息为空时返回错误
// - 处理失败时会记录详细错误日志
// - 服务商属于企业身份，需要企业代码关联
func (s *Service) ReceiveServiceProviderMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.EnterpriseTaskMsg) error {

	l := s.log.WithContext(ctx)
	if in == nil {
		return errors.New("服务商收到的消息为nil")
	}
	l.Infof("服务商收到的任务为:%#v", in)

	method := s.getServiceProviderGrowthRule(in)

	err := s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_SERVICE_PROVIDER, method)
	if err != nil {
		l.Errorf("ReceiveKolMsg,Err:%v", err)
		return err
	}
	return nil
}

// ReceiveKolMsg 处理KOL任务进度更新的Kafka消息
// 执行步骤：
// 1. 记录接收到的任务数据用于调试和监控
// 2. 获取KOL成长规则处理器
// 3. 调用身份等级升级处理逻辑
// 注意事项：
// - 主要用于处理KOL的粉丝数量和发帖数量任务
// - 根据粉丝数和发帖数判断任务完成状态
// - 支持多等级的粉丝和内容发布要求
// - 处理失败时会记录详细错误日志
func (s *Service) ReceiveKolMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.KolTaskMsg) error {

	l := s.log.WithContext(ctx)
	l.Infof("Kol收到的任务为:%#v", in)

	method := s.getKolGrowthRule(in)

	err := s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_KOL, method)
	if err != nil {
		l.Errorf("ReceiveKolMsg,Err:%v", err)
		return err
	}
	return nil
}

// ReceiveInvestorMsg 处理投资者任务进度更新的Kafka消息
// 执行步骤：
// 1. 记录接收到的任务数据用于调试和监控
// 2. 获取投资者成长规则处理器
// 3. 调用身份等级升级处理逻辑
// 注意事项：
// - 主要用于处理投资者的资产验证、实名认证、账户绑定等任务
// - 根据资金总额、实名状态、账户类型等判断任务完成状态
// - 处理失败时会记录详细错误日志
// - 升级到青铜投资者时会自动获得天眼VIP奖励
func (s *Service) ReceiveInvestorMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.InvestorTaskMsg) error {
	l := s.log.WithContext(ctx)
	l.Infof("投资者收到的任务为:%#v", in)

	method := s.getInvestorGrowthRule(in)

	err := s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_INVESTOR, method)
	if err != nil {
		l.Errorf("ReceiveKolMsg,Err:%v", err)
		return err
	}
	return nil
}

// ReceiveUserIdentityMsg 处理用户身份切换的Kafka消息
// 执行步骤：
// 1. 验证消息参数的有效性（用户ID不能为空）
// 2. 解析身份类型并确定对象类型和对象代码
// 3. 查询用户当前身份状态和关联关系
// 4. 处理身份清空操作（identity=UNKNOWN时）
// 5. 处理正常身份切换：创建或更新身份记录
// 6. 初始化新身份的基础等级任务（默认完成普通等级任务）
// 7. 更新用户关联关系（个人用户或企业关联）
// 8. 在事务中保存所有相关数据
// 注意事项：
// - 企业身份（服务商、交易商）必须提供企业代码
// - 身份切换时会自动完成普通等级的所有任务
// - 支持身份历史记录的恢复（重新激活之前的身份）
// - 用户关联关系会根据身份类型自动调整
// - 所有数据更新在事务中执行，保证数据一致性
func (s *Service) ReceiveUserIdentityMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.UserIdentityMsg) error {

	if strings.TrimSpace(in.UserId) == "" {
		return errors.New("UserId cannot be empty or null")
	}

	l := s.log.WithContext(ctx)
	s.log.Infof("身份切换, 接收到的数据为:%#v", in)

	var (
		identity   pb.UserIdentity
		objectType = 1
		objectCode = in.UserId
	)
	switch strings.ToLower(in.Identity) {
	case "kol":
		identity = pb.UserIdentity_KOL
	case "investor":
		identity = pb.UserIdentity_INVESTOR
	case "ordinary":
		identity = pb.UserIdentity_UNKNOWN
	case "service":
		identity = pb.UserIdentity_SERVICE_PROVIDER
	case "trader":
		identity = pb.UserIdentity_TRADER
	case "personal_ib":
		identity = pb.UserIdentity_PERSONAL_IB
	}

	if identity == pb.UserIdentity_TRADER || identity == pb.UserIdentity_SERVICE_PROVIDER {
		if strings.TrimSpace(in.EnterpriseCode) == "" {
			s.log.Infof("用户ID:%s, 切换为企业身份,企业Code为空", in.UserId)
			return errors.New("切换为企业身份,企业Code为空")
		}
		objectType = 2
		objectCode = in.EnterpriseCode
	}
	var (
		identityGradeId string
		//inProcessIdentityGradeId string
		createTime              = time.Now().UTC()
		userTaskSchedule        []*models.UserIdentityTaskSchedule
		userIdentity            *models.UserIdentity
		userHistory             []*models.UserIdentityHistory
		history                 *models.UserIdentityHistory
		err                     error
		targetRelation          *models.UserRelation
		currentRelation         *models.UserRelation
		userRelation            []*models.UserRelation
		currentUserTaskSchedule []*models.UserIdentityTaskSchedule
	)

	userIdentity, err = s.userIdentity.GetUserIdentity(ctx, in.UserId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("ReceiveUserIdentityMsg,Err:%v", err)
		return err
	}

	currentRelation, err = s.userRelation.GetEnabledUserRelation(ctx, in.UserId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("GetEnabledUserRelation,Err:%v", err)
		return err
	}

	if identity == pb.UserIdentity_UNKNOWN && userIdentity == nil {
		l.Infof("初始化,身份不对")
		return errors.New("初始化,身份不能为清空")
	}

	/********************身份清空******************/
	if identity == pb.UserIdentity_UNKNOWN {
		userIdentity.Status = 0
		userIdentity.UpdateTime = time.Now().UTC()

		if currentRelation != nil {
			currentRelation.Status = 0
			userRelation = append(userRelation, currentRelation)
		}

		err = s.userIdentity.Save(ctx,
			&models.SaveUserIdentityModel{
				UserIdentity: *userIdentity,
				UserRelation: userRelation,
			},
		)
		if err != nil {
			l.Errorf("userIdentity.Save,err:%v", err)
			return err
		}

		return nil
	}

	/********************正常切换身份**********************/
	targetRelation, err = s.userRelation.GetUserRelationWithObjectCode(ctx, in.UserId, objectCode)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("FindUserRelation,Err:%v", err)
		return err
	}

	history, err = s.identityHistory.GetUserHistory(ctx, objectCode, identity)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("ReceiveUserIdentityMsg,Err:%v", err)
		return err
	}

	tsks, err := s.gradeTask.FindTaskWithIdentity(ctx, identity)
	if err != nil {
		l.Errorf("ReceiveUserIdentityMsg,Err:%v", err)
		return err
	}

	var (
		normalTask = make([]*models.IdentityGradeTaskInfo, 0, len(tsks))
		//nextTask   = make([]*models.IdentityGradeTaskInfo, 0, len(tsks))
		grade pb.UserGrade
	)
	for _, v := range tsks {
		if v.Grade == pb.UserGrade_NORMAL { // 变更身份，默认完成最低级别的身份任务
			normalTask = append(normalTask, v)
		}
	}

	if len(normalTask) > 0 {
		identityGradeId = normalTask[0].IdentityGradeId
		grade = normalTask[0].Grade
	}

	currentUserTaskSchedule, err = s.userTaskSchedule.FindUserTaskScheduleWithGradeId(ctx, objectCode, identityGradeId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("FindUserTaskScheduleWithGradeId,Err:%v", err)
		return err
	}
	if len(currentUserTaskSchedule) == 0 {
		userTaskSchedule = make([]*models.UserIdentityTaskSchedule, 0, len(tsks))
		for _, v := range normalTask {

			t := &models.UserIdentityTaskSchedule{
				UserId:          objectCode,
				Identity:        v.Identity,
				Grade:           v.Grade,
				IdentityGradeId: identityGradeId,
				GradeTaskId:     v.ID,
				IsFinish:        true,
				CreateTime:      createTime,
				UpdateTime:      createTime,
			}
			t.ID = t.GetId()

			userTaskSchedule = append(userTaskSchedule, t)
		}
	}

	if userIdentity == nil || history == nil { // 没有任何身份或者切换到一个新的身份
		if userIdentity == nil {
			userIdentity = &models.UserIdentity{
				UserId:          in.UserId,
				IdentityGradeId: identityGradeId,
				//InProcessIdentityGradeId: inProcessIdentityGradeId,
				AcquireTime: createTime,
				CreateTime:  createTime,
				UpdateTime:  createTime,
				Status:      1,
			}
			userIdentity.ID = userIdentity.GetId()
		}
		if history == nil {
			userIdentity.IdentityGradeId = identityGradeId
			userIdentity.UpdateTime = createTime
			userIdentity.AcquireTime = createTime

			uh := &models.UserIdentityHistory{
				UserId:          objectCode,
				Identity:        identity,
				Grade:           grade,
				IdentityGradeId: identityGradeId,
				CreateTime:      createTime,
			}
			uh.ID = uh.GetId()
			userHistory = append(userHistory, uh)
		}
	} else {
		if userIdentity.Status == 0 {
			userIdentity.Status = 1
		}
		userIdentity.IdentityGradeId = history.IdentityGradeId
		userIdentity.AcquireTime = history.CreateTime
		userIdentity.UpdateTime = time.Now().UTC()
	}

	// 目标为空
	if targetRelation == nil {
		ur := &models.UserRelation{
			UserId:     in.UserId,
			ObjectCode: objectCode,
			ObjectType: objectType,
			CreateTime: createTime,
			Status:     1,
		}
		ur.ID = ur.GetId()
		userRelation = append(userRelation, ur)
	} else if targetRelation.Status == 0 {
		targetRelation.UpdateTime = &createTime
		targetRelation.Status = 1
		userRelation = append(userRelation, targetRelation)
	}

	if currentRelation != nil && (currentRelation.ObjectType != objectType || currentRelation.ObjectCode != objectCode) {
		currentRelation.UpdateTime = &createTime
		currentRelation.Status = 0
		userRelation = append(userRelation, currentRelation)
	}

	err = s.userIdentity.Save(ctx,
		&models.SaveUserIdentityModel{
			UserIdentity:     *userIdentity,
			UserHistory:      userHistory,
			UserTaskSchedule: userTaskSchedule,
			UserRelation:     userRelation,
		},
	)

	if err != nil {
		l.Errorf("ReceiveUserIdentityMsg,err:%v", err)
		return err
	}

	return nil
}

// ReceivePersonalIBTaskMsg 处理个人IB任务进度更新的Kafka消息
// 执行步骤：
// 1. 验证消息参数的有效性（用户ID不能为空）
// 2. 记录接收到的任务数据用于调试和监控
// 3. 获取个人IB成长规则处理器
// 4. 调用身份等级升级处理逻辑
// 注意事项：
// - 主要用于处理个人IB用户的账户资产验证任务
// - 根据账户资产金额自动判断任务完成状态
// - 任务完成后会触发等级升级检查
// - 支持四个等级的资产验证：$20K、$50K、$100K、$200K
// - 处理失败时会记录详细错误日志
func (s *Service) ReceivePersonalIBTaskMsg(ctx context.Context, _ string, _ broker.Headers, in *pb.PersonalIBTaskMsg) error {
	if strings.TrimSpace(in.UserId) == "" {
		return errors.New("UserId cannot be empty or null")
	}

	l := s.log.WithContext(ctx)
	l.Infof("个人IB收到的任务为:%#v", in)

	method := s.getPersonalIBGrowthRule(in)

	err := s.HandleIdentityGradeUpgrade(ctx, in.UserId, pb.UserIdentity_PERSONAL_IB, method)
	if err != nil {
		l.Errorf("ReceivePersonalIBTaskMsg,Err:%v", err)
		return err
	}
	return nil
}

// getKolGrowthRule 获取KOL身份的成长规则处理器
// 执行步骤：
// 1. 返回一个任务调度器函数，用于处理KOL的各种任务
// 2. 根据任务类型判断任务完成状态和进度
// 3. 支持KOL之路（粉丝数量）和发布内容（发帖数量）任务
// 注意事项：
// - KOL之路任务：比较用户粉丝数与目标粉丝数
// - 发布内容任务：比较用户发帖数与目标发帖数
// - 进度值为实际数量，达到目标数量即完成任务
// - 支持多等级的粉丝和发帖数量要求
func (s *Service) getKolGrowthRule(in *pb.KolTaskMsg) TaskScheduler {
	return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
		var (
			isFinished bool
			schedule   float64
		)
		if t.Task == pb.TaskEnum_KOL_ROUTE {

			if in.FansCnt >= t.Target {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.FansCnt)
				isFinished = false
			}
		}
		if t.Task == pb.TaskEnum_PUBLISH_CONTENT {
			if in.PostsCnt >= t.Target {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.PostsCnt)
				isFinished = false
			}
		}

		return &models.TaskCompletion{
			IsFinished: isFinished,
			Schedule:   schedule,
		}
	}
}

// getServiceProviderGrowthRule 获取服务商身份的成长规则处理器
// 执行步骤：
// 1. 返回一个任务调度器函数，用于处理服务商的各种任务
// 2. 根据任务类型判断任务完成状态和进度
// 3. 支持企业员工数量和企业合作数量验证任务
// 注意事项：
// - 企业员工任务：比较企业员工数与目标员工数
// - 企业合作任务：比较合作数量与目标合作数
// - 企业认证任务已注释，可能在其他地方处理
// - 进度值为实际数量，达到目标数量即完成任务
func (s *Service) getServiceProviderGrowthRule(in *pb.EnterpriseTaskMsg) TaskScheduler {
	return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
		var (
			isFinished bool
			schedule   float64
		)

		if t.Task == pb.TaskEnum_ENTERPRISE_STAFF {
			if in.StaffCnt >= t.Target {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.StaffCnt)
				isFinished = false
			}
		}

		if t.Task == pb.TaskEnum_ENTERPRISE_COOPERATION {
			if in.CooperationCnt >= t.Target {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.CooperationCnt)
				isFinished = false
			}
		}

		return &models.TaskCompletion{
			IsFinished: isFinished,
			Schedule:   schedule,
		}
	}
}

func (s *Service) getTraderGrowthRule(in *pb.EnterpriseTaskMsg) TaskScheduler {
	return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
		var (
			isFinished bool
			schedule   float64
		)
		if t.Task == pb.TaskEnum_TRADER_ENTERPRISE_STAFF {
			if in.StaffCnt >= t.Target {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.StaffCnt)
				isFinished = false
			}
		}

		if t.Task == pb.TaskEnum_TRADER_ENTERPRISE_COOPERATION {
			if in.CooperationCnt >= t.Target {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.CooperationCnt)
				isFinished = false
			}
		}

		return &models.TaskCompletion{
			IsFinished: isFinished,
			Schedule:   schedule,
		}
	}
}

// getInvestorGrowthRule 获取投资者身份的成长规则处理器
// 执行步骤：
// 1. 返回一个任务调度器函数，用于处理投资者的各种任务
// 2. 根据任务类型判断任务完成状态和进度
// 3. 支持绑定真实账户、资产验证、实名认证、入金规模验证等任务
// 注意事项：
// - 绑定真实账户：需要真实账户标识且有交易订单
// - 账户资产验证：比较用户资产与目标金额
// - 实名认证：检查用户实名认证状态
// - 入金规模验证：验证用户入金金额是否达标
// - 进度值：资产类任务为实际金额，布尔类任务为0或1
func (s *Service) getInvestorGrowthRule(in *pb.InvestorTaskMsg) TaskScheduler {
	return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
		var (
			isFinished bool
			schedule   float64
		)
		if t.Task == pb.TaskEnum_BINDING_REAL_ACCOUNT {
			if in.RealFlag && in.HasOrder {
				isFinished = true
			}
		}
		if t.Task == pb.TaskEnum_VERIFY_ACCOUNT_ASSETS {

			if in.FundTotal >= float32(t.Target) {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.FundTotal)
				isFinished = false
			}
		}

		if t.Task == pb.TaskEnum_USER_AUTHORIZATION { //绑定账户（实盘或模拟盘）
			if in.IsRealName {
				isFinished = true
			}
		}
		if t.Task == pb.TaskEnum_VERIFY_DEPOSIT_SCALE {
			if in.FundTotal >= float32(t.Target) {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.FundTotal)
				isFinished = false
			}
		}

		return &models.TaskCompletion{
			IsFinished: isFinished,
			Schedule:   schedule,
		}
	}
}

// getPersonalIBGrowthRule 获取个人IB身份的成长规则处理器
// 执行步骤：
// 1. 返回一个任务调度器函数，用于处理个人IB的各种任务
// 2. 根据任务类型判断任务完成状态和进度
// 3. 支持账户资产验证任务和身份选择任务
// 注意事项：
// - 账户资产验证任务：比较用户资产与目标金额，达到目标即完成
// - 身份选择任务：布尔类型，切换身份时自动完成
// - 进度值：资产任务为实际资产金额，身份任务为1
// - 支持四个等级的资产验证：$20K、$50K、$100K、$200K
func (s *Service) getPersonalIBGrowthRule(in *pb.PersonalIBTaskMsg) TaskScheduler {
	return func(t *models.IdentityGradeTaskInfo) *models.TaskCompletion {
		var (
			isFinished bool
			schedule   float64
		)

		// 处理个人IB账户资产验证任务
		if t.Task == pb.TaskEnum_VERIFY_IB_ACCOUNT_ASSETS {
			if in.AccountAssets >= float32(t.Target) {
				schedule = float64(t.Target)
				isFinished = true
			} else {
				schedule = float64(in.AccountAssets)
				isFinished = false
			}
		}

		// 处理选择个人IB身份任务（布尔类型任务，默认完成）
		if t.Task == pb.TaskEnum_CHOOSE_PERSONAL_IB {
			isFinished = true
			schedule = 1
		}

		return &models.TaskCompletion{
			IsFinished: isFinished,
			Schedule:   schedule,
		}
	}
}
