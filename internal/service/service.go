package service

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	"wiki_user_center/api/common"
	v1 "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/conf"
	"wiki_user_center/internal/dao"

	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(NewGreeterService)

type Service struct {
	v1.UnimplementedServiceServer
	user             *dao.User
	userIdentity     *dao.UserIdentity
	gradeTask        *dao.GradeTask
	userTaskSchedule *dao.UserIdentityTaskSchedule
	log              *log.Helper
	business         *conf.Business
	imgformat        urlformat.Format
	banner           *dao.Banner
	identityHistory  *dao.UserIdentityHistory
	identityGrade    *dao.IdentityGrade
	userRelation     *dao.UserRelation
}

func NewGreeterService(
	user *dao.User,
	userIdentity *dao.UserIdentity,
	gradeTask *dao.GradeTask,
	userTaskSchedule *dao.UserIdentityTaskSchedule,
	logger log.Logger,
	business *conf.Business,
	banner *dao.Banner,
	identityHistory *dao.UserIdentityHistory,
	identityGrade *dao.IdentityGrade,
	userRelation *dao.UserRelation,
) *Service {
	return &Service{
		user:             user,
		userIdentity:     userIdentity,
		log:              log.NewHelper(logger),
		gradeTask:        gradeTask,
		userTaskSchedule: userTaskSchedule,
		business:         business,
		imgformat:        urlformat.NewFormat(business.ImgDomain),
		banner:           banner,
		identityHistory:  identityHistory,
		identityGrade:    identityGrade,
		userRelation:     userRelation,
	}
}

func (s *Service) Healthy(_ context.Context, _ *common.EmptyRequest) (*common.HealthyReply, error) {
	return &common.HealthyReply{
		Status: common.HealthyStatus_HealthyStatusSERVING,
	}, nil
}

func (s *Service) GetUserInfo(_ context.Context, _ *v1.GetUserInfoRequest) (*v1.GetUserInfoReply, error) {
	return &v1.GetUserInfoReply{}, nil
}

func (s *Service) StringReply(_ context.Context, _ *v1.StringReplyRequest) (*common.StringReply, error) {
	return &common.StringReply{
		Body: "success",
	}, nil
}
