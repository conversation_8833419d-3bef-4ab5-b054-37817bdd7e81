package service

import (
	"strings"
	pb "wiki_user_center/api/user_growth_center/v1"

	"git55.fxeyeinterface.com/public-projects/go-tools/urlformat"
)

// 颜色与资源映射表
var (
	// 等级通用背景色（渐变）
	_gradeBgColors = map[pb.UserGrade][]string{
		pb.UserGrade_NORMAL: {"#CDE1FF", "#F7F9FF"},
		pb.UserGrade_BRONZE: {"#FFBA97", "#FFFFFF"},
		pb.UserGrade_SILVER: {"#CCE3ED", "#F7FDFF"},
		pb.UserGrade_GOLD:   {"#FFEB96", "#FFFAE7"},
	}
	// 等级标题颜色
	_identityGradeFontColor = map[pb.UserGrade]string{
		pb.UserGrade_NORMAL: "#7588A4",
		pb.UserGrade_BRONZE: "#935434",
		pb.UserGrade_SILVER: "#75A1B1",
		pb.UserGrade_GOLD:   "#EC8703",
	}
	// Banner 背景色
	_identityBannerBg = map[pb.UserGrade]string{
		pb.UserGrade_NORMAL: "#CECECE",
		pb.UserGrade_BRONZE: "#E4A78B",
		pb.UserGrade_SILVER: "#8EB6C5",
		pb.UserGrade_GOLD:   "#F6C22F",
	}
	// 等级流程图
	_gradeProcessImg = map[pb.UserGrade]string{
		pb.UserGrade_NORMAL: "/growth_center/normal_process.webp",
		pb.UserGrade_BRONZE: "/growth_center/bronze_process.webp",
		pb.UserGrade_SILVER: "/growth_center/silver_process.webp",
		pb.UserGrade_GOLD:   "/growth_center/gold_process.webp",
	}
	// 身份规则页徽章
	_identityRuleBadge = map[pb.UserIdentity]string{
		pb.UserIdentity_INVESTOR:         "/growth_center/rule_investor.png",
		pb.UserIdentity_KOL:              "/growth_center/rule_kol.png",
		pb.UserIdentity_SERVICE_PROVIDER: "/growth_center/rule_service_provider.png",
		pb.UserIdentity_TRADER:           "/growth_center/rule_trader.png",
		pb.UserIdentity_PERSONAL_IB:      "/growth_center/ib_silver.png",
	}
	// 身份-等级浅色背景
	_identityGradeBg = map[pb.UserIdentity]map[pb.UserGrade][]string{
		pb.UserIdentity_INVESTOR: {
			pb.UserGrade_NORMAL: {"#F5F9FF"},
			pb.UserGrade_BRONZE: {"#EDF4FF"},
			pb.UserGrade_SILVER: {"#E1EBFC"},
			pb.UserGrade_GOLD:   {"#CFDEF5"},
		},
		pb.UserIdentity_KOL: {
			pb.UserGrade_NORMAL: {"#FDF9F4"},
			pb.UserGrade_BRONZE: {"#FBF2E9"},
			pb.UserGrade_SILVER: {"#FAECDE"},
			pb.UserGrade_GOLD:   {"#F8E5D3"},
		},
		pb.UserIdentity_SERVICE_PROVIDER: {
			pb.UserGrade_NORMAL: {"#FFFBEB"},
			pb.UserGrade_BRONZE: {"#FEF9E2"},
			pb.UserGrade_SILVER: {"#F4EFD8"},
			pb.UserGrade_GOLD:   {"#EFE9CC"},
		},
		pb.UserIdentity_TRADER: {
			pb.UserGrade_NORMAL: {"#F4F9FA"},
			pb.UserGrade_BRONZE: {"#E9F3F6"},
			pb.UserGrade_SILVER: {"#DEECF1"},
			pb.UserGrade_GOLD:   {"#D3E6ED"},
		},
		pb.UserIdentity_PERSONAL_IB: {
			pb.UserGrade_NORMAL: {"#F7F9FC"},
			pb.UserGrade_BRONZE: {"#F2F5FF"},
			pb.UserGrade_SILVER: {"#E6EBFC"},
			pb.UserGrade_GOLD:   {"#DCE3FA"},
		},
	}
)

// 资源路径包装
func (s *Service) asset(path string) string {
	return s.imgformat.FullPath(path, urlformat.WithTemplate(s.business.DefaultTemplate))
}

func getGradeColor(grade pb.UserGrade) []string {
	if c, ok := _gradeBgColors[grade]; ok {
		return c
	}
	return nil
}

func getIdentityGradeColor(grade pb.UserGrade) string {
	if c, ok := _identityGradeFontColor[grade]; ok {
		return c
	}
	return ""
}

func getIdentityBannerBgColor(grade pb.UserGrade) string {
	if c, ok := _identityBannerBg[grade]; ok {
		return c
	}
	return ""
}

func getIdentityGradeProcessImg(grade pb.UserGrade) string {
	if p, ok := _gradeProcessImg[grade]; ok {
		return p
	}
	return ""
}

func getBadge(identity pb.UserIdentity, grade pb.UserGrade, language, badge string) (badgeWidth float64, badgeHeight float64, resBadge string) {
	return computeEnterpriseBadge(identity, grade, language, badge, false)
}

func getShareBadge(identity pb.UserIdentity, grade pb.UserGrade, language, badge string) (badgeWidth float64, badgeHeight float64, resBadge string) {
	return computeEnterpriseBadge(identity, grade, language, badge, true)
}

// 企业身份徽章尺寸与资源选择表
var (
	_enterpriseBadgeNormalLangWidth     float64 = 141
	_enterpriseBadgeNormalHeight        float64 = 42
	_enterpriseBadgeShareLangWidth      float64 = 263
	_enterpriseBadgeShareHeight         float64 = 78
	_enterpriseBadgeNormalDefaultWidths         = map[pb.UserGrade]float64{
		pb.UserGrade_GOLD:   150,
		pb.UserGrade_NORMAL: 186,
		pb.UserGrade_BRONZE: 183,
		pb.UserGrade_SILVER: 162,
	}
	_enterpriseBadgeShareDefaultWidths = map[pb.UserGrade]float64{
		pb.UserGrade_GOLD:   274,
		pb.UserGrade_NORMAL: 334,
		pb.UserGrade_BRONZE: 334,
		pb.UserGrade_SILVER: 295,
	}
)

// shareMode=false 表示普通徽章，true 表示分享页徽章
func computeEnterpriseBadge(identity pb.UserIdentity, grade pb.UserGrade, language, badge string, shareMode bool) (badgeWidth float64, badgeHeight float64, resBadge string) {
	if identity != pb.UserIdentity_SERVICE_PROVIDER && identity != pb.UserIdentity_TRADER {
		if shareMode {
			return 355, 211, badge
		}
		return 112, 60, badge
	}

	lang := strings.ToLower(language)
	badges := strings.Split(badge, ",")

	// 简体/繁体固定宽度
	if len(badges) >= 2 && (lang == "zh-cn" || lang == "zh" || lang == "zh-hk" || lang == "zh-tw") {
		if shareMode {
			if lang == "zh-cn" {
				return _enterpriseBadgeShareLangWidth, _enterpriseBadgeShareHeight, badges[0]
			}
			return _enterpriseBadgeShareLangWidth, _enterpriseBadgeShareHeight, badges[1]
		}
		if lang == "zh-cn" {
			return _enterpriseBadgeNormalLangWidth, _enterpriseBadgeNormalHeight, badges[0]
		}
		return _enterpriseBadgeNormalLangWidth, _enterpriseBadgeNormalHeight, badges[1]
	}

	// 其他语言：按等级取宽度 + 第三张图
	if len(badges) >= 3 {
		if shareMode {
			return _enterpriseBadgeShareDefaultWidths[grade], _enterpriseBadgeShareHeight, badges[2]
		}
		return _enterpriseBadgeNormalDefaultWidths[grade], _enterpriseBadgeNormalHeight, badges[2]
	}

	// 兜底
	if shareMode {
		return _enterpriseBadgeShareLangWidth, _enterpriseBadgeShareHeight, firstOrEmpty(badges)
	}
	return _enterpriseBadgeNormalLangWidth, _enterpriseBadgeNormalHeight, firstOrEmpty(badges)
}

func firstOrEmpty(arr []string) string {
	if len(arr) > 0 {
		return arr[0]
	}
	return ""
}
