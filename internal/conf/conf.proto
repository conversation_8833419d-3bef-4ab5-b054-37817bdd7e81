syntax = "proto3";
package kratos.api;

option go_package = "wiki_user_center/internal/conf;conf";

import "google/protobuf/duration.proto";
import "common/config.proto";

message Bootstrap {
  common.ServerConfig server = 1;
  common.DataConfig data = 2;
  Business business = 3;
}

message Business {
  string img_domain =1;
  string default_template=2;
  string kol_task_topic=3;
  string enterprise_task_topic=4;
  string user_identity_topic=5;
  string investor_task_topic=6;
  repeated string kafka_address=7;
  string consume_group=8;
  string service_provider_addr=9;
  string wiki_api_addr=10;
  string log_path=11;
  string user_growth_msg_topic=12;
}