// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.3
// source: conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	common "wiki_user_center/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server   *common.ServerConfig `protobuf:"bytes,1,opt,name=server,json=server,proto3" json:"server"`
	Data     *common.DataConfig   `protobuf:"bytes,2,opt,name=data,json=data,proto3" json:"data"`
	Business *Business            `protobuf:"bytes,3,opt,name=business,json=business,proto3" json:"business"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *common.ServerConfig {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *common.DataConfig {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetBusiness() *Business {
	if x != nil {
		return x.Business
	}
	return nil
}

type Business struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImgDomain           string   `protobuf:"bytes,1,opt,name=img_domain,json=imgDomain,proto3" json:"img_domain"`
	DefaultTemplate     string   `protobuf:"bytes,2,opt,name=default_template,json=defaultTemplate,proto3" json:"default_template"`
	KolTaskTopic        string   `protobuf:"bytes,3,opt,name=kol_task_topic,json=kolTaskTopic,proto3" json:"kol_task_topic"`
	EnterpriseTaskTopic string   `protobuf:"bytes,4,opt,name=enterprise_task_topic,json=enterpriseTaskTopic,proto3" json:"enterprise_task_topic"`
	UserIdentityTopic   string   `protobuf:"bytes,5,opt,name=user_identity_topic,json=userIdentityTopic,proto3" json:"user_identity_topic"`
	InvestorTaskTopic   string   `protobuf:"bytes,6,opt,name=investor_task_topic,json=investorTaskTopic,proto3" json:"investor_task_topic"`
	KafkaAddress        []string `protobuf:"bytes,7,rep,name=kafka_address,json=kafkaAddress,proto3" json:"kafka_address"`
	ConsumeGroup        string   `protobuf:"bytes,8,opt,name=consume_group,json=consumeGroup,proto3" json:"consume_group"`
	ServiceProviderAddr string   `protobuf:"bytes,9,opt,name=service_provider_addr,json=serviceProviderAddr,proto3" json:"service_provider_addr"`
	WikiApiAddr         string   `protobuf:"bytes,10,opt,name=wiki_api_addr,json=wikiApiAddr,proto3" json:"wiki_api_addr"`
	LogPath             string   `protobuf:"bytes,11,opt,name=log_path,json=logPath,proto3" json:"log_path"`
	UserGrowthMsgTopic  string   `protobuf:"bytes,12,opt,name=user_growth_msg_topic,json=userGrowthMsgTopic,proto3" json:"user_growth_msg_topic"`
}

func (x *Business) Reset() {
	*x = Business{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Business) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Business) ProtoMessage() {}

func (x *Business) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Business.ProtoReflect.Descriptor instead.
func (*Business) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Business) GetImgDomain() string {
	if x != nil {
		return x.ImgDomain
	}
	return ""
}

func (x *Business) GetDefaultTemplate() string {
	if x != nil {
		return x.DefaultTemplate
	}
	return ""
}

func (x *Business) GetKolTaskTopic() string {
	if x != nil {
		return x.KolTaskTopic
	}
	return ""
}

func (x *Business) GetEnterpriseTaskTopic() string {
	if x != nil {
		return x.EnterpriseTaskTopic
	}
	return ""
}

func (x *Business) GetUserIdentityTopic() string {
	if x != nil {
		return x.UserIdentityTopic
	}
	return ""
}

func (x *Business) GetInvestorTaskTopic() string {
	if x != nil {
		return x.InvestorTaskTopic
	}
	return ""
}

func (x *Business) GetKafkaAddress() []string {
	if x != nil {
		return x.KafkaAddress
	}
	return nil
}

func (x *Business) GetConsumeGroup() string {
	if x != nil {
		return x.ConsumeGroup
	}
	return ""
}

func (x *Business) GetServiceProviderAddr() string {
	if x != nil {
		return x.ServiceProviderAddr
	}
	return ""
}

func (x *Business) GetWikiApiAddr() string {
	if x != nil {
		return x.WikiApiAddr
	}
	return ""
}

func (x *Business) GetLogPath() string {
	if x != nil {
		return x.LogPath
	}
	return ""
}

func (x *Business) GetUserGrowthMsgTopic() string {
	if x != nil {
		return x.UserGrowthMsgTopic
	}
	return ""
}

var File_conf_conf_proto protoreflect.FileDescriptor

var file_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x93, 0x01, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70,
	0x12, 0x2c, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x26,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x22, 0xfe, 0x03, 0x0a, 0x08, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x67, 0x5f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x67, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x6b, 0x6f, 0x6c, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6b, 0x6f, 0x6c, 0x54, 0x61, 0x73, 0x6b,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x6b, 0x61, 0x66,
	0x6b, 0x61, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x6b, 0x61, 0x66, 0x6b, 0x61, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x77, 0x69, 0x6b, 0x69, 0x5f,
	0x61, 0x70, 0x69, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x77, 0x69, 0x6b, 0x69, 0x41, 0x70, 0x69, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6c,
	0x6f, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c,
	0x6f, 0x67, 0x50, 0x61, 0x74, 0x68, 0x12, 0x31, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x75, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x4d, 0x73, 0x67, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x42, 0x25, 0x5a, 0x23, 0x77, 0x69, 0x6b,
	0x69, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e, 0x66,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_conf_proto_rawDescOnce sync.Once
	file_conf_conf_proto_rawDescData = file_conf_conf_proto_rawDesc
)

func file_conf_conf_proto_rawDescGZIP() []byte {
	file_conf_conf_proto_rawDescOnce.Do(func() {
		file_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_conf_proto_rawDescData)
	})
	return file_conf_conf_proto_rawDescData
}

var file_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),           // 0: kratos.api.Bootstrap
	(*Business)(nil),            // 1: kratos.api.Business
	(*common.ServerConfig)(nil), // 2: common.ServerConfig
	(*common.DataConfig)(nil),   // 3: common.DataConfig
}
var file_conf_conf_proto_depIdxs = []int32{
	2, // 0: kratos.api.Bootstrap.server:type_name -> common.ServerConfig
	3, // 1: kratos.api.Bootstrap.data:type_name -> common.DataConfig
	1, // 2: kratos.api.Bootstrap.business:type_name -> kratos.api.Business
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_conf_conf_proto_init() }
func file_conf_conf_proto_init() {
	if File_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Business); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_conf_proto_goTypes,
		DependencyIndexes: file_conf_conf_proto_depIdxs,
		MessageInfos:      file_conf_conf_proto_msgTypes,
	}.Build()
	File_conf_conf_proto = out.File
	file_conf_conf_proto_rawDesc = nil
	file_conf_conf_proto_goTypes = nil
	file_conf_conf_proto_depIdxs = nil
}
