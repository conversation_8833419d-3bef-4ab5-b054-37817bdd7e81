package conf

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"git55.fxeyeinterface.com/public-projects/go-tools/config"
)

func TestLoadConfig(t *testing.T) {
	os.Setenv("APOLLO_APP_ID", "wiki-user-center")
	os.Setenv("APOLLO_ENDPOINT", "http://apolloconfig.fxeyeinterface.com:8080")
	os.Setenv("APOLLO_NAMESPACE", "wiki-user-center.yaml")
	os.Setenv("APOLLO_SECRET", "c9c656cdded84e669a8ba6abcc9a21ad")
	os.Setenv("APOLLO_CLUSTER", "DEV")

	var bc Bootstrap
	err := config.LoadConfig(&bc)
	if err != nil {
		panic(err)
	}

	str, _ := json.Marshal(bc)
	fmt.Println(string(str))
}
