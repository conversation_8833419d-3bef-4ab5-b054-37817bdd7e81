package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"wiki_user_center/internal/models"
)

type UserIdentity struct {
	Mysql *igorm.DB
	Redis *redis.Client
}

func NewUserIdentity(mysql *igorm.DB, redis *redis.Client) *UserIdentity {
	return &UserIdentity{Mysql: mysql, Redis: redis}
}

func (p *UserIdentity) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return p.Mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (p *UserIdentity) Save(ctx context.Context, data *models.SaveUserIdentityModel, opts ...igorm.Option) error {
	var (
		tx  = p.Session(ctx, opts...).Begin().WithContext(ctx)
		err error
	)

	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = tx.Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{"Identity_Grade_Id", "In_Process_Identity_Grade_Id", "Acquire_Time", "Update_Time", "Status"}),
	}).Save(data.UserIdentity).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	if len(data.UserTaskSchedule) > 0 {
		err = tx.Save(data.UserTaskSchedule).Error
		if err != nil {
			return ormhelper.WrapErr(err)
		}
	}
	if len(data.UserRelation) > 0 {
		err = tx.Save(data.UserRelation).Error
		if err != nil {
			return ormhelper.WrapErr(err)
		}
	}

	if len(data.UserHistory) > 0 {
		err = tx.Create(data.UserHistory).Error
		if err != nil {
			return ormhelper.WrapErr(err)
		}
	}
	return nil
}

func (p *UserIdentity) FindIdentityRule(ctx context.Context, userId string, opts ...igorm.Option) ([]*models.IdentityGradeRule, error) {
	var out []*models.IdentityGradeRule
	err := p.Session(ctx, opts...).
		Table(" growthcenter_identity_grade i").
		Joins("left join growthcenter_identity_task_association ass on i.Id=ass.Identity_Grade_Id").
		Joins("left join growthcenter_grade_task tsk on ass.Grade_Task_Id=tsk.Id AND tsk.Is_Enable=1").
		Joins("left join growthcenter_user_identity u on i.Id=u.Identity_Grade_Id AND u.User_Id=?", userId).
		Where("i.Is_Enable=1 ").
		Order("i.Identity,i.Grade,ass.`Order` asc").
		Select([]string{
			"i.Identity",
			"i.Grade",
			"i.Badge",
			"tsk.ID AS Task_Id",
			"tsk.Type",
			"tsk.Content",
			"tsk.Target",
			"tsk.Unit",
			"tsk.Jump_Address",
			"u.Identity_Grade_Id",
		}).Find(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (p *UserIdentity) GetUserShare(ctx context.Context, userId string, opts ...igorm.Option) (*models.UserShareInfo, error) {
	var out *models.UserShareInfo
	err := p.Session(ctx, opts...).
		Table("growthcenter_user_identity as i").
		Joins("inner join growthcenter_identity_grade as g on i.Identity_Grade_Id=g.ID AND i.User_Id=?", userId).
		Select([]string{
			"i.User_Id",
			"i.Acquire_Time",
			"g.Identity",
			"g.Grade",
			"g.Share_Badge",
		}).First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}

	return out, nil
}

func (p *UserIdentity) GetUserIdentityGrade(ctx context.Context, userId string, opts ...igorm.Option) (*models.IdentityGrade, error) {
	var out *models.IdentityGrade

	err := p.Session(ctx, opts...).
		Table("growthcenter_user_identity u").
		Joins("inner join growthcenter_identity_grade g on u.Identity_Grade_Id=g.Id").
		Where("User_Id=? AND Status=1", userId).
		Select("g.*").
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (p *UserIdentity) GetUserIdentity(ctx context.Context, userId string, opts ...igorm.Option) (*models.UserIdentity, error) {
	var out *models.UserIdentity
	err := p.Session(ctx, opts...).Where("User_Id=?", userId).First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

/*
func (p *UserIdentity) FindUserIdentityDetail(ctx context.Context, userId string, opts ...igorm.Option) ([]*models.UserIdentityDetail, error) {
	var out []*models.UserIdentityDetail
	err := p.Session(ctx, opts...).
		Table("growthcenter_user_identity as u").
		Joins("inner join growthcenter_identity_grade as g on u.Identity_Grade_Id=g.ID AND u.User_Id=?", userId).
		Joins(" left join growthcenter_identity_task_association ass on u.Identity_Grade_Id=ass.Identity_Grade_Id").
		Joins(" left join growthcenter_grade_task tsk on ass.Grade_Task_Id=tsk.Id AND tsk.Is_Enable=1").
		Joins(" left join growthcenter_user_identity_task_schedule sch on u.User_Id=sch.User_Id AND tsk.Id=sch.ID").
		Order("i.Identity,i.Grade asc").
		Select([]string{
			"u.User_Id",
			"g.Identity",
			"g.Grade",
			"g.Badge",
			"tsk.ID AS Task_Id",
			"tsk.Type",
			"tsk.Content",
			"tsk.Target",
			"tsk.Jump_Address",
			"sch.Schedule",
			"sch.Is_Finish",
		}).Find(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, err
}

*/
