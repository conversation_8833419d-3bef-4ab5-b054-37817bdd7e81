package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	v1 "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/models"
)

type UserIdentityTaskSchedule struct {
	mysql *igorm.DB
	redis *redis.Client
}

func (s *UserIdentityTaskSchedule) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return s.mysql.NewOptions(opts...).Session().WithContext(ctx)
}

/*
func (s *UserIdentityTaskSchedule) FindUserTaskScheduleWithIdentityGrade(ctx context.Context, userId string, identity v1.UserIdentity, grade v1.UserGrade, opts ...igorm.Option) ([]*models.UserIdentityTaskSchedule, error) {
	var out []*models.UserIdentityTaskSchedule

	err := s.Session(ctx, opts...).Where("User_Id=? AND Identity=? AND Grade=?", userId, identity, grade).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
*/

func (s *UserIdentityTaskSchedule) FindUserTaskScheduleWithIdentity(ctx context.Context, userId string, identity v1.UserIdentity, opts ...igorm.Option) ([]*models.UserIdentityTaskSchedule, error) {
	var out []*models.UserIdentityTaskSchedule

	err := s.Session(ctx, opts...).Where("User_Id=? AND Identity=? ", userId, identity).Order("Grade Desc").Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *UserIdentityTaskSchedule) FindUserTaskScheduleWithGradeId(ctx context.Context, userId, identityGradeId string, opts ...igorm.Option) ([]*models.UserIdentityTaskSchedule, error) {
	var out []*models.UserIdentityTaskSchedule

	err := s.Session(ctx, opts...).Where("User_Id=? AND Identity_Grade_Id=? ", userId, identityGradeId).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *UserIdentityTaskSchedule) FindUserTaskSchedule(ctx context.Context, userId string, opts ...igorm.Option) ([]*models.UserIdentityTaskSchedule, error) {
	var out []*models.UserIdentityTaskSchedule

	err := s.Session(ctx, opts...).Where("User_Id=?", userId).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func NewUserIdentityTaskSchedule(mysql *igorm.DB, redis *redis.Client) *UserIdentityTaskSchedule {
	return &UserIdentityTaskSchedule{mysql: mysql, redis: redis}
}
