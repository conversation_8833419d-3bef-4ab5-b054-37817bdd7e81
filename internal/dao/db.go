package dao

import (
	"errors"
	"time"
	"wiki_user_center/api/common"

	"git55.fxeyeinterface.com/public-projects/go-tools/env"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/iredis"
	"github.com/go-kratos/kratos/v2/log"
	redis "github.com/go-redis/redis/v8"
	goCache "github.com/liyanbing/go-cache"
	redisCache "github.com/liyanbing/go-cache/cacher/redis"
)

func NewMySQL(c *common.DataConfig, logger log.Logger) (*igorm.DB, func(), error) {
	database := c.Database
	if database == nil {
		return nil, nil, errors.New("empty database config")
	}

	mysqlClient, err := igorm.NewGORM(&igorm.Config{
		LogLevel:    int(database.Level),
		MaxOpen:     int(database.MaxOpen),
		MaxIdle:     int(database.MaxIdle),
		MaxLifeTime: time.Duration(database.MaxLifeTimeSeconds) * time.Second,
		Source:      c.Database.Source,
	}, logger)
	if err != nil {
		return nil, nil, err
	}

	return mysqlClient, func() {
		mysqlClient.Close()
	}, nil
}

func NewRedis(c *common.DataConfig, logger log.Logger) (*redis.Client, func(), error) {
	redisClient, err := iredis.NewClient(&iredis.Config{
		Address:  c.Redis.Address,
		Password: c.Redis.Password,
		DB:       int(c.Redis.Db),
		MaxIdle:  int(c.Redis.MaxIdle),
	}, logger)
	if err != nil {
		return nil, nil, err
	}

	return redisClient, func() {
		redisClient.Close()
	}, nil
}

func NewRedisCache(redisClient *redis.Client) (goCache.Cache, error) {
	cache := redisCache.NewRedisCache(redisClient)
	cache.SetNamespace(env.GetServiceName())
	return cache, nil
}
