package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"wiki_user_center/internal/models"
)

type Banner struct {
	mysql *igorm.DB
	redis *redis.Client
}

func (s *Banner) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return s.mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (s *Banner) FindBanner(ctx context.Context, language string, opts ...igorm.Option) ([]*models.Banner, error) {
	var out []*models.Banner
	err := s.Session(ctx, opts...).Where("status=1 AND language_code=? ", language).Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func NewBanner(mysql *igorm.DB, redis *redis.Client) *Banner {
	return &Banner{mysql: mysql, redis: redis}
}
