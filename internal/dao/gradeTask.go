package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	pb "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/models"
)

type GradeTask struct {
	mysql *igorm.DB
	Redis *redis.Client
}

func (p *GradeTask) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return p.mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (p *GradeTask) FindTaskWithIdentity(ctx context.Context, identity pb.UserIdentity, opts ...igorm.Option) ([]*models.IdentityGradeTaskInfo, error) {
	db := p.Session(ctx, opts...)
	var out []*models.IdentityGradeTaskInfo
	err := db.Table("growthcenter_identity_grade i ").
		Joins("inner join growthcenter_identity_task_association as ass on i.Id=ass.Identity_Grade_Id").
		Joins("left join growthcenter_grade_task tsk on ass.Grade_Task_Id=tsk.Id").
		Where("i.Identity=? AND i.Is_Enable=1", identity).
		Select("tsk.*,i.Identity,i.Grade,i.Badge,i.Id as Identity_Grade_Id,i.Banner,i.Mode").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (p *GradeTask) FindTaskWithGradeId(ctx context.Context, gradeId string, opts ...igorm.Option) ([]*models.IdentityGradeTaskInfo, error) {
	db := p.Session(ctx, opts...)
	var out []*models.IdentityGradeTaskInfo
	err := db.Table("growthcenter_identity_grade i ").
		Joins("inner join growthcenter_identity_task_association as ass on i.Id=ass.Identity_Grade_Id").
		Joins("left join growthcenter_grade_task tsk on ass.Grade_Task_Id=tsk.Id").
		Where("i.Id=? AND i.Is_Enable=1", gradeId).
		Select("tsk.*,i.Identity,i.Grade,i.Badge,i.Id as Identity_Grade_Id,i.Banner,i.Mode").
		Order("ass.Order asc").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func NewGradeTask(mysql *igorm.DB, redis *redis.Client) *GradeTask {
	return &GradeTask{mysql: mysql, Redis: redis}
}
