package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"gorm.io/gorm"
	"wiki_user_center/internal/models"
)

type UserRelation struct {
	Mysql *igorm.DB
}

func NewUserRelation(mysql *igorm.DB) *UserRelation {
	return &UserRelation{Mysql: mysql}
}

func (p *UserRelation) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return p.Mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (p *UserRelation) GetUserRelationWithObjectCode(ctx context.Context, uid string, objectCode string, opts ...igorm.Option) (*models.UserRelation, error) {
	var out *models.UserRelation
	err := p.Session(ctx, opts...).Where("User_Id=? AND Object_Code=?", uid, objectCode).First(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
func (p *UserRelation) GetEnabledUserRelation(ctx context.Context, uid string, opts ...igorm.Option) (*models.UserRelation, error) {
	var out *models.UserRelation
	err := p.Session(ctx, opts...).Where("User_Id=? AND Status=1", uid).First(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
