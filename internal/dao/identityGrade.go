package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	pb "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/models"
)

type IdentityGrade struct {
	mysql *igorm.DB
	Redis *redis.Client
}

func (p *IdentityGrade) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return p.mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (p *IdentityGrade) FindWithIdentity(ctx context.Context, identity pb.UserIdentity, opts ...igorm.Option) ([]*models.IdentityGrade, error) {
	var out []*models.IdentityGrade
	err := p.Session(ctx, opts...).Where("Identity=? AND Is_Enable=1", identity).Order("Grade asc").Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func NewIdentityGrade(mysql *igorm.DB, redis *redis.Client) *IdentityGrade {
	return &IdentityGrade{mysql: mysql, Redis: redis}
}
