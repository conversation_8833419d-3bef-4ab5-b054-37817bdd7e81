package dao

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/igorm"
	"git55.fxeyeinterface.com/public-projects/go-tools/ormhelper"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	v1 "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/models"
)

type UserIdentityHistory struct {
	mysql *igorm.DB
	redis *redis.Client
}

func (s *UserIdentityHistory) Session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return s.mysql.NewOptions(opts...).Session().WithContext(ctx)
}

func (s *UserIdentityHistory) GetUserHistory(ctx context.Context, userId string, identity v1.UserIdentity, opts ...igorm.Option) (*models.UserIdentityHistory, error) {
	var out *models.UserIdentityHistory
	err := s.Session(ctx, opts...).Where("User_Id=? AND Identity=?", userId, identity).Order("Create_Time desc, Grade desc").Limit(1).First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func NewUserIdentityHistory(mysql *igorm.DB, redis *redis.Client) *UserIdentityHistory {
	return &UserIdentityHistory{mysql: mysql, redis: redis}
}
