package server

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/tx7do/kratos-transport/transport/kafka"
	"wiki_user_center/internal/conf"
	"wiki_user_center/internal/service"
)

func NewKafkaServer(cfg *conf.Business, _ log.Logger, svc *service.Service) *kafka.Server {
	ctx := context.Background()

	fmt.Println(fmt.Printf("kafka地址：%v", cfg.KafkaAddress))
	srv := kafka.NewServer(
		kafka.WithAddress(cfg.KafkaAddress),
		kafka.WithGlobalTracerProvider(),
		kafka.WithGlobalPropagator(),
		kafka.WithCodec("json"),
	)
	err = kafka.RegisterSubscriber(srv, ctx,
		cfg.UserGrowthMsgTopic, cfg.ConsumeGroup, false,
		svc.HandleGrowthMsg,
	)
	if err != nil {
		log.Errorf("kafka.RegisterSubscriber err: %v", err)
		return nil
	}

	return srv
}
