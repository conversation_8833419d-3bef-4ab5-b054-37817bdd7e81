package server

import (
	"context"
	"wiki_user_center/internal/conf"
	"wiki_user_center/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tx7do/kratos-transport/transport/kafka"
)

func NewKafkaServer(cfg *conf.Business, logger log.Logger, svc *service.Service) *kafka.Server {
	ctx := context.Background()
	helper := log.NewHelper(logger)

	helper.Infof("kafka地址：%v", cfg.KafkaAddress)
	srv := kafka.NewServer(
		kafka.WithAddress(cfg.KafkaAddress),
		kafka.WithGlobalTracerProvider(),
		kafka.WithGlobalPropagator(),
		kafka.WithCodec("json"),
	)
	if err := kafka.RegisterSubscriber(srv, ctx,
		cfg.UserGrowthMsgTopic, cfg.ConsumeGroup, false,
		svc.HandleGrowthMsg,
	); err != nil {
		helper.Errorf("kafka.RegisterSubscriber err: %v", err)
		return nil
	}

	return srv
}
