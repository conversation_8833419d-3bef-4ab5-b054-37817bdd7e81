package server

import (
	"time"
	"wiki_user_center/api/common"
	v1 "wiki_user_center/api/user_growth_center/v1"
	"wiki_user_center/internal/service"

	"git55.fxeyeinterface.com/public-projects/go-tools/env"
	"git55.fxeyeinterface.com/public-projects/go-tools/ilog"
	"git55.fxeyeinterface.com/public-projects/go-tools/metrics"
	"github.com/go-kratos/kratos/v2/log"
	mmd "github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
)

func NewGRPCServer(c *common.ServerConfig, svc *service.Service, logger log.Logger) *grpc.Server {
	var opts = []grpc.ServerOption{
		grpc.Middleware(
			tracing.Server(),
			recovery.Recovery(),
			mmd.Server(),
			ilog.LoggingGRPC(),
			metrics.ServerMetricsMiddleware(env.GetServiceName()),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.TimeoutSeconds > 0 {
		opts = append(opts, grpc.Timeout(time.Duration(c.Grpc.TimeoutSeconds)*time.Second))
	}
	srv := grpc.NewServer(opts...)
	v1.RegisterServiceServer(srv, svc)
	return srv
}
