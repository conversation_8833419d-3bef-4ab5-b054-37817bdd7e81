package server

import (
	"context"
	"git55.fxeyeinterface.com/public-projects/go-tools/icontext"
	"git55.fxeyeinterface.com/public-projects/go-tools/iheader"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"strings"
)

func parseHeaders() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			tr, ok := transport.FromServerContext(ctx)
			if !ok {
				return handler(ctx, req)
			}

			value := tr.RequestHeader().Get(iheader.ForwardForHeaderKey)
			splits := strings.Split(value, ",")

			// 客户端ip
			if len(splits) > 0 {
				ctx = icontext.WithClientIP(ctx, splits[0])
			}

			// 用户ID
			ctx = icontext.WithUserId(ctx, tr.RequestHeader().Get(iheader.UserIdHeader<PERSON>ey))

			// basicData
			basicData := tr.RequestHeader().Get(iheader.BasicDataHeaderKey)
			splits = strings.Split(basicData, ",")

			// 平台
			if len(splits) > 0 {
				var plat icontext.Platform
				switch splits[0] {
				case "0":
					plat = icontext.IOS
				case "1":
					plat = icontext.Android
				case "3":
					plat = icontext.PC
				default:
					plat = icontext.Web
				}

				ctx = icontext.WithAppPlatform(ctx, plat)
			}

			// app id
			if len(splits) > 1 { // nolint:gomnd
				ctx = icontext.WithAppId(ctx, splits[1])
			}

			// 类型
			if len(splits) > 2 { // nolint:gomnd
				ctx = icontext.WithProjectType(ctx, splits[2])
			}

			// version
			if len(splits) > 3 { // nolint:gomnd
				ctx = icontext.WithAppVersion(ctx, splits[3])
			}

			countryCode := tr.RequestHeader().Get(iheader.CountryCodeHeaderKey)
			if countryCode != "" {
				// 城市码
				ctx = icontext.WithCountryCode(ctx, countryCode)

				// 区域码
				//ctx = icontext.WithAreaCode(ctx, svc.BaseDomain.GetAreaCodeByCountry(ctx, countryCode))

				// 二字区域码
				//	ctx = icontext.WithTwoAreaCode(ctx, svc.BaseDomain.GetTwoAreaCodeByCountry(ctx, countryCode))
			}

			// 语言code
			languageCode := tr.RequestHeader().Get(iheader.LanguageCodeHeaderKeyOld)
			if languageCode == "zh" {
				languageCode = "zh-HK"
			}
			ctx = icontext.WithLanguageCode(ctx, languageCode)

			// 偏好语言
			ctx = icontext.WithPreferredLanguageCode(ctx, tr.RequestHeader().Get(iheader.PreferredLanguageHeaderKey))
			return handler(ctx, req)
		}
	}
}
