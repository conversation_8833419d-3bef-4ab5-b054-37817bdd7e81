package models

import (
	"fmt"
	"time"
)

type UserRelation struct {
	ID         string     `json:"id" gorm:"column:Id"`                                            // 主键
	UserId     string     `json:"userId" gorm:"column:User_Id"`                                   // 用户ID
	ObjectCode string     `json:"objectCode" gorm:"column:Object_Code"`                           // 对象Code
	ObjectType int        `json:"objectType" gorm:"column:Object_Type"`                           // 对象类型 1 个人 2 企业
	Status     int        `json:"status" gorm:"Status"`                                           // 状态 0 未关联  1 关联
	CreateTime time.Time  `json:"createTime" gorm:"column:Create_Time"`                           // 创建时间
	UpdateTime *time.Time `json:"updateTime" gorm:"column:Update_Time;default:CURRENT_TIMESTAMP"` // 更新时间
}

func (UserRelation) TableName() string {
	return UserRelationTableName
}

func (UserRelation) GetId() string {
	time.Sleep(time.Millisecond)
	return fmt.Sprintf("UR%d", time.Now().UTC().UnixMicro())
}
