package models

import (
	"time"
	"wiki_user_center/api/user_growth_center/v1"
)

// IdentityGrade
// 身份等级配置表
type IdentityGrade struct {
	ID         string               `json:"id" gorm:"column:Id"`                  // 主键
	Identity   v1.UserIdentity      `json:"identity" gorm:"column:Identity"`      // 身份 1 投资者 2 KOL 3 服务商
	Grade      v1.UserGrade         `json:"grade" gorm:"column:Grade"`            // 等级 1 普通 2 青铜 3 白银 4 黄金
	IsEnable   bool                 `json:"isEnable" gorm:"column:Is_Enable"`     // 是否启用
	Mode       v1.IdentityGradeMode `json:"mode" gorm:"column:Mode"`              // 任务模式, 1 全部完成  2 任意一个完成
	Banner     string               `json:"banner" gorm:"column:Banner"`          // 等级banner图
	Badge      string               `json:"badge" gorm:"column:Badge"`            // 徽章
	UserBadge  string               `json:"userBadge" gorm:"column:User_Badge"`   // 用户中心显示徽章
	ShareBadge string               `json:"shareBadge" gorm:"column:Share_Badge"` // 分享徽章
	CreateTime time.Time            `json:"createTime" gorm:"column:Create_Time"` // 创建时间
	Creator    string               `json:"creator" gorm:"column:Creator"`        // 创建人
	UpdateTime time.Time            `json:"updateTime" gorm:"UpdateTime"`         // 修改时间
	Updater    string               `json:"updater" gorm:"column:Updater"`        // 修改人
}

func (IdentityGrade) TableName() string {
	return IdentityGradeTableName
}

type IdentityGradeRule struct {
	Identity        v1.UserIdentity `json:"Identity" gorm:"column:Identity"`
	Grade           v1.UserGrade    `json:"Grade" gorm:"column:Grade"`
	Badge           string          `json:"Badge" gorm:"column:Badge"`
	TaskId          string          `json:"Task_Id" gorm:"column:Task_Id"`
	Type            int             `json:"Type" gorm:"column:Type"`
	Content         string          `json:"Content" gorm:"column:Content"`
	Target          float64         `json:"Target" gorm:"column:Target"`
	Unit            string          `json:"Unit" gorm:"column:Unit"`
	JumpAddress     string          `json:"Jump_Address" gorm:"column:Jump_Address"`
	IdentityGradeId string          `json:"Identity_Grade_Id" gorm:"column:Identity_Grade_Id"`
}
