package models

import (
	"fmt"
	"time"
	v1 "wiki_user_center/api/user_growth_center/v1"
)

type UserIdentityHistory struct {
	ID              string          `json:"id" gorm:"column:Id"`                             // 主键
	UserId          string          `json:"userId" gorm:"column:User_Id"`                    // 用户ID
	Identity        v1.UserIdentity `json:"identity" gorm:"column:Identity"`                 // 身份
	Grade           v1.UserGrade    `json:"grade" gorm:"column:Grade"`                       // 等级
	IdentityGradeId string          `json:"identityGradeId" gorm:"column:Identity_Grade_Id"` // 身份等级ID
	CreateTime      time.Time       `json:"createTime" gorm:"column:Create_Time"`            // 创建时间
}

func (UserIdentityHistory) TableName() string {
	return UserIdentityHistoryTableName
}

func (UserIdentityHistory) GetId() string {
	time.Sleep(time.Millisecond)
	return fmt.Sprintf("UIH%d", time.Now().UTC().UnixMicro())
}
