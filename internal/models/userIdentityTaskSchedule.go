package models

import (
	"fmt"
	"time"
	v1 "wiki_user_center/api/user_growth_center/v1"
)

type UserIdentityTaskSchedule struct {
	ID              string          `json:"id" gorm:"column:Id"`                             // 主键
	UserId          string          `json:"userId" gorm:"column:User_Id"`                    // 用户ID
	Identity        v1.UserIdentity `json:"identity" gorm:"column:Identity"`                 // 身份
	Grade           v1.UserGrade    `json:"grade" gorm:"column:Grade"`                       // 等级
	IdentityGradeId string          `json:"identityGradeId" gorm:"column:Identity_Grade_Id"` // 身份等级ID
	GradeTaskId     string          `json:"gradeTaskId" gorm:"column:Grade_Task_Id"`         // 等级任务ID
	Schedule        float64         `json:"schedule" gorm:"column:Schedule"`                 // 当前进度(数值类型)
	IsFinish        bool            `json:"IsFinish" gorm:"column:Is_Finish"`                // 是否完成
	CreateTime      time.Time       `json:"createTime" gorm:"column:Create_Time"`            // 创建时间
	UpdateTime      time.Time       `json:"updateTime" gorm:"column:Update_Time"`            // 更新时间
}

func (UserIdentityTaskSchedule) TableName() string {
	return UserIdentityTaskScheduleTableName
}

func (UserIdentityTaskSchedule) GetId() string {
	time.Sleep(time.Millisecond)
	return fmt.Sprintf("UITS%d", time.Now().UTC().UnixMicro())
}
