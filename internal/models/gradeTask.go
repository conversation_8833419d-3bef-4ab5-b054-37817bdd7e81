package models

import (
	"time"
	v1 "wiki_user_center/api/user_growth_center/v1"
)

type GradeTask struct {
	ID          string                   `json:"id" gorm:"column:Id"`                         // 主键
	Type        int32                    `json:"type" gorm:"column:Type"`                     //类型 1 bool 2 数值型
	Task        v1.TaskEnum              `json:"task" gorm:"column:Task"`                     // 任务
	Name        string                   `json:"name" gorm:"column:Name"`                     // 任务中文名称，例如：绑定真实账户,账户资产验证
	Content     string                   `json:"content" gorm:"column:Content"`               // 任务内容,例如:绑定盈利中MT4/MT5账户
	Target      int64                    `json:"target" gorm:"column:Target"`                 // 数值型任务目标
	Unit        string                   `json:"unit" gorm:"column:Unit"`                     //单位,例如：$
	JumpAddress v1.UserGrowthJumpAddress `json:"jumpAddress" gorm:"column:Jump_Address"`      // 跳转地址
	IsEnable    bool                     `json:"is_enable" gorm:"column:Is_Enable DEFAULT 1"` // 是否启用
	Creator     string                   `json:"creator" gorm:"column:Creator"`               // 创建人
	CreateTime  time.Time                `json:"createTime" gorm:"column:Create_Time"`        // 创建时间
	Updater     string                   `json:"updater" gorm:"column:Updater"`               // 更新人
	UpdateTime  time.Time                `json:"updateTime" gorm:"column:Update_Time"`        // 更新时间
	Label       string                   `json:"label" gorm:"column:label"`                   // 任务内容，详情展示
}

func (GradeTask) TableName() string {
	return GradTaskTableName
}

type TaskCompletion struct {
	IsFinished bool
	Schedule   float64
}

type IdentityGradeTaskInfo struct {
	GradeTask
	Identity        v1.UserIdentity      `json:"identity" gorm:"column:Identity"`
	Grade           v1.UserGrade         `json:"grade" gorm:"column:Grade"`
	Mode            v1.IdentityGradeMode `json:"mode" gorm:"column:Mode"`
	Badge           string               `json:"badge" gorm:"column:Badge"`
	Banner          string               `json:"banner" gorm:"column:Banner"`
	IdentityGradeId string               `json:"identityGradeId" gorm:"column:Identity_Grade_Id"`
	Order           int                  `json:"order" gorm:"column:Order"`
}
