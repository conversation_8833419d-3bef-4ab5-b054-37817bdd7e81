package models

import "time"

type IdentityTaskAssociation struct {
	ID              string    `json:"id" gorm:"column:Id"`                             // 主键
	IdentityGradeId string    `json:"identityGradeId" gorm:"column:Identity_Grade_Id"` // 身份等级ID
	GradeTaskId     string    `json:"gradeTaskId" gorm:"column:Grade_Task_Id"`         // 等级任务ID
	Order           int64     `json:"order" gorm:"column:Order"`                       // 排序号
	CreateTime      time.Time `json:"createTime" gorm:"column:Create_Time"`            // 创建时间

}

func (IdentityTaskAssociation) TableName() string {
	return IdentityTaskAssociationTableName
}
