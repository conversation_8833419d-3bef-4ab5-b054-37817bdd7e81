package models

import (
	"fmt"
	"time"
	pb "wiki_user_center/api/user_growth_center/v1"
)

type UserIdentity struct {
	ID              string `json:"id" gorm:"column:Id;primary_key"`                 // 主键
	UserId          string `json:"userId" gorm:"column:User_Id"`                    // 用户Id
	IdentityGradeId string `json:"identityGradeId" gorm:"column:Identity_Grade_Id"` // 已经获得的身份等级ID
	//InProcessIdentityGradeId string    `json:"inProcessIdentityGradeId" gorm:"column:In_Process_Identity_Grade_Id"` // 正在进行升级的身份等级ID
	Status      int       `json:"status" gorm:"column:Status"`            // 0 无效 1 生效
	AcquireTime time.Time `json:"acquireTime" gorm:"column:Acquire_Time"` // 身份获得时间
	CreateTime  time.Time `json:"createTime" gorm:"column:Create_Time"`   //创建时间
	UpdateTime  time.Time `json:"updateTime" gorm:"column:Update_Time"`   // 更新时间
}

func (UserIdentity) TableName() string {
	return UserIdentityTableName
}

func (UserIdentity) GetId() string {
	time.Sleep(time.Millisecond)
	return fmt.Sprintf("UI%d", time.Now().UTC().UnixMicro())
}

// UserShareInfo
// 用户分享图
type UserShareInfo struct {
	UserId     string `json:"User_Id" gorm:"column:User_Id"`
	NickName   string `json:"nickName" gorm:"column:NickName"`
	Avatar     string `json:"avatar" gorm:"column:Avatar"`
	ShareBadge string `json:"shareBadge" gorm:"column:Share_Badge"`
	//Badge       string          `json:"badge" gorm:"column:Badge"`
	Identity    pb.UserIdentity `json:"identity" gorm:"column:Identity"`
	Grade       pb.UserGrade    `json:"grade" gorm:"column:Grade"`
	AcquireTime time.Time       `json:"Acquire_Time" gorm:"column:Acquire_Time"`
}

type UserIdentityDetail struct {
	UserId      string                   `json:"userId" gorm:"column:User_Id"`
	Identity    pb.UserIdentity          `json:"identity" gorm:"column:Identity"`
	Grade       pb.UserGrade             `json:"grade" gorm:"column:Grade"`
	Badge       string                   `json:"badge" gorm:"Grade"`
	TaskId      string                   `json:"taskId" gorm:"column:Task_Id"`
	Type        int                      `json:"type" gorm:"column:Type"`
	Content     string                   `json:"content" gorm:"column:Content"`
	Target      float64                  `json:"target" gorm:"column:Target"`
	JumpAddress pb.UserGrowthJumpAddress `json:"jumpAddress" gorm:"column:Jump_Address"`
	Schedule    float64                  `json:"schedule" gorm:"column:Schedule"`
	IsFinish    bool                     `json:"isFinish" gorm:"column:Is_Finish"`
}

type SaveUserIdentityModel struct {
	UserIdentity
	UserHistory      []*UserIdentityHistory
	UserTaskSchedule []*UserIdentityTaskSchedule
	UserRelation     []*UserRelation
}
