package models

import (
	"time"
	v1 "wiki_user_center/api/user_growth_center/v1"
)

// Banner
// 成长中心轮播图
type Banner struct {
	Id            string                   `json:"id" gorm:"column:id"`                        // 主键
	Name          string                   `json:"name" gorm:"column:name"`                    // 轮播图名称
	Position      int                      `json:"position" gorm:"column:position"`            // 轮播图位置
	LanguageCode  string                   `json:"languageCode" gorm:"column:language_code"`   // 语言
	IdentityLevel int                      `json:"identityLevel" gorm:"column:identity_level"` // 身份等级  --身份需要和等级拆开
	EffectiveAt   time.Time                `json:"effectiveAt" gorm:"column:effective_at"`     // 生效时间
	ExpireAt      time.Time                `json:"expireAt" gorm:"column:expire_at"`           // 失效时间
	AlwaysShow    bool                     `json:"alwaysShow" gorm:"column:always_show"`       // 长期展示
	JumpUrl       v1.UserGrowthJumpAddress `json:"jumpUrl" gorm:"column:jump_url"`             // 跳转地址
	Status        int                      `json:"status" gorm:"column:status"`                // 0:下架中 1:上架中
	Desc          string                   `json:"desc" gorm:"column:desc"`                    // 备注
	Creator       string                   `json:"creator" gorm:"creator"`                     // 创建人
	CreateTime    time.Time                `json:"createTime" gorm:"column:create_time"`       // 创建时间
	Updator       string                   `json:"updator" gorm:"column:updator"`              // 更新人
	UpdateTime    time.Time                `json:"updateTime" gorm:"column:update_time"`       // 更新时间
	ImgsBanner    string                   `json:"ImgsBanner" gorm:"column:ImgsBanner"`        // 轮播banner
}

func (Banner) TableName() string {
	return BannerTableName
}
